app:
  name: dcOam

server:
  port: 7000


logging:
  level:
    com.netflix.discovery: 'OFF'
    org.springframework.cloud: 'DEBUG'
    org.springframework: 'INFO'



management:
  context-path: /admin
  security:
    enabled: false

security:
  basic:
    enabled: false


spring:
  security:
    enabled: false
  cloud:
    config:
#      label: dev
#      enabled: false
      server:
        git:
          uri: ssh://<EMAIL>:29418/topower-conf-test.git
#          searchPaths: dev
#          basedir: target/config
    bus:
      trace:
        enable: true
  rabbitmq:
    host: **************
    port: 5672
    username: topower
    password: topower123
    virtual-host: topower
    virtualHost: topower
          
