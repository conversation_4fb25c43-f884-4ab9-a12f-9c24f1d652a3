package com.cdz360.cloud.oam;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.config.server.EnableConfigServer;
import org.springframework.context.annotation.Configuration;

@Configuration
@EnableAutoConfiguration
@EnableDiscoveryClient(autoRegister=false)
@EnableConfigServer
public class DcOamMain {

    public static void main(String[] args) {
        SpringApplication.run(DcOamMain.class, args);
    }
}
