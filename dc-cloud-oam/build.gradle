//plugins {
//    id 'java'
//}





dependencies {

    implementation('org.springframework.cloud:spring-cloud-config-server')
//    implementation('org.springframework.cloud:spring-cloud-bus')
//    implementation 'org.springframework.boot:spring-boot-starter-amqp'
//    implementation 'org.springframework.cloud:spring-cloud-stream-binder-rabbit'
//    implementation('org.springframework.cloud:spring-cloud-starter-bus-amqp')

}
