package com.cdz360.cloud.zipkin;



import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import zipkin2.server.internal.EnableZipkinServer;

@SpringBootApplication
@EnableZipkinServer
public class DcZipkinMain {

    public static void main(String[] args) {
        new SpringApplicationBuilder(DcZipkinMain.class).web(WebApplicationType.SERVLET).run(args);
    }
}
