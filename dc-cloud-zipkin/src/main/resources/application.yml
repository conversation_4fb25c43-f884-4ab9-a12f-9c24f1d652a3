app:
  name: <PERSON><PERSON><PERSON><PERSON><PERSON>

server:
  address: 0.0.0.0
  port: 7010
  use-forward-headers: true
  compression.enabled: true


management:
  context-path: /admin
  security:
    enabled: false



logging:
  level:
    org.springframework: 'INFO'
    feign: 'DEBUG'


eureka:
  instance:
    hostname: localhost
  client:
    registerWithEureka: false
    fetchRegistry: true
    healthcheck:
      enabled: true
    serviceUrl:
      defaultZone: https://aaa:<EMAIL>:7001/eureka/
