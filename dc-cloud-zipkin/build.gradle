

ext {
    ZIPKIN_VERSION = '2.12.9'
}

//configurations.all {
//    //exclude module: 'log4j-slf4j-impl'
//    // exclude module: 'logback-classic'
//    exclude module: 'spring-boot-starter-logging'
//}

dependencies {


    implementation('org.springframework.boot:spring-boot-starter-actuator')
//    implementation('org.springframework.boot:spring-boot-starter-log4j2')
    implementation('org.springframework.cloud:spring-cloud-starter-config')
    implementation('org.springframework.cloud:spring-cloud-starter-netflix-eureka-client')


    implementation("io.zipkin.java:zipkin-server:${ZIPKIN_VERSION}")

    implementation("io.zipkin.java:zipkin-autoconfigure-ui:${ZIPKIN_VERSION}")



}
