package com.cdz360.data.cache;


import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;

import com.cdz360.base.model.bi.vo.SamplingMinuteDataVo;
import com.cdz360.base.model.es.vo.BmsRtData;
import com.cdz360.base.model.es.vo.BmsRtInfo;
import com.cdz360.base.model.es.vo.DehRtData;
import com.cdz360.base.model.es.vo.DehRtInfo;
import com.cdz360.base.model.es.vo.EmuRtData;
import com.cdz360.base.model.es.vo.EmuRtInfo;
import com.cdz360.base.model.es.vo.FfsRtData;
import com.cdz360.base.model.es.vo.FfsRtInfo;
import com.cdz360.base.model.es.vo.LiquidRtData;
import com.cdz360.base.model.es.vo.LiquidRtInfo;
import com.cdz360.base.model.es.vo.MeterRtData;
import com.cdz360.base.model.es.vo.MeterRtInfo;
import com.cdz360.base.model.es.vo.PcsRtData;
import com.cdz360.base.model.es.vo.PcsRtInfo;
import com.cdz360.base.model.es.vo.SignalVal;
import com.cdz360.base.model.es.vo.UpsRtData;
import com.cdz360.base.model.es.vo.UpsRtInfo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DateUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.data.cache.utils.RedisKeyGenerator;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.TreeSet;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class RedisEmuRwService extends RedisEmuRoService {

    @Autowired
    protected StringRedisTemplate redisTemplate;



    public void updateEmuRtInfo(String emuDno, EmuRtInfo emuRtInfo) {
        String key = RedisKeyGenerator.genEmuRtInfoKey(emuDno);
        String json = JsonUtils.toJsonString(emuRtInfo);
        log.debug("更新 EMU 信息到 redis. key = {}, emuRtInfo = {}", key, json);
        redisTemplate.opsForValue().set(key, json);
    }

    public void updateEmuRtData(String emuDno, EmuRtData emuRtData) {
        String key = RedisKeyGenerator.genEmuRtDataKey(emuDno);
        String json = JsonUtils.toJsonString(emuRtData);
        log.debug("更新 EMU 数据到 redis. key = {}, emuRtData = {}", key, json);
        redisTemplate.opsForValue().set(key, json);
    }


    public void updatePcsRtInfo(String pcsDno, PcsRtInfo pcsRtInfo) {
        String key = RedisKeyGenerator.genPcsRtInfoKey(pcsDno);
        String json = JsonUtils.toJsonString(pcsRtInfo);
        log.debug("更新 PCS 信息到 redis. key = {}, pcsRtInfo = {}", key, json);
        redisTemplate.opsForValue().set(key, json);
    }


    public void updatePcsRtData(String pcsDno, PcsRtData pcsRtData) {
        String key = RedisKeyGenerator.genPcsRtDataKey(pcsDno);
        String json = JsonUtils.toJsonString(pcsRtData);
        log.debug("更新 PCS 数据到 redis. key = {}, pcsRtData = {}", key, json);
        redisTemplate.opsForValue().set(key, json);
    }

    public void updateBmsRtInfo(String bmsDno, BmsRtInfo bmsRtInfo) {
        String key = RedisKeyGenerator.genBmsRtInfoKey(bmsDno);
        String json = JsonUtils.toJsonString(bmsRtInfo);
        log.debug("更新 BMS 信息到 redis. key = {}, bmsRtInfo = {}", key, json);
        redisTemplate.opsForValue().set(key, json);
    }


    public void updateBmsRtData(String bmsDno, BmsRtData bmsRtData) {
        String key = RedisKeyGenerator.genBmsRtDataKey(bmsDno);
        String json = JsonUtils.toJsonString(bmsRtData);
        if (json.length() > 300) {  // 避免日志太大
            log.debug("更新 BMS 数据到 redis. key = {}, bmsRtData = {} ....", key,
                json.substring(0, 300));
        } else {
            log.debug("更新 BMS 数据到 redis. key = {}, bmsRtData = {}", key, json);
        }
        redisTemplate.opsForValue().set(key, json);
    }

    /**
     * 更新 液冷 实时信息
     */
    public void updateLiquidRtInfo(String liquidDno, LiquidRtInfo liquidRtInfo) {
        String key = RedisKeyGenerator.genLiquidRtInfoKey(liquidDno);
        String json = JsonUtils.toJsonString(liquidRtInfo);
        log.debug("更新 液冷 信息到 redis. key = {}, liquidRtInfo = {}", key, json);
        redisTemplate.opsForValue().set(key, json);
    }

    /**
     * 更新 液冷 实时数据
     */
    public void updateLiquidRtData(String liquidDno, LiquidRtData liquidRtData) {
        String key = RedisKeyGenerator.genLiquidRtDataKey(liquidDno);
        String json = JsonUtils.toJsonString(liquidRtData);
        log.debug("更新 液冷 数据到 redis. key = {}, liquidRtData = {}", key, json);
        redisTemplate.opsForValue().set(key, json);
    }

    /**
     * 更新 除湿器 实时信息
     */
    public void updateDehRtInfo(String dehDno, DehRtInfo dehRtInfo) {
        String key = RedisKeyGenerator.genDehRtInfoKey(dehDno);
        String json = JsonUtils.toJsonString(dehRtInfo);
        log.debug("更新 除湿器 信息到 redis. key = {}, dehRtInfo = {}", key, json);
        redisTemplate.opsForValue().set(key, json);
    }
//    public void updateDiRtInfo(String emuDno, List<SignalVal> diVals) {
//        if (CollectionUtils.isEmpty(diVals)) {
//            return;
//        }
//        String key = RedisKeyGenerator.genEmuRtDiDataKey(emuDno);
//        String json = JsonUtils.toJsonString(diVals);
//        log.debug("更新 EMU DI信息到 redis. key = {}, diVals = {}", key, json);
//        redisTemplate.opsForValue().set(key, json);
//    }

    /**
     * 更新 除湿器 实时数据
     */
    public void updateDehRtData(String dehDno, DehRtData dehRtData) {
        String key = RedisKeyGenerator.genDehRtDataKey(dehDno);
        String json = JsonUtils.toJsonString(dehRtData);
        log.debug("更新 除湿器 数据到 redis. key = {}, dehRtData = {}", key, json);
        redisTemplate.opsForValue().set(key, json);
    }

    /**
     * 更新 消防系统 实时信息
     */
    public void updateFfsRtInfo(String ffsDno, FfsRtInfo ffsRtInfo) {
        String key = RedisKeyGenerator.genFfsRtInfoKey(ffsDno);
        String json = JsonUtils.toJsonString(ffsRtInfo);
        log.debug("更新 消防系统 信息到 redis. key = {}, ffsRtInfo = {}", key, json);
        redisTemplate.opsForValue().set(key, json);
    }

    /**
     * 更新 UPS 实时信息
     */
    public void updateUpsRtInfo(String upsDno, UpsRtInfo upsRtInfo) {
        String key = RedisKeyGenerator.genUpsRtInfoKey(upsDno);
        String json = JsonUtils.toJsonString(upsRtInfo);
        log.debug("更新 UPS 信息到 redis. key = {}, upsRtInfo = {}", key, json);
        redisTemplate.opsForValue().set(key, json);
    }


    /**
     * 更新 UPS 实时数据
     */
    public void updateUpsRtData(String upsDno, UpsRtData upsRtData) {
        String key = RedisKeyGenerator.genUpsRtDataKey(upsDno);
        String json = JsonUtils.toJsonString(upsRtData);
        log.debug("更新 UPS 数据到 redis. key = {}, upsRtData = {}", key, json);
        redisTemplate.opsForValue().set(key, json);
    }

    /**
     * 更新 电表 实时信息
     */
    public void updateMeterRtInfo(String meterDno, MeterRtInfo meterRtInfo) {
        String key = RedisKeyGenerator.genMeterRtInfoKey(meterDno);
        String json = JsonUtils.toJsonString(meterRtInfo);
        log.debug("更新 电表 信息到 redis. key = {}, meterRtInfo = {}", key, json);
        redisTemplate.opsForValue().set(key, json);
    }

    /**
     * 更新 电表 实时数据
     */
    public void updateMeterRtData(String meterDno, MeterRtData meterRtData) {
        String key = RedisKeyGenerator.genMeterRtDataKey(meterDno);
        String json = JsonUtils.toJsonString(meterRtData);
        log.debug("更新电表数据到 redis. key = {}, meterRtData = {}", key, json);
        redisTemplate.opsForValue().set(key, json);
    }

    /**
     * 更新消防系统实时数据
     */
    public void updateFfsRtData(String ffsDno, FfsRtData ffsRtData) {
        String key = RedisKeyGenerator.genFfsRtInfoKey(ffsDno);
        String json = JsonUtils.toJsonString(ffsRtData);
        log.debug("更新消防系统数据到 redis. key = {}, ffsRtData = {}", key, json);
        redisTemplate.opsForValue().set(key, json);
    }

    /**
     * 添加采样数据到BMS的 日内 数据曲线，data.v1=SOC
     * <p>
     * 有效期72H
     */
    public void appendBmsInDaySamplingData(String bmsDno, LocalDate date,
        SamplingMinuteDataVo data) {
        String key = RedisKeyGenerator.genBmsInDaySamplingDataKey(bmsDno, date);
        this.appendInDaySamplingData(key, date, data);
    }

    /**
     * 更新采样数据到BMS的 日内 数据曲线, 如果缓存中已有，则全部替换，data.v1=SOC
     * <p>
     * 有效期72H
     */
    public void setBmsInDaySamplingData(String bmsDno, LocalDate date,
        List<SamplingMinuteDataVo> dataList) {
        String key = RedisKeyGenerator.genBmsInDaySamplingDataKey(bmsDno, date);
        this.setInDaySamplingData(key, date, dataList);
    }

    /**
     * 添加采样数据到PCS的 日内 数据曲线，data. v1=交流侧功率, v2=交流侧电压, v3=交流侧电流
     * <p>
     * 有效期72H
     */
    public void appendPcsInDaySamplingData(String pcsDno, LocalDate date,
        SamplingMinuteDataVo data) {
        String key = RedisKeyGenerator.genPcsInDaySamplingDataKey(pcsDno, date);
        this.appendInDaySamplingData(key, date, data);
    }

    /**
     * 添加采样数据到关口电表的 日内 数据曲线，data. v1=有功功率, v2=无功功率, v3=功率因数
     * <p>
     * 有效期72H
     */
    public void appendMeterInDaySamplingData(String meterDno, LocalDate date,
        SamplingMinuteDataVo data) {
        String key = RedisKeyGenerator.genMeterInDaySamplingDataKey(meterDno, date);
        this.appendInDaySamplingData(key, date, data);
    }


    /**
     * 更新采样数据到PCS的 日内 数据曲线, 如果缓存中已有，则全部替换，data.v1=交流侧功率, v2=交流侧电压, v3=交流侧电流
     * <p>
     * 有效期72H
     */
    public void setPcsInDaySamplingData(String pcsDno, LocalDate date,
        List<SamplingMinuteDataVo> dataList) {
        String key = RedisKeyGenerator.genPcsInDaySamplingDataKey(pcsDno, date);
        this.setInDaySamplingData(key, date, dataList);
    }

    private void appendInDaySamplingData(String key, LocalDate date,
        SamplingMinuteDataVo data) {
        String lastDataStr = redisTemplate.opsForList().rightPop(key);
        if (StringUtils.isNotBlank(lastDataStr)) {
            SamplingMinuteDataVo lastData = JsonUtils.fromJson(lastDataStr,
                SamplingMinuteDataVo.class);
            if (lastData.getMinute() != data.getMinute()) {
                redisTemplate.opsForList().rightPush(key, lastDataStr);
            }
        }
        redisTemplate.opsForList().rightPush(key, JsonUtils.toJsonString(data));
        redisTemplate.expire(key, Duration.ofHours(72L));
    }

    private void setInDaySamplingData(String key, LocalDate date,
        List<SamplingMinuteDataVo> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            log.warn("dataList 为空,不更新redis缓存");
            return;
        }
        List<String> dataListX = dataList.stream()
            .collect(
                collectingAndThen(
                    toCollection(
                        () -> new TreeSet<>(Comparator.comparing(SamplingMinuteDataVo::getMinute))),
                    ArrayList::new) // 去重
            ).stream()
            .sorted()
            .map(d -> JsonUtils.toJsonString(d))
            .collect(Collectors.toList());

        redisTemplate.opsForList()
            .rightPushAll(key, dataListX);
        redisTemplate.expire(key, Duration.ofHours(72L));
    }
}
