package com.cdz360.data.cache;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.cus.vo.CusInfoVo;
import com.cdz360.base.model.cus.vo.LoginSaltVo;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.SerializeUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.data.cache.utils.RedisCacheUtils;
import com.cdz360.data.cache.utils.RedisKeyGenerator;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class RedisCusRwService extends RedisCusReadService {


    public void updateLoginSalt(@NonNull LoginSaltVo loginSalt) {
        String key = RedisKeyGenerator.genLoginSaltKey(loginSalt.getTopCommId(),
            loginSalt.getUsername());
        Map<String, String> map = SerializeUtils.objectToMap(loginSalt);
        log.info("更新账号登录的 salt 到redis. key = {}, loginSalt = {}", key, map);
        this.redisTemplate.opsForHash().putAll(key, map);
        this.redisTemplate.expire(key, CacheConstants.REDIS_LOGIN_SALT_TTL, TimeUnit.SECONDS);
    }

    public void updateUserInfo(@NonNull CusInfoVo cusInfo) {
        if (cusInfo.getCusId() < 1L) {
            log.error("invalid cusId ... cusInfo = {}", cusInfo);
            throw new DcArgumentException("客户ID错误");
        }
        String key = RedisKeyGenerator.genUserInfoKey(cusInfo.getTopCommId(), cusInfo.getCusId());
        CusInfoVo vo = super.getUserInfoCache(cusInfo.getTopCommId(), cusInfo.getCusId());
        Map<String, String> map = new HashMap<>();
        boolean changed = false;
        if (vo == null) {
            vo = cusInfo;
            vo.setClientIds(CusInfoVo.toStringClientIds(vo));
            changed = true;
            map = SerializeUtils.objectToMap(vo);
        } else {
            changed = RedisCacheUtils.updateProperty(cusInfo, vo, map, "token");
            changed = RedisCacheUtils.updateProperty(cusInfo, vo, map, "phone") || changed;
            changed = RedisCacheUtils.updateProperty(cusInfo, vo, map, "name") || changed;
            changed = RedisCacheUtils.updateProperty(cusInfo, vo, map, "username") || changed;

            if (equalsClientIds(cusInfo, vo)) {
                // 客户端类型有变更
                map.put("clientIds", CusInfoVo.toStringClientIds(cusInfo));
                changed = true;
            }
        }
        if (changed) {
            log.info("更新客户信息到redis. key = {}, cusInfo = {}", key, map);
            this.redisTemplate.opsForHash().putAll(key, map);
            this.redisTemplate.expire(key, CacheConstants.REDIS_CUS_INFO_TTL_DAYS, TimeUnit.DAYS);
        }
    }


    private boolean equalsClientIds(CusInfoVo right, CusInfoVo left) {
        String strRight = CusInfoVo.toStringClientIds(right);
        String strLeft = CusInfoVo.toStringClientIds(left);
        return StringUtils.equalsIgnoreCase(strRight, strLeft);
    }

    public void deleteUserInfoCache(long topCommId, long cusId) {
        String key = RedisKeyGenerator.genUserInfoKey(topCommId, cusId);
        log.info("删除缓存的客户信息: cusId = {}, key = {}", cusId, key);
        this.redisTemplate.delete(key);
    }

    /**
     * 未来逐步废弃，使用 updateUserInfo 替换
     */
    public void updateCusInfo(@NonNull AppClientType appType, @NonNull CusInfoVo cusInfo) {
        if (cusInfo.getCusId() < 1L) {
            log.error("invalid cusId ... cusInfo = {}", cusInfo);
            throw new DcArgumentException("客户ID错误");
        }
        String key = RedisKeyGenerator.genCustomerKey(appType, cusInfo.getCusId());
        CusInfoVo vo = super.getCustomerInfoCache(appType, cusInfo.getCusId());
        Map<String, String> map = new HashMap<>();
        boolean changed = false;
        if (vo == null) {
            vo = cusInfo;
            changed = true;
            map = SerializeUtils.objectToMap(vo);
        } else {
            changed = RedisCacheUtils.updateProperty(cusInfo, vo, map, "token");
            changed = RedisCacheUtils.updateProperty(cusInfo, vo, map, "phone") || changed;
            changed = RedisCacheUtils.updateProperty(cusInfo, vo, map, "name") || changed;

            if (!NumberUtils.equals(cusInfo.getCommId(), vo.getCommId())) {
                // 集团商户ID
                map.put("commId", String.valueOf(cusInfo.getCommId()));
                changed = true;
            }
        }
        if (changed) {
            log.info("更新客户信息到redis. key = {}, cusInfo = {}", key, map);
            this.redisTemplate.opsForHash().putAll(key, map);
            this.redisTemplate.expire(key, CacheConstants.REDIS_CUS_INFO_TTL_DAYS, TimeUnit.DAYS);
        }
    }

    /**
     * 未来逐步废弃，使用 deleteUserInfoCache 替换
     */
    public void deleteCusInfoCache(AppClientType appType, long cusId) {
        String key = RedisKeyGenerator.genCustomerKey(appType, cusId);
        log.info("删除缓存的客户信息: cusId = {}, key = {}", cusId, key);
        this.redisTemplate.delete(key);
    }
}
