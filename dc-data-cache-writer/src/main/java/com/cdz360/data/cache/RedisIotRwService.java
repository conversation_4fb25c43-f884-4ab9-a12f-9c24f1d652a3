package com.cdz360.data.cache;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.type.EvseStatus;
import com.cdz360.base.model.base.type.PlugStatus;
import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.model.iot.vo.PlugVo;
import com.cdz360.base.model.iot.vo.TransformerVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.PlugNoUtils;
import com.cdz360.base.utils.SerializeUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.data.cache.model.IotGwCmdCacheVo;
import com.cdz360.data.cache.utils.RedisKeyGenerator;
import java.lang.reflect.Method;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class RedisIotRwService extends RedisIotReadService {


    /**
     * 桩绑定到场站
     *
     * @param siteId     场站ID
     * @param evseNo     桩编号
     * @param plugNoList 枪头编号列表
     */
    public void bindEvse(String siteId, String evseNo, List<String> plugNoList) {
        String key = RedisKeyGenerator.genSiteEvseRedisKey(siteId);
        this.redisTemplate.execute(new RedisCallback<String>() {
            @Override
            public String doInRedis(RedisConnection connection) throws DataAccessException {
                connection.openPipeline();
                connection.sAdd(key.getBytes(), evseNo.getBytes());
                if (plugNoList != null) {
                    plugNoList.stream().forEach(plugNo -> {
                        bindPlug(connection, siteId, plugNo);
                    });
                }
                connection.closePipeline();
                return null;
            }
        });
    }

    /**
     * 桩和场站解绑
     *
     * @param siteId     场站ID
     * @param evseNo     桩编号
     * @param plugNoList 枪头编号列表
     */
    public void unbindEvse(String siteId, String evseNo, List<String> plugNoList) {
        String key = RedisKeyGenerator.genSiteEvseRedisKey(siteId);
        this.redisTemplate.opsForSet().remove(key, evseNo);
        EvseVo evseCache = getEvseRedisCache(evseNo);
        evseCache.setSiteId("").setSiteCommId(0L).setSiteName("");
        this.updateEvseRedisCache(evseCache);
        if (evseCache != null && plugNoList != null) {
            plugNoList.stream().forEach(plugNo -> {
                PlugVo plugCache = new PlugVo();
                // 清空枪头缓存里的场站和商户信息
                plugCache.setEvseNo(evseNo)
                    .setPlugNo(plugNo)
                    .setSiteId("")
                    .setSiteName("")
                    .setSiteCommId(0L).setSiteCommName("");
                this.updatePlugRedisCache(plugCache);
                this.unbindPlug(siteId, plugNo);
            });

        }
    }


    private void bindPlug(RedisConnection connection, String siteId, String plugNo) {
        String key = RedisKeyGenerator.genSitePlugRedisKey(siteId);
        //String plugNo = RedisKeyGenerator.genPlugNo(evseNo, idx);
        connection.sAdd(key.getBytes(), plugNo.getBytes());
        //this.redisTemplate.opsForSet().add(key, plugNo);
    }

    public void unbindPlug(String siteId, String plugNo) {
        String key = RedisKeyGenerator.genSitePlugRedisKey(siteId);
        //String plugNo = RedisKeyGenerator.genPlugNo(evseNo, idx);
        this.redisTemplate.opsForSet().remove(key, plugNo);
    }

    /**
     * 更新变压器信息到redis
     *
     * @param tfm 变压器信息
     */
    public void updateTransformerRedisCache(@NonNull TransformerVo tfm) {
        if (tfm.getId() == null) {
            log.error("变压器ID不能为空 tfm = {}", tfm);
            throw new DcArgumentException("变压器ID不能为空");
        }
        String key = RedisKeyGenerator.genTransformerRedisKey(tfm.getId());
        TransformerVo vo = super.getTransformerRedisCache(tfm.getId());
        Map<String, String> map = new HashMap<>();
        boolean changed = false;
        if (vo == null) {
            vo = tfm;
            changed = true;
            vo.setUpdateTime(new Date());
            map = SerializeUtils.objectToMap(vo);
        } else {
            changed = this.updateProperty(tfm, vo, map, "siteId");
            changed = this.updateIntegerProperty(tfm, vo, map, "plugNum") || changed;
            changed = this.updateIntegerProperty(tfm, vo, map, "actualPower") || changed;
            changed = this.updateIntegerProperty(tfm, vo, map, "assignPower") || changed;

            if (changed) {
                // vo.setUpdateTime(new Date());
                map.put("updateTime", String.valueOf(new Date().getTime()));
            }
        }
        if (changed) {
            log.info("更新变压器信息到redis. key = {}, tfm = {}", key, map);
            this.redisTemplate.opsForHash().putAll(key, map);
            this.redisTemplate.expire(key, CacheConstants.REDIS_EVSE_TTL_MINUTES, TimeUnit.MINUTES);
        }
    }

    /**
     * 更新桩信息到redis
     *
     * @param evse 桩信息
     */
    public void updateEvseRedisCache(@NonNull EvseVo evse) {
        if (StringUtils.isBlank(evse.getEvseNo())) {
            log.error("evseNo is empty.... evse = {}", evse);
            throw new DcArgumentException("桩编号不能为空");
        }
        String key = RedisKeyGenerator.genEvseRedisKey(evse.getEvseNo());
        EvseVo vo = super.getEvseRedisCache(evse.getEvseNo());
        Map<String, String> map = new HashMap<>();
        boolean changed = false;
        if (vo == null) {
            vo = evse;
            changed = true;
            vo.setUpdateTime(new Date());
            if (vo.getStatus() == null) {
                vo.setStatus(EvseStatus.UNKNOWN);
            }
            map = SerializeUtils.objectToMap(vo);
        } else {
            changed = this.updateProperty(evse, vo, map, "siteId");
            changed = this.updateProperty(evse, vo, map, "gwno") || changed;
            changed = this.updateProperty(evse, vo, map, "name") || changed;
            changed = this.updateProperty(evse, vo, map, "firmwareVer") || changed;
            changed = this.updateProperty(evse, vo, map, "pc01Ver") || changed;
            changed = this.updateProperty(evse, vo, map, "pc02Ver") || changed;
            changed = this.updateProperty(evse, vo, map, "modelName") || changed;
            changed = this.updateBooleanProperty(evse, vo, map, "connSupport") || changed;
            changed = this.updateIntegerProperty(evse, vo, map, "actualPower") || changed;
            changed = this.updateIntegerProperty(evse, vo, map, "assignPower") || changed;

            if (evse.getSiteCommId() != null && !NumberUtils.equals(evse.getSiteCommId(),
                vo.getSiteCommId())) {
                // 场站的商户ID
                map.put("siteCommId", String.valueOf(evse.getSiteCommId()));
                changed = true;
            }
            if (evse.getStatus() != null && evse.getStatus() != vo.getStatus()) {
                //vo.setStatus(evse.getStatus());
                map.put("status", evse.getStatus().name());
                changed = true;
            }
            changed = this.updateLongProperty(evse, vo, map, "priceCode") || changed;    // 价格模板ID

            if (evse.getPower() != null && !NumberUtils.equals(evse.getPower(), vo.getPower())) {
                // 功率
                map.put("power", String.valueOf(evse.getPower()));
                changed = true;
            }
            if (evse.getSupplyType() != null && evse.getSupplyType() != vo.getSupplyType()) {
                map.put("supplyType", evse.getSupplyType().name());
                changed = true;
            }
            if (evse.getBizStatus() != null && evse.getBizStatus() != vo.getBizStatus()) {
                vo.setBizStatus(evse.getBizStatus());
                map.put("bizStatus", evse.getBizStatus().name());
                changed = true;
            }

            if (evse.getPlugNum() != null && !evse.getPlugNum().equals(vo.getPlugNum())) {
                map.put("plugNum", String.valueOf(evse.getPlugNum()));
                changed = true;
            }
            if (evse.getProtocol() != null && !evse.getProtocol().equals(vo.getProtocol())) {
                map.put("protocol", evse.getProtocol().name());
                changed = true;
            }
            if (evse.getProtocolVer() != null && !NumberUtils.equals(evse.getProtocolVer(),
                vo.getProtocolVer())) {
                map.put("protocolVer", String.valueOf(evse.getProtocolVer()));
                changed = true;
            }
            if (evse.getVoltage() != null && !NumberUtils.equals(evse.getVoltage(),
                vo.getVoltage())) {
                map.put("voltage", String.valueOf(evse.getVoltage()));
                changed = true;
            }
            if (evse.getCurrent() != null && !NumberUtils.equals(evse.getCurrent(),
                vo.getCurrent())) {
                map.put("current", String.valueOf(evse.getCurrent()));
                changed = true;
            }
            if (changed) {
                // vo.setUpdateTime(new Date());
                map.put("updateTime", String.valueOf(new Date().getTime()));
            }
        }
        if (changed) {
            log.info("更新桩信息到redis. key = {}, evse = {}", key, map);
//            this.redisTemplate.opsForValue().set(key, JsonUtils.toJsonString(vo),
//                    CacheConstants.REDIS_EVSE_TTL_MINUTES, TimeUnit.MINUTES);
            this.redisTemplate.opsForHash().putAll(key, map);
            this.redisTemplate.expire(key, CacheConstants.REDIS_EVSE_TTL_MINUTES, TimeUnit.MINUTES);


        }
    }

    /**
     * 删除缓存在redis里的桩信息
     *
     * @param evseNo 桩编号
     */
    public void deleteEvseRedisCache(@NonNull String evseNo) {
        log.info("删除缓存的桩: {}", evseNo);
        String key = RedisKeyGenerator.genEvseRedisKey(evseNo);
        this.redisTemplate.delete(key);
    }

    /**
     * 更新枪头信息到redis
     *
     * @param plug 枪头信息. 仅更新不为null的属性到redis
     * @return 返回缓存的内容
     */
    public PlugVo updatePlugRedisCache(@NonNull PlugVo plug) {

        if (StringUtils.isBlank(plug.getEvseNo())
            && StringUtils.isBlank(plug.getPlugNo())) {
            log.error("no evseNo nor plugNo.... plug = {}", plug);
            throw new DcArgumentException("桩编号和枪编号不能同时为空");
        } else if ((plug.getIdx() == null || plug.getIdx() < 1)
            && StringUtils.isBlank(plug.getPlugNo())) {
            log.error("枪头序号错误.... plug = {}", plug);
            throw new DcArgumentException("枪头序号错误");
        }
        String key;
        String plugNo = plug.getPlugNo();
        if (StringUtils.isBlank(plugNo)) {
            plugNo = PlugNoUtils.formatPlugNo(plug.getEvseNo(), plug.getIdx());
        }

        key = RedisKeyGenerator.genPlugRedisKey(plug.getPlugNo());

        PlugVo vo = super.getPlugRedisCache(plug.getPlugNo());

        Map<String, String> map = new HashMap<>();
        boolean changed = false;
        if (vo == null) {
            vo = plug;
            vo.setTopCommId(plug.getTopCommId())
                .setPlugNo(plugNo)
                .setEvseNo(plug.getEvseNo())
                .setIdx(plug.getIdx())
                .setUpdateTime(new Date()).setAlertCode(plug.getAlertCode())
                .setErrorCode(plug.getErrorCode()).setErrorMsg(plug.getErrorMsg())
                .setSiteId(plug.getSiteId()).setGwno(plug.getGwno())
                .setSiteName(plug.getSiteName()).setSiteCommName(plug.getSiteCommName())
                .setName(plug.getName()).setEvseName(plug.getEvseName())
                .setProtocol(plug.getProtocol())
                .setLockStatus(plug.getLockStatus())
                .setLockNo(plug.getLockNo())
                .setActualPower(plug.getActualPower())
                .setAssignPower(plug.getAssignPower())
                .setPower(plug.getPower())
                .setRequirePower(plug.getRequirePower());

            if (vo.getStatus() == null) {
                vo.setStatus(PlugStatus.UNKNOWN);
            }
            if (vo.getSupply() == null) {
                vo.setSupply(SupplyType.UNKNOWN);
            }
            if (vo.getPriceCode() != null) {
                vo.setPriceCode(plug.getPriceCode());
            }
            map = SerializeUtils.objectToMap(vo);
            changed = true;
        } else {
            // 仅更新不为null的项目

            changed = this.updateProperty(plug, vo, map, "siteId");
            changed = this.updateLongProperty(plug, vo, map, "topCommId") || changed;
            changed = this.updateProperty(plug, vo, map, "gwno") || changed;
            changed = this.updateProperty(plug, vo, map, "siteName") || changed;
            changed = this.updateProperty(plug, vo, map, "siteCommName") || changed;
            changed = this.updateProperty(plug, vo, map, "name") || changed;
            changed = this.updateProperty(plug, vo, map, "evseName") || changed;
            changed = this.updateProperty(plug, vo, map, "orderNo") || changed;
            changed = this.updateBooleanProperty(plug, vo, map, "lockStatus") || changed;
            changed = this.updateProperty(plug, vo, map, "lockNo") || changed;
            changed = this.updateBooleanProperty(plug, vo, map, "connSupport") || changed;
            changed = this.updateBooleanProperty(plug, vo, map, "constantCharge") || changed;
            changed = this.updateIntegerProperty(plug, vo, map, "actualPower") || changed;
            changed = this.updateIntegerProperty(plug, vo, map, "assignPower") || changed;

//            if (plug.getSiteCommId() != null && !NumberUtils.equals(plug.getSiteCommId(), vo.getSiteCommId())) {
//                // 场站的商户ID
//                map.put("siteCommId", String.valueOf(plug.getSiteCommId()));
//                changed = true;
//            }
            changed = this.updateLongProperty(plug, vo, map, "siteCommId") || changed;
            // 如果redis缓存里的桩编号为空,做数据补全
            if (StringUtils.isBlank(vo.getEvseNo()) && StringUtils.isNotBlank(plug.getEvseNo())) {
                vo.setEvseNo(plug.getEvseNo());
                map.put("evseNo", plug.getEvseNo());
                changed = true;
            }
            if (plug.getStatus() != null && plug.getStatus() != vo.getStatus()) {
                vo.setStatus(plug.getStatus());
                map.put("status", plug.getStatus().name());
                changed = true;
            }
            if (plug.getSupply() != null && plug.getSupply() != vo.getSupply()) {
                vo.setSupply(plug.getSupply());
                map.put("supply", plug.getSupply().name());
                changed = true;
            }
            if (plug.getBizStatus() != null && plug.getBizStatus() != vo.getBizStatus()) {
                vo.setBizStatus(plug.getBizStatus());
                map.put("bizStatus", plug.getBizStatus().name());
                changed = true;
            }
            if (vo.getProtocol() == null && plug.getProtocol() != null) {
                vo.setProtocol(plug.getProtocol());
                map.put("protocol", plug.getProtocol().name());
                changed = true;
            }
            changed = this.updateLongProperty(plug, vo, map, "priceCode") || changed;
            if (plug.getErrorCode() != null && !plug.getErrorCode().equals(vo.getErrorCode())) {
                vo.setErrorCode(plug.getErrorCode());
                map.put("errorCode", plug.getErrorCode().toString());
                changed = true;
            }
            changed = this.updateProperty(plug, vo, map, "errorMsg") || changed;
            if (plug.getAlertCode() != null && !plug.getAlertCode().equals(vo.getAlertCode())) {
                vo.setAlertCode(plug.getAlertCode());
                map.put("alertCode", plug.getAlertCode().toString());
                changed = true;
            }
            if (plug.getMinVoltage() != null && !plug.getMinVoltage().equals(vo.getMinVoltage())) {
                vo.setMinVoltage(plug.getMinVoltage());
                map.put("minVoltage", plug.getMinVoltage().toPlainString());
                changed = true;
            }
            if (plug.getMaxVoltage() != null && !plug.getMaxVoltage().equals(vo.getMaxVoltage())) {
                vo.setMaxVoltage(plug.getMaxVoltage());
                map.put("maxVoltage", plug.getMaxVoltage().toPlainString());
                changed = true;
            }
            if (plug.getMinCurrent() != null && !plug.getMinCurrent().equals(vo.getMinCurrent())) {
                vo.setMinCurrent(plug.getMinCurrent());
                map.put("minCurrent", plug.getMinCurrent().toPlainString());
                changed = true;
            }
            if (plug.getMaxCurrent() != null && !plug.getMaxCurrent().equals(vo.getMaxCurrent())) {
                vo.setMaxCurrent(plug.getMaxCurrent());
                map.put("maxCurrent", plug.getMaxCurrent().toPlainString());
                changed = true;
            }
            if (plug.getPower() != null && !plug.getPower().equals(vo.getPower())) {
                vo.setPower(plug.getPower());
                map.put("power", plug.getPower().toPlainString());
                changed = true;
            }
            if (plug.getRequirePower() != null && !plug.getRequirePower()
                .equals(vo.getRequirePower())) {
                vo.setRequirePower(plug.getRequirePower());
                map.put("requirePower", plug.getRequirePower().toPlainString());
                changed = true;
            }
            if (changed) {
                vo.setUpdateTime(new Date());
                map.put("updateTime", String.valueOf(new Date().getTime()));
            }
        }
        if (changed) {
            log.info("更新枪头信息到redis. key = {}, plug = {}", key, map);
//            this.redisTemplate.opsForValue().set(key, JsonUtils.toJsonString(vo),
//                    CacheConstants.REDIS_PLUG_TTL_MINUTES, TimeUnit.MINUTES);

            //this.redisTemplate.opsForHash().putAll(key, map);
            this.redisTemplate.opsForHash().putAll(key, map);
            this.redisTemplate.expire(key, CacheConstants.REDIS_PLUG_TTL_MINUTES, TimeUnit.MINUTES);
            //ops.putAll();
        }
        return vo;
    }

    /**
     * 批量更新枪头状态, 用于反向互联互通场景
     *
     * @param plugNoSuffix 枪头后缀
     * @param status       修改后的枪头状态
     */
    public void updatePlugsStatus(String plugNoSuffix, PlugStatus status) {
        UpdatePlugsStatusTask task = new UpdatePlugsStatusTask(plugNoSuffix, status);
        redisTemplate.execute(task);
    }


    /**
     * @param evseNo 桩编号
     * @param idx    枪头序号
     */
    public void deletePlugRedisCache(@NonNull String evseNo, int idx) {
        String key = RedisKeyGenerator.genPlugRedisKey(evseNo, idx);
        log.info("删除缓存的枪: evseNo = {}, idx = {}, 枪头编号 = {}", evseNo, idx, key);
        this.redisTemplate.delete(key);
    }

    public void deletePlugRedisCache(@NonNull String plugNo) {
        String key = RedisKeyGenerator.genPlugRedisKey(plugNo);
        log.info("删除缓存的枪: plugNo = {}", plugNo);
        this.redisTemplate.delete(key);
    }

    /**
     * 将发给iot网关的下行指令缓存到redis
     *
     * @param gwCmd 网关指令
     */
    public void addIotGwCmd(IotGwCmdCacheVo gwCmd) {
        String cmdsKey = RedisKeyGenerator.genIotGwCmdsKey(gwCmd.getGwno());
        this.redisTemplate.opsForHash()
            .put(cmdsKey, gwCmd.getSeq(), String.valueOf(new Date().getTime()));
        this.redisTemplate.expire(cmdsKey, 60, TimeUnit.MINUTES);
        String cmdKey = RedisKeyGenerator.genIotGwCmdKye(gwCmd.getGwno(), gwCmd.getSeq());
        this.redisTemplate.opsForValue()
            .set(cmdKey, JsonUtils.toJsonString(gwCmd), 10, TimeUnit.MINUTES);
    }


    /**
     * 收到网关的上行确认后, 将redis里缓存的下行指令删除
     *
     * @param gwno 网关编号
     * @param seq  指令的序列号
     */
    public void deleteIotGwCmd(String gwno, String seq) {
        String cmdsKey = RedisKeyGenerator.genIotGwCmdsKey(gwno);
        this.redisTemplate.opsForHash().delete(cmdsKey, seq);
        String cmdKey = RedisKeyGenerator.genIotGwCmdKye(gwno, seq);
        this.redisTemplate.delete(cmdKey);
    }


    /**
     * 修改桩/枪缓存的价格模板ID
     *
     * @param evseNo     桩编号
     * @param plugNoList 枪头编号列表
     * @param priceCode  价格模板ID
     */
    public void updatePriceCode(String evseNo, List<String> plugNoList, Long priceCode) {
        log.info("修改桩/枪缓存的价格模板ID. evseNo = {}, plugNoList = {}, priceCode = {}",
            evseNo, plugNoList, priceCode);
        UpdatePriceCodeTask task = new UpdatePriceCodeTask(evseNo, plugNoList, priceCode);
        redisTemplate.execute(task);
    }

    private boolean updateStringProperty(Object src, Object dest, String name) {
        try {

            String nameX = name.substring(0, 1).toUpperCase() + name.substring(1);
            Method getMethod = src.getClass().getMethod("get" + nameX);
            Method setMethod = src.getClass().getMethod("set" + nameX, String.class);
            String newValue = (String) getMethod.invoke(src);
            String oldValue = (String) getMethod.invoke(dest);
            if (newValue != null && !newValue.equals(oldValue)) {
                setMethod.invoke(dest, newValue);
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }

    private boolean updateProperty(Object src, Object dest, Map<String, String> map, String name) {
        try {

            String nameX = name.substring(0, 1).toUpperCase() + name.substring(1);
            Method getMethod = src.getClass().getMethod("get" + nameX);
            Method setMethod = src.getClass().getMethod("set" + nameX, String.class);
            String newValue = (String) getMethod.invoke(src);
            String oldValue = (String) getMethod.invoke(dest);
            if (newValue != null && !newValue.equals(oldValue)) {
                setMethod.invoke(dest, newValue);
                map.put(name, newValue);
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }

    private boolean updateLongProperty(Object src, Object dest, Map<String, String> map,
        String name) {
        try {

            String nameX = name.substring(0, 1).toUpperCase() + name.substring(1);
            Method getMethod = src.getClass().getMethod("get" + nameX);
            Method setMethod = src.getClass().getMethod("set" + nameX, Long.class);
            Long newValue = (Long) getMethod.invoke(src);
            Long oldValue = (Long) getMethod.invoke(dest);
            if (newValue != null && !newValue.equals(oldValue)) {
                setMethod.invoke(dest, newValue);
                map.put(name, String.valueOf(newValue));
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }

    private boolean updateIntegerProperty(Object src, Object dest, Map<String, String> map,
        String name) {
        try {
            String nameX = name.substring(0, 1).toUpperCase() + name.substring(1);
            Method getMethod = src.getClass().getMethod("get" + nameX);
            Method setMethod = src.getClass().getMethod("set" + nameX, Integer.class);
            Integer newValue = (Integer) getMethod.invoke(src);
            Integer oldValue = (Integer) getMethod.invoke(dest);
            if (newValue != null && !newValue.equals(oldValue)) {
                setMethod.invoke(dest, newValue);
                map.put(name, String.valueOf(newValue));
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }

    private boolean updateBooleanProperty(Object src, Object dest, Map<String, String> map,
        String name) {
        try {

            String nameX = name.substring(0, 1).toUpperCase() + name.substring(1);
            Method getMethod = src.getClass().getMethod("get" + nameX);
            Method setMethod = src.getClass().getMethod("set" + nameX, Boolean.class);
            Boolean newValue = (Boolean) getMethod.invoke(src);
            Boolean oldValue = (Boolean) getMethod.invoke(dest);
            if (newValue != null && !newValue.equals(oldValue)) {
                setMethod.invoke(dest, newValue);
                map.put(name, String.valueOf(newValue));
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }

    private static class UpdatePriceCodeTask implements RedisCallback<Boolean> {

        private String evseNo;
        private List<String> plugNoList;
        private byte[] priceCode;

        public UpdatePriceCodeTask(String evseNo, List<String> plugNoList, Long priceCode) {
            this.evseNo = evseNo;
            this.plugNoList = plugNoList;
            // this.priceCode = ByteBuffer.allocate(8).putLong(priceCode).array();
            this.priceCode = String.valueOf(priceCode).getBytes();
        }

        @Override
        public Boolean doInRedis(RedisConnection conn) throws DataAccessException {
            String evseKey = RedisKeyGenerator.genEvseRedisKey(evseNo);
            conn.hSet(evseKey.getBytes(), "priceCode".getBytes(), priceCode);
            if (CollectionUtils.isEmpty(plugNoList)) {
                log.info("plugNoList is empty....");
                return true;
            }
            for (String plugNo : this.plugNoList) {
                String plugKey = RedisKeyGenerator.genPlugRedisKey(plugNo);
                conn.hSet(plugKey.getBytes(), "priceCode".getBytes(), priceCode);
            }
            return true;
        }
    }


    private static class UpdatePlugsStatusTask implements RedisCallback<Boolean> {

        private String plugNoSuffix;
        private PlugStatus status;
        private byte[] priceCode;

        public UpdatePlugsStatusTask(String plugNoSuffix, PlugStatus status) {
            this.plugNoSuffix = plugNoSuffix;
            this.status = status;
        }

        @Override
        public Boolean doInRedis(RedisConnection conn) throws DataAccessException {
            String sk = RedisKeyGenerator.genPlugSearchKey(this.plugNoSuffix);
            Set<byte[]> keys = conn.keys(sk.getBytes());
            if (CollectionUtils.isEmpty(keys)) {
                return true;
            }
            log.info("更新枪头状态, keys = {}, status = {}",
                keys.stream().map(k -> new String(k)).collect(Collectors.toList()), status);
            for (byte[] key : keys) {
                conn.hSet(key, "status".getBytes(), status.name().getBytes());
            }
            return true;
        }
    }
}
