package com.cdz360.data.cache;

import com.cdz360.base.model.app.vo.AppCfg;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.data.cache.utils.RedisKeyGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class RedisAppRwService extends RedisAppReadService {

    public void updateAppCfg(AppCfg appCfg) {
        String key = RedisKeyGenerator.genAppCfgKey();
        super.redisTemplate.opsForHash().put(key, appCfg.getAppId(), JsonUtils.toJsonString(appCfg));
    }
}
