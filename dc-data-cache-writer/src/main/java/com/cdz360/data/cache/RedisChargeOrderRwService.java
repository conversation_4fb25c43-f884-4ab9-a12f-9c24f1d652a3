package com.cdz360.data.cache;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.data.cache.utils.RedisKeyGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.BoundListOperations;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RedisChargeOrderRwService extends RedisChargeOrderReadService {

    public void setOrderStopCodeString(String orderNo, String code) {

        if (StringUtils.isBlank(orderNo)) {
            throw new DcArgumentException("参数错误, 订单号不能为空");
        }
        if (StringUtils.isBlank(code)) {
            throw new DcArgumentException("参数错误, 停充码不能为空");
        }
        String key = RedisKeyGenerator.genChargerOrderStopCodeKey(orderNo);
        log.debug("更新订单停充信息到redis. orderNo = {}, code = {}", orderNo, code);
        super.redisTemplate.opsForValue().set(key, code,
                CacheConstants.REDIS_CHARGE_ORDER_TTL_MINUTES, TimeUnit.MINUTES);
    }


    public <T> void updateChargeOrder(String orderNo, T order) {
        if (StringUtils.isBlank(orderNo)) {
            throw new DcArgumentException("参数错误, 订单号不能为空");
        }
        String key = RedisKeyGenerator.genChargerOrderInfoKey(orderNo);
        String strVal = JsonUtils.toJsonString(order);
        log.debug("更新订单信息到redis. order = {}", JsonUtils.toJsonString(order));
        super.redisTemplate.opsForValue().set(key, strVal,
                CacheConstants.REDIS_CHARGE_ORDER_TTL_MINUTES, TimeUnit.MINUTES);
    }

    public void setExpectLimitSoc(String orderNo, Integer expectLimitSoc) {
        if (StringUtils.isBlank(orderNo)) {
            throw new DcArgumentException("参数错误, 订单号不能为空");
        }
        if (expectLimitSoc == null) {
            throw new DcArgumentException("参数错误, soc不能为空");
        }
        String key = RedisKeyGenerator.genExpectLimitSocInfoKey(orderNo);
        log.debug("缓存订单期望的soc到redis. order = {}, expectLimitSoc = {}", orderNo, expectLimitSoc);
        super.redisTemplate.opsForValue().set(key, expectLimitSoc.toString(),
                CacheConstants.REDIS_CHARGE_ORDER_TTL_MINUTES, TimeUnit.MINUTES);
    }

    public Boolean removeExpectLimitSoc(String orderNo) {
        return super.redisTemplate.delete(RedisKeyGenerator.genExpectLimitSocInfoKey(orderNo));
    }

    public void setLimitSocChangeEvent(String orderNo, String seq) {
        if (StringUtils.isBlank(orderNo)) {
            throw new DcArgumentException("参数错误, 订单号不能为空");
        }
        if (StringUtils.isBlank(seq)) {
            throw new DcArgumentException("参数错误, seq不能为空");
        }
        String key = RedisKeyGenerator.genLimitSocEventKey(orderNo);
        log.debug("设置soc限制事件到redis. order = {}, seq = {}", orderNo, seq);
        super.redisTemplate.opsForValue().set(key, seq,
                CacheConstants.REDIS_CHARGE_ORDER_TTL_MINUTES, TimeUnit.MINUTES);
    }

    public Boolean removeLimitSocChangeEvent(String orderNo) {
        return super.redisTemplate.delete(RedisKeyGenerator.genLimitSocEventKey(orderNo));
    }


    /**
     * 将缓存的订单删除. 仅用于测试
     *
     * @param orderNo 订单号
     */
    public void removeChargeOrder(String orderNo) {
        log.warn("删除缓存的订单. orderNo = {}", orderNo);
        super.redisTemplate.delete(RedisKeyGenerator.genChargerOrderInfoKey(orderNo));
        super.redisTemplate.delete(RedisKeyGenerator.genChargerOrderDetailKey(orderNo));
        this.removeExpectLimitSoc(orderNo);
        this.removeLimitSocChangeEvent(orderNo);
    }


    public <T> void addChargeOrderDetail(String orderNo, List<T> orderDetail) {
        if (StringUtils.isBlank(orderNo)) {
            throw new DcArgumentException("参数错误, 订单号不能为空");
        }
        String key = RedisKeyGenerator.genChargerOrderDetailKey(orderNo);
        List<String> strList = orderDetail.stream().map(d -> JsonUtils.toJsonString(d))
                .collect(Collectors.toList());
        BoundListOperations ops = redisTemplate.boundListOps(key);
        //ops.rightPushAll(strList);
        redisTemplate.opsForList().rightPushAll(key, strList);
        ops.expire(CacheConstants.REDIS_CHARGE_ORDER_TTL_MINUTES, TimeUnit.MINUTES);
    }


}
