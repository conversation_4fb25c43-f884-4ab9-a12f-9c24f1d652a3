package com.cdz360.data.cache;

import com.cdz360.base.utils.NumberUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class RedisLockService {

    private static final String LOCK_KEY_PREFIX = "lock:";
    private static final int LOCK_TIME_SECONDS = 10;
    @Autowired
    private StringRedisTemplate redisTemplate;

    /**
     * 获取key对应的锁.
     *
     * @param key 唯一的key值
     * @return 返回是有成功获取到锁
     */
    public boolean lock(String key) {

        String lockKey = getCacheLockKey(key);
        IotRedisCallback callback = new IotRedisCallback(lockKey);

        Boolean ret = redisTemplate.execute(callback);
        return ret != null && ret;
    }


    public void unlock(String key) {
        log.debug("unlock key = {}", key);
        redisTemplate.delete(getCacheLockKey(key));
    }


//    private String getCacheKey(String seq) {
//        return CACHE_KEY_PREFIX + seq;
//    }

    private String getCacheLockKey(String key) {
        return LOCK_KEY_PREFIX + key;
    }

    public static class IotRedisCallback implements RedisCallback<Boolean> {

        private String lockKey;

        IotRedisCallback(String lockKey) {
            this.lockKey = lockKey;
        }

        @Override
        public Boolean doInRedis(RedisConnection connection) throws DataAccessException {
            Boolean acquire = connection.setNX(lockKey.getBytes(), String.valueOf(getExpireTime()).getBytes());

            if (acquire != null && acquire) {
                connection.pExpireAt(lockKey.getBytes(), getExpireTime());
                return true;
            } else {

                byte[] value = connection.get(lockKey.getBytes());

                if (value != null && value.length > 0) {

                    long expireTime = Long.parseLong(new String(value));

                    if (expireTime < System.currentTimeMillis()) {
                        long oldValue = NumberUtils.parseLong(connection.getSet(lockKey.getBytes(),
                                String.valueOf(getExpireTime()).getBytes()), 0L);
                        return oldValue < System.currentTimeMillis();
                    }
                }
            }
            return false;
        }

        private long getExpireTime() {
            return System.currentTimeMillis() + RedisLockService.LOCK_TIME_SECONDS * 1000 + 1;
        }
    }
}

