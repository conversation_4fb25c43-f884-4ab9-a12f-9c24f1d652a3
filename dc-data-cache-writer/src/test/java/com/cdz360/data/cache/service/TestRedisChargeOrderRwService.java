package com.cdz360.data.cache.service;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.data.cache.DataCacheWriterTestBase;
import com.cdz360.data.cache.RedisChargeOrderRwService;
import com.cdz360.data.cache.model.ChargeOrderCacheVo;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Slf4j
public class TestRedisChargeOrderRwService extends DataCacheWriterTestBase {

    @Autowired
    private RedisChargeOrderRwService redisChargeOrderRwService;
    private ChargeOrderCacheVo order;

    @BeforeEach
    public void setup() {
        order = new ChargeOrderCacheVo();
        order.setOrderNo("ut-" + UUID.randomUUID().toString())
                .setEvseNo("ut-" + UUID.randomUUID().toString());

    }

    @AfterEach
    public void cleanup() {
        if (order != null) {
            redisChargeOrderRwService.removeChargeOrder(order.getOrderNo());
        }

    }

    @Test
    public void test_updateChargerOrder() {
        redisChargeOrderRwService.updateChargeOrder(order.getOrderNo(), order);
        ChargeOrderCacheVo result = redisChargeOrderRwService.getOrder(order.getOrderNo(), ChargeOrderCacheVo.class);
        log.info("result = {}", JsonUtils.toJsonString(result));
    }

    @Test
    public void test_updateChargerOrderStopCode() {
        redisChargeOrderRwService.setOrderStopCodeString(order.getOrderNo(), "1234");
        String result = redisChargeOrderRwService.getOrderStopCodeString(order.getOrderNo());
        log.info("result = {}", JsonUtils.toJsonString(result));
    }


    @Test
    public void test_getChargeOrderDetail() {
        List<OrderDetail> detailList = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            OrderDetail detail = new OrderDetail();
            detail.abc = String.valueOf(i + 1);
            detail.efg = String.valueOf(i * i);
            detailList.add(detail);
        }
        redisChargeOrderRwService.addChargeOrderDetail(order.getOrderNo(), detailList);
        List<OrderDetail> result = redisChargeOrderRwService.getChargeOrderDetail(order.getOrderNo(), OrderDetail.class);
        log.info("result = {}", JsonUtils.toJsonString(result));
    }

    @Test
    public void test_updateExpectLimitSoc() {
        redisChargeOrderRwService.setExpectLimitSoc(order.getOrderNo(), 77);
        Integer result = redisChargeOrderRwService.getExpectLimitSoc(order.getOrderNo());
        log.info("result = {}", JsonUtils.toJsonString(result));
    }

    @Test
    public void test_updateLimitSocEvent() {
        redisChargeOrderRwService.setLimitSocChangeEvent(order.getOrderNo(), "123");
        String result = redisChargeOrderRwService.getLimitSocChangeEvent(order.getOrderNo());
        log.info("result = {}", JsonUtils.toJsonString(result));
    }

    @Data
    public static class OrderDetail {
        String abc;
        String efg;
    }
}
