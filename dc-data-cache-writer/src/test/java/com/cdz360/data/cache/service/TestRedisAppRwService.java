package com.cdz360.data.cache.service;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.app.vo.AppCfg;
import com.cdz360.data.cache.DataCacheWriterTestBase;
import com.cdz360.data.cache.RedisAppRwService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;

@Slf4j
public class TestRedisAppRwService extends DataCacheWriterTestBase {

    @Autowired
    private RedisAppRwService redisAppRwService;

    @Test
    public void test_updateAppCfg() {
        AppCfg appCfg = new AppCfg();
        appCfg.setAppId("unit-test-app-id");
        appCfg.setAppCommId(123456L);
        appCfg.setAppName("单元测试");
        appCfg.setAppType(AppClientType.WX_LITE);
        appCfg.setMinChargeAmount(BigDecimal.valueOf(5.01));
        appCfg.setMinPrepayAmount(BigDecimal.valueOf(100.02));
        redisAppRwService.updateAppCfg(appCfg);
        AppCfg result = redisAppRwService.getAppCfg(appCfg.getAppId());
        log.info("result = {}", result);
        Assertions.assertNotNull(result);
        Assertions.assertEquals(appCfg, result);
    }
}
