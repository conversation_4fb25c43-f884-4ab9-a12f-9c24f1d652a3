package com.cdz360.data.cache;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;


@SpringBootApplication
public class DataCacheWriterTestMain {
    private final static Logger logger = LoggerFactory.getLogger(DataCacheWriterTestMain.class);

    public static void main(String[] args) {
        logger.info("starting....");
        SpringApplication.run(DataCacheWriterTestMain.class, args);
        logger.info("started");
    }
}