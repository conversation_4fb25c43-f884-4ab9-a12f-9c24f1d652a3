package com.cdz360.data.cache.service;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.cus.vo.CusInfoVo;
import com.cdz360.data.cache.DataCacheWriterTestBase;
import com.cdz360.data.cache.RedisCusRwService;
import java.util.List;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class TestRedisCusRwService extends DataCacheWriterTestBase {

    @Autowired
    private RedisCusRwService redisCusRwService;

    private long topCommId;
    private long cusId;
    private CusInfoVo cusInfo;

    @BeforeEach
    public void setup() {
        topCommId = 22334L;
        cusId = System.currentTimeMillis();
        cusInfo = new CusInfoVo();
        cusInfo.setCusId(cusId).setName(UUID.randomUUID().toString())
            .setPhone("98765432101").setName(UUID.randomUUID().toString())
            .setCommId(1234L)
            .setTopCommId(topCommId)
            .setClients(
                List.of(AppClientType.WX_LITE,
                    AppClientType.MGM_WX_LITE));
        redisCusRwService.updateCusInfo(AppClientType.WX_LITE, cusInfo);

        redisCusRwService.updateUserInfo(cusInfo);
    }

    @AfterEach
    public void cleanup() {
        if (cusInfo != null) {
            redisCusRwService.deleteCusInfoCache(AppClientType.WX_LITE, cusInfo.getCusId());
        }
    }

    @Test
    public void test_updateCusInfo() {
        String name = UUID.randomUUID().toString().replace("-", "");
        CusInfoVo cus = this.redisCusRwService.getCustomerInfoCache(AppClientType.WX_LITE, cusId);
        cus.setName(name);
        redisCusRwService.updateCusInfo(AppClientType.WX_LITE, cus);
        CusInfoVo result = this.redisCusRwService.getCustomerInfoCache(AppClientType.WX_LITE,
            cusId);
        Assertions.assertNotNull(result);
        Assertions.assertEquals(name, result.getName());
    }

    @Test
    public void test_updateUserInfo() {
        String name = UUID.randomUUID().toString().replace("-", "");
        CusInfoVo cus = this.redisCusRwService.getUserInfoCache(topCommId, cusId);
        log.info("从redis获取 cus= {}", cus);
        cus.setName(name);
        redisCusRwService.updateUserInfo(cus);
        CusInfoVo result = this.redisCusRwService.getUserInfoCache(topCommId,
            cusId);
        Assertions.assertNotNull(result);
        Assertions.assertEquals(name, result.getName());
    }
}
