package com.cdz360.data.cache.service;

import com.cdz360.base.model.base.type.IotGwCmdType2;
import com.cdz360.base.model.base.type.PlugStatus;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.model.iot.vo.PlugVo;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.data.cache.DataCacheWriterTestBase;
import com.cdz360.data.cache.RedisIotRwService;
import com.cdz360.data.cache.model.IotGwCmdCacheVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.*;

@Slf4j
public class TestRedisIotRwService extends DataCacheWriterTestBase {

    @Autowired
    private RedisIotRwService iotRedisRwService;

    private String siteId;
    private EvseVo evse;
    private PlugVo plug;

    @BeforeEach
    public void setup() {
        siteId = UUID.randomUUID().toString();
        evse = new EvseVo();
        evse.setEvseNo("ut-" + UUID.randomUUID().toString().substring(0, 8))
                .setName("testabcd").setPlugNum(4).setSiteId("ut-" + "zhelishichangzhanid");
        iotRedisRwService.updateEvseRedisCache(evse);

        plug = new PlugVo();
        plug.setPlugNo("aaa:bbb:ccc");
        iotRedisRwService.updatePlugRedisCache(plug);
    }

    @AfterEach
    public void cleanup() {
        if (evse != null) {
            iotRedisRwService.deleteEvseRedisCache(evse.getEvseNo());
        }

        if (plug != null) {
            iotRedisRwService.deletePlugRedisCache(plug.getPlugNo());
        }
    }


    @Test
    public void test_bindEvse() {
        log.trace(">>");
        List<String> plugNoList = List.of(evse.getEvseNo() + "01", evse.getEvseNo() + "03");
        iotRedisRwService.bindEvse(siteId, evse.getEvseNo(), plugNoList);

        // 查看场站绑定的桩编号
        Set<String> evseNoList = iotRedisRwService.getSiteEvses(siteId);
        log.info("evseNoList = {}", evseNoList);
        Assertions.assertEquals(1, evseNoList.size());

        // 查看场站绑定的枪头编号
        Set<String> plugNos = iotRedisRwService.getSitePlugs(siteId);
        log.info("plugNos = {}", plugNos);
        Assertions.assertEquals(4, plugNos.size());  // 设置有4个枪

        iotRedisRwService.unbindEvse(siteId, evse.getEvseNo(), plugNoList);
        iotRedisRwService.deleteEvseRedisCache(evse.getEvseNo());


        // 解绑后, 查看场站绑定的桩编号
        evseNoList = iotRedisRwService.getSiteEvses(siteId);
        log.info("evseNoList = {}", evseNoList);

        // 解绑后, 查看场站绑定的枪头编号
        plugNos = iotRedisRwService.getSitePlugs(siteId);
        log.info("plugNos = {}", plugNos);
        log.trace("<<");
    }

    @Test
    public void test_updateEvseInfo() {
        log.trace(">>");
        EvseVo result = iotRedisRwService.getEvseRedisCache(evse.getEvseNo());
        log.info("result from redis = {}", JsonUtils.toJsonString(result));
        evse.setName("efgsc").setSiteId("");
        iotRedisRwService.updateEvseRedisCache(evse);
        result = iotRedisRwService.getEvseRedisCache(evse.getEvseNo());
        Assertions.assertEquals(result.getName(), "efgsc");
        Assertions.assertEquals(result.getSiteId(), "");
        log.info("result from redis = {}", JsonUtils.toJsonString(result));
        this.iotRedisRwService.deleteEvseRedisCache(evse.getEvseNo());
        log.trace("<<");
    }

    @Test
    public void test_getEvseList() {
        List<String> evseNoList = new ArrayList<>();
        evseNoList.add(evse.getEvseNo());
        evseNoList.add("gewgewgewgewehwehweegwxx");   // 故意加一个没有的
        List<EvseVo> list = iotRedisRwService.getEvseList(evseNoList);
        log.info("list = {}", list);
    }

    @Test
    public void test_getPlugList() {

        PlugVo plug = new PlugVo();
        plug.setEvseNo("ut-" + UUID.randomUUID().toString().substring(0, 8))
                .setIdx(2)
                .setName("testabcd");
        PlugVo result = iotRedisRwService.updatePlugRedisCache(plug);
        log.info("result = {}", result);

        List<String> plugNoList = new ArrayList<>();
        plugNoList.add(plug.getPlugNo());
        plugNoList.add("gewgewgewgewehwehweegw");   // 故意加一个没有的
        List<PlugVo> list = iotRedisRwService.getPlugList(plugNoList);
        log.info("list = {}", list);


    }

    @Test
    public void test_updatePlugInfo() {
        log.debug(">>");
        PlugVo plug = new PlugVo();
        plug.setEvseNo("ut-" + UUID.randomUUID().toString().substring(0, 8))
                .setIdx(2).setGwno("old-gwno")
                .setName("testabcd")
                .setMinVoltage(BigDecimal.valueOf(111.111))
                .setMaxVoltage(BigDecimal.valueOf(222.222))
                .setMinCurrent(BigDecimal.valueOf(333.333))
                .setMaxCurrent(BigDecimal.valueOf(444.444));
        iotRedisRwService.updatePlugRedisCache(plug);
        PlugVo result = iotRedisRwService.getPlugRedisCache(plug.getEvseNo(), plug.getIdx());
        log.info("result from redis = {}", JsonUtils.toJsonString(result));
        plug.setName("efgsc").setGwno("new-gwno")
                .setMinVoltage(BigDecimal.valueOf(111.1111))
                .setMaxVoltage(BigDecimal.valueOf(222.2222))
                .setMinCurrent(BigDecimal.valueOf(333.3333))
                .setMaxCurrent(BigDecimal.valueOf(444.4444));
        iotRedisRwService.updatePlugRedisCache(plug);
        result = iotRedisRwService.getPlugRedisCache(plug.getEvseNo(), plug.getIdx());
        log.info("result from redis = {}", JsonUtils.toJsonString(result));
        this.iotRedisRwService.deletePlugRedisCache(plug.getEvseNo(), plug.getIdx());
        log.debug("<<");
    }


    @Test
    public void test_listPlugBySiteId() {
        String siteId = "20190719755256729170156133";
        //String siteId = "20190629625743491830359995";
        List<PlugVo> list = this.iotRedisRwService.listPlugBySiteId(siteId);
        log.info("list.size = {}", list.size());
        log.info("list = {}", JsonUtils.toJsonString(list));
    }

    @Test
    public void test_addIotGwCmd() {
        IotGwCmdCacheVo cmd = new IotGwCmdCacheVo();
        String gwno = "ut_" + UUID.randomUUID().toString().replace("-", "");
        String seq = "ut_" + UUID.randomUUID().toString().replace("-", "");
        cmd.setGwno(gwno).setSeq(seq).setCmd(IotGwCmdType2.CE_CHARGE_STOP)
                .setData("sagewgewhewhewh");
        this.iotRedisRwService.addIotGwCmd(cmd);

        IotGwCmdCacheVo result = this.iotRedisRwService.getIotGwCmd(gwno, seq);
        log.info("result = {}", result);
        Assertions.assertNotNull(result);

        long timestamp = new Date().getTime() + 1;
        List<String> seqs = this.iotRedisRwService.getGwSeqs(timestamp);
        log.info("seqs = {}", seqs);
        Assertions.assertFalse(seqs.isEmpty());
        Assertions.assertTrue(seqs.contains(seq));


        this.iotRedisRwService.deleteIotGwCmd(gwno, seq);
        result = this.iotRedisRwService.getIotGwCmd(gwno, seq);
        log.info("result after delete: {}", result);
        Assertions.assertNull(result);

        seqs = this.iotRedisRwService.getGwSeqs(timestamp);
        log.info("seqs = {}", seqs);
        Assertions.assertFalse(seqs.contains(seq)); // 删除后不再存在
    }


    @Test
    public void test_updatePriceCode() {
        String evseNo = "UT_ABCD1234";
        List<String> plugNoList = List.of("UT_ABCD1234_01", "UT_ABCD1234_02", "UT_ABCD1234_03");
        this.iotRedisRwService.updatePriceCode(evseNo, plugNoList, 1234L);
    }

    @Test
    public void test_updatePlugsStatus() {
        this.iotRedisRwService.updatePlugsStatus("bbb:ccc", PlugStatus.BUSY);
    }
}
