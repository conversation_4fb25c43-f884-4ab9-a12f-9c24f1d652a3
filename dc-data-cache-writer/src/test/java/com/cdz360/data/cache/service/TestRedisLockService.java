package com.cdz360.data.cache.service;

import com.cdz360.data.cache.DataCacheWriterTestBase;
import com.cdz360.data.cache.RedisLockService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.UUID;

@Slf4j
public class TestRedisLockService extends DataCacheWriterTestBase {

    @Autowired
    private RedisLockService redisLockService;

    @Test
    public void testLockTwice() {
        String key = UUID.randomUUID().toString();
        boolean ret = redisLockService.lock(key);
        log.info("ret = {}", ret);
        Assertions.assertTrue(ret);
        ret =redisLockService.lock(key);
        log.info("ret = {}", ret);
        Assertions.assertFalse(ret);
    }

    @Test
    public void testUnlock() {
        String key = UUID.randomUUID().toString();
        boolean ret = redisLockService.lock(key);
        log.info("ret = {}", ret);
        Assertions.assertTrue(ret);
        redisLockService.unlock(key);
        ret = redisLockService.lock(key);
        log.info("ret = {}", ret);
        Assertions.assertTrue(ret);
    }
}
