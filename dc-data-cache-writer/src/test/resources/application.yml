app:
  name: cacheWriterTest

server:
  address: 0.0.0.0
  port: 8080
  use-forward-headers: true
  compression.enabled: true

spring:
  config:
    import: "configserver:http://oam-test.iot.renwochong.com"
  profiles:
    active: common,redis
  cloud:
    config:
      label: test01



management:
  context-path: /admin
  security:
    enabled: false



logging:
  level:
    org.springframework: 'INFO'
    feign: 'DEBUG'



