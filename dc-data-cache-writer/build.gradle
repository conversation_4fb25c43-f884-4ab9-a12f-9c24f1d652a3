
plugins {
    id 'java'
}

jar{
    enabled = true
}


dependencies {

    implementation project(':dc-base-model')
    implementation project(':dc-base-utils')
    implementation project(':dc-data-cache')
    implementation project(':dc-data-cache-reader')
    implementation('org.springframework.boot:spring-boot-starter-data-redis')
    implementation("com.fasterxml.jackson.core:jackson-databind:${jacksonVersion}")
    implementation("com.fasterxml.jackson.core:jackson-core:${jacksonVersion}")
    implementation("com.fasterxml.jackson.core:jackson-annotations:${jacksonVersion}")


    testRuntimeOnly("org.apache.commons:commons-pool2:${commonsPoolVersion}")
    testRuntimeOnly('org.springframework.cloud:spring-cloud-starter-config')
}