package com.cdz360.mgc.model.hybridInverter.trans;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 枚举数据项
 */
@Data
@Accessors(chain = true)
public class EnumItem {

    /**
     * 数据描述
     */
    @SerializedName("l")
    @JsonProperty("l")
    private String label;

    /**
     * 数据值
     */
    @SerializedName("v")
    @JsonProperty("v")
    private Float value;
}
