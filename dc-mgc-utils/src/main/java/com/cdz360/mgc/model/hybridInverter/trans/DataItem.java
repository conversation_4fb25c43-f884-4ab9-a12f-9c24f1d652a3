package com.cdz360.mgc.model.hybridInverter.trans;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * Modbus协议中数据项信息
 */
@Data
@Accessors(chain = true)
public class DataItem {

    /**
     * 开始寄存器地址
     */
    @SerializedName("ad")
    @JsonProperty("ad")
    private Integer address;

    /**
     * 数据项描述信息
     */
    private String desc;

    /**
     * 多语言配置
     */
    private Map<String, String> i18nMap;

    /**
     * 占用寄存器个数，默认1
     */
    @SerializedName("oc")
    @JsonProperty("oc")
    private int occupy = 1;

    /**
     * 寄存器占用字节数
     */
    @SerializedName("s")
    @JsonProperty("s")
    private Integer size;

    /**
     * 数据类型
     * <p>
     * enum : 枚举</p><p>int : 整形</p><p>uint : 无符号整形</p><p>float : 浮点</p><p>bit : 位
     */
    @SerializedName("dt")
    @JsonProperty("dt")
    private String dataType;

    /**
     * 如果dataType == ‘enum'，需要提供该列表
     */
    private List<EnumItem> enumList; // [{label: '', val: 567688},...]

    /**
     * 数据单位
     */
    private String unit;

//    /**
//     * 数据转换模式
//     * <p>缓冲区数据转成Long承接使用的模式</p>
//     * <p>0x1234 --> 0x1234(4660)</p>
//     * <p>0x1234 --> 0x3412(13330)</p>
//     * <p>0x1234 -BCD-> 1234</p>
//     */
//    @JsonProperty("dm")
//    private String dataMode;

    /**
     * 字段映射模式
     * <p>数据域映射对象字段</p>
     * <p>0x1234 --> { field: 0x1234 }</p>
     */
    @SerializedName("fm")
    @JsonProperty("fm")
    private String fieldMapper;

    /**
     * 国际化显示
     *
     * @param lang 语言
     * @return
     */
    public String i18nDesc(String lang) {
        if (null == i18nMap) {
            return desc;
        }
        return i18nMap.getOrDefault(lang, desc);
    }
}
