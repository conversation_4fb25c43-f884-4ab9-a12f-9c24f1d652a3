package com.cdz360.mgc.model.hybridInverter.trans;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ConfigTab {

    /**
     * 数据项描述信息
     */
    private String desc;

    /**
     * 多语言配置
     */
    private Map<String, String> i18nMap;

    /**
     * 功能码
     */
    @SerializedName("fc")
    @JsonProperty("fc")
    private int functionCode;

    /**
     * 寄存器地址列表
     */
    @SerializedName("ads")
    @JsonProperty("ads")
    private List<Integer> addressList;

    /**
     * 国际化显示
     *
     * @param lang 语言
     * @return
     */
    public String i18nDesc(String lang) {
        if (null == i18nMap) {
            return desc;
        }
        return i18nMap.getOrDefault(lang, desc);
    }
}
