package com.cdz360.mgc.model.hybridInverter;

import com.cdz360.base.model.es.vo.hi.InverterRtData;
import com.cdz360.base.model.es.vo.hi.InverterRtInfo;
import com.cdz360.mgc.south.hybridInverter.InverterService;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.ToString;

@Data
@ToString(callSuper = true)
public class Inverter {

    private InverterRtInfo rtInfo;

    /**
     * 最后一次获取到的运行数据
     */
    @JsonIgnore
    private InverterRtData lastRtData;

    @JsonIgnore // 不输出到配置文件
    private InverterService inverterService;


}
