package com.cdz360.mgc.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Data;
import lombok.Getter;

@Data
public class SinexcelBasicInfo {

    private CommandType commandType;

    private String seq;

    private Command command;

    private Long dataLength;

    private byte[] data;

    @Getter
    public enum CommandType {
        UNKNOWN(999, "未知"),
        REQ(0, "请求"),
        RSP(1, "响应"),
        ;

        @JsonValue
        private final Integer code;
        private final String desc;

        CommandType(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        @JsonCreator
        public static CommandType valueOf(Object codeIn) {
            if (codeIn == null) {
                return CommandType.UNKNOWN;
            }
            int code = 0;
            if (codeIn instanceof CommandType) {
                return (CommandType) codeIn;
            } else if (codeIn instanceof Integer) {
                code = (Integer) codeIn;
            } else if (codeIn instanceof Long) {
                code = ((Long) codeIn).intValue();
            } else if (codeIn instanceof String) {
                code = Integer.parseInt((String) codeIn);
            }
            for (CommandType type : values()) {
                if (type.code == code) {
                    return type;
                }
            }
            return CommandType.UNKNOWN;
        }
    }

    @Getter
    public enum Command {
        UNKNOWN(0x00, "未知"),
        REGISTER(0x01, "网关注册平台"),
        HEARTBEAT(0x02, "网关发起心跳"),
        REPORT_DATA(0x03, "网关上报数据"),
        REPORT_DATA_END(0x1B, "网关上报数据完成标志"),
        REPORT_HISTORY_DATA(0x04, "网关上报历史数据"),


        READ_WIFI_NETWORKING_PARAMETERS(0x0F, "读取WiFi联网参数"),
        SET_WIFI_NETWORKING_PARAMETERS(0x10, "配置WiFi联网参数"),
        READ_NETWORK_STATUS(0x11, "读取联网状态"),
        SYSTEM_REBOOT(0x12, "系统重启"),


        GET_SERVER_INFORMATION(0x17, "读取系统版本信息"),
        READ_THE_GATEWAY_LOCATION_INFORMATION(0x18, "读取网关位置信息"),
        ;

        @JsonValue
        private final Integer code;
        private final String desc;

        Command(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        @JsonCreator
        public static Command valueOf(Object codeIn) {
            if (codeIn == null) {
                return Command.UNKNOWN;
            }
            int code = 0;
            if (codeIn instanceof Command) {
                return (Command) codeIn;
            } else if (codeIn instanceof Integer) {
                code = (Integer) codeIn;
            } else if (codeIn instanceof Long) {
                code = ((Long) codeIn).intValue();
            } else if (codeIn instanceof String) {
                code = Integer.parseInt((String) codeIn);
            }
            for (Command type : values()) {
                if (type.code == code) {
                    return type;
                }
            }
            return Command.UNKNOWN;
        }
    }

}
