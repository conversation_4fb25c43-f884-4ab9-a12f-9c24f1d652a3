package com.cdz360.mgc.model.hybridInverter;

import com.cdz360.mgc.model.hybridInverter.sinexcel.SinexcelOpCode;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import javax.annotation.Nullable;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@JsonInclude(Include.NON_NULL)
public class InverterMsgDown {

    /**
     * 设备通讯地址
     */
    private Integer deviceId;

    private SinexcelOpCode opCode;

    /**
     * 状态起始地址
     * <p>升级相关报文不存在该值</p>
     */
    @Nullable
    private Integer startAddress;

    private Integer num;

    /**
     * 下行报文
     */
    private String downMsg;

    /**
     * 下行报文
     */
    private byte[] byteMsg;

    /**
     * 最后一个有意义的报文标识
     * <p>定时充放电，不支持多参数下发时存在</p>
     */
    @Nullable
    private Boolean lastMeaningfulFlag;

}
