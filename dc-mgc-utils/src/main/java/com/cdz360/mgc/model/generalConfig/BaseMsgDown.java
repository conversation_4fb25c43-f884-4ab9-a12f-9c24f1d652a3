package com.cdz360.mgc.model.generalConfig;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@JsonInclude(Include.NON_NULL)
public class BaseMsgDown {

    /**
     * 设备通讯地址
     */
    private Integer deviceId;

    private Integer opCode;

    /**
     * 状态起始地址
     */
    private Integer startAddress;

    /**
     * 下行报文
     */
    private String downMsg;

}
