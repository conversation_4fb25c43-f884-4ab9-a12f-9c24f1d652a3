package com.cdz360.mgc.model;

import com.cdz360.base.model.es.dto.EssDtuDto;
import com.cdz360.mgc.model.dtu.elinter.ElinterMsgType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ElinterEssDtu extends EssDtuDto {

    private Integer ver;

    private Byte highByte;
    private Byte lowByte;
    /**
     * 操作码
     */
    private ElinterMsgType msgType;

    /**
     * 参数
     */
    private Integer para;

    private String msgId;

    private Long dataLength;

}
