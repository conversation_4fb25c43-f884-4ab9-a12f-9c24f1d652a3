package com.cdz360.mgc.model.hybridInverter.trans;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ManualConfig {

    private String ver;

    @SerializedName("fcs")
    @JsonProperty("fcs")
    private List<FunctionCodeData> functionCodeDataList;

    /**
     * 主机配置项配置信息
     */
    @SerializedName("hc")
    @JsonProperty("hc")
    private ConfigTab hostConfig;

    /**
     * 电池配置项配置信息
     */
    @SerializedName("bc")
    @JsonProperty("bc")
    private ConfigTab batConfig;

    /**
     * 高级配置项配置信息
     */
    @SerializedName("sc")
    @JsonProperty("sc")
    private ConfigTab seniorConfig;
}
