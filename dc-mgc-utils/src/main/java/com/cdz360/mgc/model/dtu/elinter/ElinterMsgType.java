package com.cdz360.mgc.model.dtu.elinter;

import lombok.Getter;

public enum ElinterMsgType {

//    UNKNOWN((byte)0x00, (byte)0x00, (byte)0x00),

    REPORT_DATA_UP((byte) 0x01, (byte) 0x01), // Client请求，上行
    REPORT_DATA_DOWN((byte) 0xA1, (byte) 0x01), // Server应答，下行

    READ_DATA_DOWN((byte) 0x02, (byte) 0x01), // Server读取采集器信息，下行
    READ_DATA_UP((byte) 0xA2, (byte) 0x01), // Client响应采集器信息，上行

    UPGRADE_DOWN((byte) 0x02, (byte) 0x02), // Server推送新固件请求，下行
    UPGRADE_UP((byte) 0xA2, (byte) 0x02), // Client推送新固件应答，上行

    ;
    @Getter
    private final byte highByte;

    @Getter
    private final byte lowByte;

    ElinterMsgType(byte highByte, byte lowByte) {
        this.highByte = highByte;
        this.lowByte = lowByte;
    }


    public static ElinterMsgType codeOf(byte highByte, byte lowByte) {
        for (ElinterMsgType type : ElinterMsgType.values()) {
            if (highByte == type.getHighByte()
                && lowByte == type.getLowByte()) {
                return type;
            }
        }
        return null;
    }
}
