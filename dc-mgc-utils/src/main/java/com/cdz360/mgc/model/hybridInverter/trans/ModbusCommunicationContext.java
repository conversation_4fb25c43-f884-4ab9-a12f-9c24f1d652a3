package com.cdz360.mgc.model.hybridInverter.trans;

import java.nio.ByteBuffer;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * Modbus通讯上下文
 */
@Data
@Accessors(chain = true)
@Builder
public class ModbusCommunicationContext {

    /**
     * 交互序列号<p>[用来控制多次下发使用]</p>
     */
    private String seq;

    /**
     * 设备地址
     */
    private Byte devId;

    /**
     * 功能码
     */
    private int fc;

    /**
     * 数据项信息
     */
    private DataItemValue dataItem;

    /**
     * 应答BUFFER
     */
    private ByteBuffer buffer;

    /**
     * 错误应答信息
     */
    private ErrorResponseMessage errorResponseMessage;

    /**
     * 解析并填充数据
     *
     * @return buffer原始值
     */
    public Float parse() {
        if (buffer.position() > 0) {
            buffer.flip(); // 将读模式改成
        }

        if (buffer.limit() <= 0) {
            return -1F;
        }

        byte targetDevId = buffer.get();
        if (!devId.equals(targetDevId)) {
            return -1F;
        }

        byte targetFc = buffer.get();
        if (fc != targetFc) {
            // 错误应答
            errorResponseMessage = new ErrorResponseMessage()
                .setFunctionCode(targetFc)
                .setErrorCode(buffer.get());
            return -2F;
        }

        // 字节数量 = 寄存器数量 * 2
        byte occupy = buffer.get();
        if (occupy > 0) {
            float val = buffer.getFloat();
            dataItem.setValue(val);

            // 可视值
            // FIXME: 根据配置项进行换
            // TODO: 2024/4/1 返回值可全部认为Float
            float visibleValue = val;
            dataItem.setVisibleValues(visibleValue);

            return val;
        }
        return 0F;
    }
}
