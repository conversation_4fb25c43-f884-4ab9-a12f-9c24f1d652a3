package com.cdz360.mgc.model.hybridInverter.trans;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * Modbus协议中数据项信息
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class DataItemValue extends DataItem {

    /**
     * 寄存器对应值
     */
    @SerializedName("val")
    @JsonProperty("val")
    private Float value;

    /**
     * 寄存器对应值对应可视值
     * <p>需要根据实际值做转换</p>
     */
    @SerializedName("vis")
    @JsonProperty("vis")
    private Float visibleValues;
}
