package com.cdz360.mgc.model.hybridInverter;

import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.ByteUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.mgc.model.hybridInverter.sinexcel.SinexcelOpCode;
import com.cdz360.mgc.model.hybridInverter.trans.ConfigTab;
import com.cdz360.mgc.model.hybridInverter.trans.DataItem;
import com.cdz360.mgc.model.hybridInverter.trans.DataItemValue;
import com.cdz360.mgc.model.hybridInverter.trans.FunctionCodeData;
import com.cdz360.mgc.model.hybridInverter.trans.ManualConfig;
import java.nio.ByteBuffer;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ManualConfigSingleton {

    private static volatile ManualConfigSingleton manualConfig;

    /**
     * 文件内容
     */
    private String json;

    /**
     * 反序列化数据对象
     */
    private ManualConfig data;

    private ManualConfigSingleton() {
    }

    @SuppressWarnings("all")
    public static ManualConfigSingleton getInstance() {
        if (manualConfig == null) {
            synchronized (ManualConfigSingleton.class) {
                if (manualConfig == null) {
                    manualConfig = new ManualConfigSingleton();
                }
            }
        }
        return manualConfig;
    }

    /**
     * 配置JSON文件内容
     *
     * @param json
     * @return
     */
    public static ManualConfigSingleton configJson(String json) {
        // 做json格式校验
        return getInstance().setJson(json)
            .setData(JsonUtils.fromJson(json, ManualConfig.class));
    }

    /**
     * 配置JSON文件内容
     *
     * @param json
     * @param lang 本地语言
     * @return
     */
    public static ManualConfigSingleton configJson(String json, String lang) {
        // 做json格式校验
        ManualConfigSingleton mc = getInstance().setJson(json)
            .setData(JsonUtils.fromJson(json, ManualConfig.class));

        if (StringUtils.isNotBlank(lang)) {
            mc.switchLang(lang);
        }
        return mc;
    }

    public ManualConfigSingleton switchLang(String lang) {
        List<FunctionCodeData> fcList = this.getData().getFunctionCodeDataList();
        if (CollectionUtils.isNotEmpty(fcList)) {
            fcList.forEach(fc -> {
                if (CollectionUtils.isNotEmpty(fc.getDataItemList())) {
                    fc.getDataItemList().forEach(x -> x.setDesc(x.i18nDesc(lang)));
                }
            });
        }
        return this;
    }

    /**
     * 获取主机配置项
     *
     * @return 配置数据项信息
     */
    public List<DataItemValue> hostConfigItems() {
        return hostConfigItems(null);
    }

    /**
     * 获取主机配置项
     *
     * @param labelLike 模糊查询参数
     * @return 配置数据项信息
     */
    public List<DataItemValue> hostConfigItems(String labelLike) {
        return configItems(data.getHostConfig(), labelLike);
    }

    /**
     * 获取电池配置项
     *
     * @return 配置数据项信息
     */
    public List<DataItemValue> batConfigItems() {
        return batConfigItems(null);
    }

    /**
     * 获取电池配置项
     *
     * @param labelLike 模糊查询参数
     * @return 配置数据项信息
     */
    public List<DataItemValue> batConfigItems(String labelLike) {
        return configItems(data.getBatConfig(), labelLike);
    }

    /**
     * 获取高级配置项
     *
     * @return 配置数据项信息
     */
    public List<DataItemValue> seniorConfigItems() {
        return seniorConfigItems(null);
    }

    /**
     * 获取高级配置项
     *
     * @param labelLike 模糊查询参数
     * @return 配置数据项信息
     */
    public List<DataItemValue> seniorConfigItems(String labelLike) {
        return configItems(data.getSeniorConfig(), labelLike);
    }

    private List<DataItemValue> configItems(ConfigTab config, String labelLike) {
        if (null == config || null == config.getAddressList()) {
            return List.of();
        }
        return data.getFunctionCodeDataList().stream()
            .filter(fc -> fc.getFunctionCode() == config.getFunctionCode())
            .findFirst()
            .map(x -> x.getDataItemList().stream()
                .filter(item -> config.getAddressList().contains(item.getAddress()) &&
                    (null == labelLike || item.getDesc().contains(labelLike)))
                .collect(Collectors.toList()))
            .orElseGet(List::of);
    }

    /**
     * 读取数据项BUFFER字符串形式
     *
     * @param devId 设备地址
     * @param info  目标数据项信息
     * @return
     */
    public static String readItemBufferString(Byte devId, DataItem info) {
        return ByteUtils.bytesToHex(readItemBufferCrc(devId, info).array());
    }

    /**
     * 读取数据项BUFFER字符串形式
     *
     * @param devId    设备地址
     * @param infoList 目标数据项信息列表
     * @return
     */
    public static List<String> readItemBufferString(Byte devId, List<DataItem> infoList) {
        if (null == infoList) {
            return List.of();
        }
        return infoList.stream().map(info -> readItemBufferString(devId, info))
            .collect(Collectors.toList());
    }

    /**
     * 读取数据项BUFFER拼接
     *
     * @param devId 设备地址
     * @param info  目标数据项信息
     * @return
     */
    public static ByteBuffer readItemBufferCrc(Byte devId, DataItem info) {
        ByteBuffer buffer = ByteBuffer.allocate(8);
        buffer.put(devId != null ? devId : (byte) 1);
        buffer.put((byte) SinexcelOpCode.ANALOG_AND_WAVEFORM.getCode());
        Integer address = info.getAddress();
        buffer.put(ByteUtils.intToByte2BE(address));
        buffer.put(ByteUtils.intToByte2BE(info.getOccupy()));
        fillCrc(buffer);
        return buffer;
    }

    private static void fillCrc(ByteBuffer buffer) {
        int crc = ByteUtils.crc16(buffer2Bytes(buffer));
        buffer.put((byte) (crc & 0xFF));
        buffer.put((byte) (crc >> 8 & 0xFF));
    }

    /**
     * 读取数据项BUFFER拼接列表
     *
     * @param devId    设备地址
     * @param infoList 目标数据项信息列表
     * @return
     */
    public static List<ByteBuffer> readItemBufferCrc(Byte devId, List<DataItem> infoList) {
        if (null == infoList) {
            return List.of();
        }
        return infoList.stream().map(info -> readItemBufferCrc(devId, info))
            .collect(Collectors.toList());
    }

    /**
     * 写数据项BUFFER字符串形式
     *
     * @param devId 设备地址
     * @param data  目标数据项信息
     * @return
     */
    public static String writeItemBufferString(Byte devId, DataItemValue data) {
        return ByteUtils.bytesToHex(writeItemBufferCrc(devId, data).array());
    }

    /**
     * 写数据项BUFFER拼接
     *
     * @param devId 设备地址
     * @param data  目标数据项信息
     * @return
     */
    public static ByteBuffer writeItemBufferCrc(Byte devId, DataItemValue data) {
        ByteBuffer buffer = ByteBuffer.allocate(13);
        buffer.put(devId != null ? devId : (byte) 1);
        buffer.put((byte) SinexcelOpCode.SET_PARAMS.getCode());
        Integer address = data.getAddress();
        buffer.put(ByteUtils.intToByte2BE(address));
        buffer.put(ByteUtils.intToByte2BE(data.getOccupy()));

        byte[] bytes = ByteUtils.floatToByteBE(data.getValue().floatValue());
        int sum = bytes.length;
        buffer.put((byte) (sum & 0xFF));
        buffer.put(bytes);
        fillCrc(buffer);
        return buffer;
    }

    private static byte[] buffer2Bytes(ByteBuffer buffer) {
        int len = buffer.position();
        byte[] bytes = new byte[len];
        buffer.position(0);

        int idx = 0;
        while (idx < len) {
            bytes[idx] = buffer.get();
            ++idx;
        }

        buffer.position(len);
        return bytes;
    }

    /**
     * 读取数据项BUFFER字符串形式
     *
     * @param devId    设备地址
     * @param dataList 目标数据项信息列表
     * @return
     */
    public static List<String> writeItemBufferString(Byte devId, List<DataItemValue> dataList) {
        if (null == dataList) {
            return List.of();
        }
        return dataList.stream().map(data -> writeItemBufferString(devId, data))
            .collect(Collectors.toList());
    }

}
