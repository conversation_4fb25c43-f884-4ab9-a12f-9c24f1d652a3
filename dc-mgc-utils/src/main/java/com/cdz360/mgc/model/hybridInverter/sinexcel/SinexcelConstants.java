package com.cdz360.mgc.model.hybridInverter.sinexcel;

public interface SinexcelConstants {

    byte BYTE_SINEXCEL_START = 0x7E;

    int BYTE_ERROR_RES_STEP = 0x80; // 错误应答帧 = 请求帧功能码 + 0x80

    int MSG_PARSE_SUCCESS = 0; // 正确应答帧，符合上下行逻辑

    int MSG_PARSE_ERROR_RES = 1; // 错误应答帧，符合上下行逻辑

    int STATUS_AND_ALARM_START_ADDRESS = 0x1000; // 设备状态量起始地址

    int NUMBER_OF_REALTIME_DATA_PER_QUERY_A = 0x64; // 每次查询实时数据的数量

    int DEVICE_REALTIME_DATA_QUEUE_START_ADDRESS_1 = 0x1000;

    int DEVICE_REALTIME_DATA_QUEUE_START_ADDRESS_2 = 0x1064;

    int DEVICE_REALTIME_DATA_QUEUE_START_ADDRESS_3 = 0x10C8;

    int NUMBER_OF_REALTIME_DATA_PER_QUERY_B = 0x48; // 每次查询实时数据的数量

    int DEVICE_REALTIME_DATA_QUEUE_START_ADDRESS_4 = 0x1200;

    int DEVICE_REALTIME_DATA_QUEUE_START_ADDRESS_5 = 0x1248;

    int DEVICE_VENDOR_INFO_START_ADDRESS = 0x2000;

    int DEVICE_SYS_SETTING_START_ADDRESS_1 = 0x3000;

    int DEVICE_WORD_MODE_ADDRESS = 0x300C; // 工作模式

//    int ACTIVE_CHARGE_DISCHARGE_POWER_ADDRESS = 0x304C; // 主动充放电功率

    int OTHER_TIME_MODEL_ADDRESS = 0x314E; // 其它时间段模式

    int POWER_ON_ADDRESS = 0x3C42; // 开机

    int POWER_OFF_ADDRESS = 0x3C44; // 关机

    int CLEAR_ALARM_ADDRESS = 0x3C46; // 故障清除

    int DEVICE_AMMETER_START_ADDRESS_1 = 0xA000;

    int DEVICE_AMMETER_START_ADDRESS_2 = 0xA100;

    int DEVICE_AMMETER_NUMBER = 0x2E;

    int DEVICE_BMS_START_ADDRESS_1 = 0xB000;

    int DEVICE_BMS_NUMBER_1 = 0x52;

//    int DEVICE_BMS_START_ADDRESS_2 = 0xB100;
    int DEVICE_BMS_START_ADDRESS_2 = 0xB0FC;

    int DEVICE_BMS_NUMBER_2 = 0x5C;

    int DEVICE_BMS_START_ADDRESS_3 = 0xB1FC;

    int DEVICE_BMS_START_ADDRESS_4 = 0xB300;

    int DEVICE_MONITOR_SETTING_START_ADDRESS_1 = 0xC000;

    int DEVICE_CHARGE_DISCHARGE_PERIOD_START_ADDRESS = 0xC014; // 充放电时间段起始地址

    int DEVICE_CHARGE_DISCHARGE_PERIOD_END_ADDRESS = 0xC042; // 充放电时间段结束地址

    int MONITOR_ALARM_ADDRESS = 0xC052; // 监控告警

    int REPEAT_CYCLE_ADDRESS = 0xC0B4; // 重复周数

    int EFFECTIVE_START_DATE_ADDRESS = 0xC0B6; // 定时充放生效起始日期

    int CHARGE_DISCHARGE_POWER_RANGE_START_ADDRESS = 0xC0BA; // 充放电功率段起始地址

    int CHARGE_DISCHARGE_POWER_RANGE_END_ADDRESS = 0xC0D0; // 充放电功率段结束地址

    int MAXIMUM_NUMBER_OF_SUPPORTED_PROTOCOLS = 6; // 协议支持最大时间段个数

    int MAXIMUM_PACKET_LENGTH = 512; // 单包最大长度（升级命令）

}
