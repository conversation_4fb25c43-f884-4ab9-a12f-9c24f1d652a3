package com.cdz360.mgc.model.hybridInverter.sinexcel;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum SinexcelOpCode {

    UNKNOWN(0x00, "未知"),
    STATUS_AND_ALARM(0x02, "读取设备的状态量、告警量"),
    ANALOG_AND_WAVEFORM(0x03, "读取设备的模拟量、波形数据（曲线、柱状图）、厂家信息"),
    SET_PARAMS(0x10, "设置设备的多个参数"),
    UPGRADE_CONNECT_DEVICE(0x20, "升级前连接设备"),
    UPGRADE_START(0x21, "启动升级命令（擦除Flash）"),
    UPGRADE_SEND_DATA(0x22, "发送升级数据"),
    UPGRADE_COMPLETED(0x23, "升级完成"),
    ;

    @JsonValue
    private final int code;

    private final String desc;

    SinexcelOpCode(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static SinexcelOpCode valueOf(Object codeIn) {
        if (codeIn == null) {
            return SinexcelOpCode.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof SinexcelOpCode) {
            return (SinexcelOpCode) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (SinexcelOpCode status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return SinexcelOpCode.UNKNOWN;
    }

}
