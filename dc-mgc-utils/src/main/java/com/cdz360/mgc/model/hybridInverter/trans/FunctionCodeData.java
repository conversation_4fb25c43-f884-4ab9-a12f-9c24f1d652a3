package com.cdz360.mgc.model.hybridInverter.trans;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * Modbus协议中功能码数据信息
 */
@Data
@Accessors(chain = true)
public class FunctionCodeData {

    /**
     * 功能码
     */
    @SerializedName("fc")
    @JsonProperty("fc")
    private int functionCode;

    /**
     * 寄存器占用字节数，默认1.
     * <p>如果数据项中没有指定“占用字节数”大小，则采用这个字段信息.</p>
     */
    @SerializedName("s")
    @JsonProperty("s")
    private int size = 1;

    /**
     * 功能码下涵盖数据项信息
     */
    @SerializedName("dis")
    @JsonProperty("dis")
    private List<DataItemValue> dataItemList;
}
