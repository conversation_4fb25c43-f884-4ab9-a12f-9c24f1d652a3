package com.cdz360.mgc.model.hybridInverter.sinexcel;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum ErrorReplyCode {

    UNKNOWN(0x999, "未知"),
    OP_CODE_ERROR(0x01, "功能码错误"),
    START_ADDRESS_ERROR(0x02, "数据地址错误"),
    DATA_ERROR(0x03, "数据错误"),
    CRC_ERROR(0x04, "校验码错误"),
    FLASH_ERASING_SUCCESS(0x05, "FLASH擦除成功"),
    FLASH_ERASING_FAILURE(0x06, "FLASH擦除失败"),
    FLASH_WRITE_SUCCESS(0x07, "写入FLASH成功"),
    FLASH_WRITE_FAILURE(0x08, "写入FLASH失败"),
    UPGRADE_SUCCESS(0x09, "升级成功"),
    UPGRADE_FAILURE(0x0A, "升级失败"),
    LENGTH_ERROR(0x0B, "长度错误"),
    SEQUENCE_ERROR(0x0C, "顺序错误"),
    ;

    @JsonValue
    private final int code;
    private final String desc;

    ErrorReplyCode(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static ErrorReplyCode valueOf(Object codeIn) {
        if (codeIn == null) {
            return ErrorReplyCode.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof ErrorReplyCode) {
            return (ErrorReplyCode) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (ErrorReplyCode type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return ErrorReplyCode.UNKNOWN;
    }


}
