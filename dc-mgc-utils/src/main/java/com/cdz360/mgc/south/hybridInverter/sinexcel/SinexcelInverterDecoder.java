package com.cdz360.mgc.south.hybridInverter.sinexcel;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.es.dto.EssFactoryDto;
import com.cdz360.base.model.es.type.ChargeFlowType;
import com.cdz360.base.model.es.type.PcsWorkMode;
import com.cdz360.base.model.es.type.hi.BatOpStatus;
import com.cdz360.base.model.es.type.hi.BmsBasicStatus;
import com.cdz360.base.model.es.type.hi.BmsHighVoltageAlarm;
import com.cdz360.base.model.es.type.hi.BmsHighVoltageFault;
import com.cdz360.base.model.es.type.hi.BmsHighVoltageProtection;
import com.cdz360.base.model.es.type.hi.BmsLowVoltageAlarm;
import com.cdz360.base.model.es.type.hi.BmsLowVoltageProtection;
import com.cdz360.base.model.es.type.hi.BmsProtocol;
import com.cdz360.base.model.es.type.hi.DcacStatus;
import com.cdz360.base.model.es.type.hi.HybridInverterOpStatus;
import com.cdz360.base.model.es.type.hi.HybridInverterVendor;
import com.cdz360.base.model.es.type.hi.MonitorPvType;
import com.cdz360.base.model.es.type.hi.MonitorPvVendor;
import com.cdz360.base.model.es.type.hi.PvOpStatus;
import com.cdz360.base.model.es.type.hi.SinexcelErrorCode;
import com.cdz360.base.model.es.type.hi.UpgradeFileType;
import com.cdz360.base.model.es.type.hi.UpgradeStatusCode;
import com.cdz360.base.model.es.vo.ChargeDisChargePeriods;
import com.cdz360.base.model.es.vo.hi.InverterRtData;
import com.cdz360.base.model.es.vo.hi.InverterRtInfo;
import com.cdz360.base.model.es.vo.hi.InverterSysData;
import com.cdz360.base.model.es.vo.hi.InverterUpgradeRes;
import com.cdz360.base.model.es.vo.hi.module.InverterDevMonitor;
import com.cdz360.base.model.es.vo.hi.module.ModuleAmmeterRtData;
import com.cdz360.base.model.es.vo.hi.module.ModuleBmsRtData;
import com.cdz360.base.model.es.vo.hi.module.ModuleBmsRtDataTemplate;
import com.cdz360.base.model.es.vo.hi.module.ModuleBmsRtDataTemplate.BatterySummary;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.ByteUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.mgc.model.SinexcelBasicInfo;
import com.cdz360.mgc.model.SinexcelBasicInfo.Command;
import com.cdz360.mgc.model.SinexcelBasicInfo.CommandType;
import com.cdz360.mgc.model.hybridInverter.InverterMsgDown;
import com.cdz360.mgc.model.hybridInverter.sinexcel.SinexcelConstants;
import com.cdz360.mgc.model.hybridInverter.sinexcel.SinexcelOpCode;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class SinexcelInverterDecoder {

    /**
     * 通用头部解析
     *
     * @return 是否为正确应答帧
     */
    protected boolean parseCommonInfo(String tid, String dno, InverterMsgDown msgDown, byte[] buf,
        boolean wLog) {
        int deviceId = msgDown.getDeviceId(); // 通讯地址
        int reqOpCode = msgDown.getOpCode().getCode(); // 请求帧功能码
        boolean valid = true;
        List<Integer> upgradeCodes = List.of(
            SinexcelOpCode.UPGRADE_CONNECT_DEVICE.getCode(), SinexcelOpCode.UPGRADE_START.getCode(),
            SinexcelOpCode.UPGRADE_SEND_DATA.getCode(), SinexcelOpCode.UPGRADE_COMPLETED.getCode());

        if (buf == null) {
            throw new DcServiceException("Modbus应答帧为空");
        } else if (buf[0] != (byte) (deviceId & 0xFF)) {
            throw new DcServiceException("Modbus应答帧地址不匹配");
        }

        // 校验功能码
        int resOpCode = buf[1] & 0xFF; // 应答帧功能码
        int resLength = buf[2] & 0xFF; // 应答帧长度
        int calcLength = resLength; // 用于核对CRC
        if (reqOpCode != resOpCode) {
            if ((reqOpCode + SinexcelConstants.BYTE_ERROR_RES_STEP) == resOpCode) {
                // 请求帧功能码 + 0x80 = 错误应答帧
                valid = false;
                calcLength = 0;
            } else {
                throw new DcServiceException("Modbus应答帧功能码不匹配");
            }
        } else if (resOpCode == SinexcelOpCode.SET_PARAMS.getCode()) {
            calcLength = 3;
        } else if (upgradeCodes.contains(resOpCode)) {
            resLength = ByteUtils.bytes2IntBE(buf, 2, 2);
            calcLength = 1 + resLength;
        }

        if (valid) {
            // 校验长度
            int expectLen = msgDown.getNum() * 2; // 期望的应答帧长度
            if (reqOpCode == SinexcelOpCode.STATUS_AND_ALARM.getCode()) {
                expectLen =
                    msgDown.getNum() % 8 == 0 ? msgDown.getNum() / 8 : msgDown.getNum() / 8 + 1;
            } else if (reqOpCode == SinexcelOpCode.SET_PARAMS.getCode()) {
                expectLen = msgDown.getNum();
                resLength = ByteUtils.bytes2IntBE(buf, 4, 2);
            } else if (upgradeCodes.contains(resOpCode)) {
                expectLen = 2; // 固定为2
            }

            if (!NumberUtils.equals(expectLen, resLength)) {
                log.warn(
                    "[{} {}] 地址 {} 返回的数据长度与期望不符!!! req.num = {}, res.length = {}",
                    tid, dno, msgDown.getStartAddress(), msgDown.getNum(), resLength);
                throw new DcServiceException("Modbus应答帧长度不匹配");
            }
        }

        // 校验CRC
        int crc = ByteUtils.crc16(buf, 3 + calcLength);
        int crcMsg = ByteUtils.bytes2IntLE(buf, 3 + calcLength, 2);    // 报文里的CRC
        if (crc != crcMsg) {
            if (wLog) {
                log.error("[{}] CRC校验不匹配. 报文crc = {}, 计算crc = {}", tid, crcMsg, crc);
            }
            throw new DcServiceException("CRC校验不匹配");
        }
        return valid;
    }

    /**
     * 检查上下行数据长度是否匹配
     *
     * @return true: 长度匹配; false: 长度不匹配
     */
    protected boolean lengthMatch(String tid, String dno, InverterMsgDown msgDown, Integer resNum) {
        int expectLen = msgDown.getNum() * 2;
        if (msgDown.getOpCode() == SinexcelOpCode.STATUS_AND_ALARM) {
            expectLen = msgDown.getNum() % 8 == 0 ? msgDown.getNum() / 8 : msgDown.getNum() / 8 + 1;
        } else if (msgDown.getOpCode() == SinexcelOpCode.SET_PARAMS) {
            return true;
        }
        if (!NumberUtils.equals(expectLen, resNum)) {
            log.warn(
                "[{} {}] 地址 {} 返回的数据长度与期望不符!!! req.length = {}, res.length = {}",
                tid, dno, msgDown.getStartAddress(), msgDown.getDownMsg(), resNum);
            return false;
        }
        return true;
    }

    public ObjectResponse<InverterRtInfo> parseOpStatus(String tid, String dno,
        InverterMsgDown msgDown, byte[] buf,
        boolean wLog) {
        boolean valid = parseCommonInfo(tid, dno, msgDown, buf, wLog);
        ObjectResponse<InverterRtInfo> dto = new ObjectResponse<>();
        if (!valid) {
            dto.setStatus(SinexcelConstants.MSG_PARSE_ERROR_RES);
            return dto;
        }

        InverterRtInfo rtInfo = new InverterRtInfo(HybridInverterVendor.SINEXCEL);
        int idx = 2;
        int length = buf[idx] & 0xFF;
        if (wLog) {
            log.debug("length = {}", length);
        }

        int data = ByteUtils.bytes2IntLE(buf, 3, length);
        /**
         * 初始化标志
         * 位个数1
         */
        int i = data & 0b1;
        rtInfo.setInitFlag(i == 1);
        data >>= 1;

        /**
         * 逆变器运行状态
         * 位个数3
         */
        i = data & 0b0111;
        rtInfo.setHybridInverterOpStatus(HybridInverterOpStatus.valueOf(i));
        data >>= 3;

        List<PvOpStatus> pvOpStatusList = new ArrayList<>();
        /**
         * 光伏1运行状态
         * 位个数1
         */
        i = (data & 0b1);
        pvOpStatusList.add(PvOpStatus.valueOf(i));
        data >>= 1;
        /**
         * 光伏2运行状态
         * 位个数1
         */
        i = (data & 0b1);
        pvOpStatusList.add(PvOpStatus.valueOf(i));
        data >>= 1;
        rtInfo.setPvOpStatus(pvOpStatusList);

        List<BatOpStatus> batOpStatuses = new ArrayList<>();
        /**
         * 电池1运行状态
         * 位个数3
         */
        i = data & 0b0111;
        batOpStatuses.add(BatOpStatus.valueOf(i));
        data >>= 3;
        /**
         * 电池2运行状态
         * 位个数3
         */
        i = data & 0b0111;
        batOpStatuses.add(BatOpStatus.valueOf(i));
        data >>= 3;
        rtInfo.setBatOpStatus(batOpStatuses);

        /**
         * DRED状态
         * 位个数4
         */
        i = data & 0b1111;
        rtInfo.setDredStatus(i);
        data >>= 4;

        /**
         * DCDC状态
         * 位个数1
         */
        i = (data & 0b1);
        rtInfo.setDcdcStatus(DcacStatus.valueOf(i));
        data >>= 1;

        /**
         * DCAC状态
         * 位个数1
         */
        i = (data & 0b1);
        rtInfo.setDcacStatus(DcacStatus.valueOf(i));
        data >>= 1;

        dto.setData(rtInfo);
        return dto;
    }

    public ListResponse<SinexcelErrorCode> parseErrorCode(String tid, String dno,
        InverterMsgDown msgDown,
        byte[] buf, boolean wLog) {
        Integer startAddress = msgDown.getStartAddress();
        Integer number = msgDown.getNum();
        boolean valid = parseCommonInfo(tid, dno, msgDown, buf, wLog);
        ListResponse<SinexcelErrorCode> dto = new ListResponse<>();
        if (!valid) {
            dto.setStatus(SinexcelConstants.MSG_PARSE_ERROR_RES);
            return dto;
        }

        int idx = 2;
        int length = buf[idx] & 0xFF;
        if (wLog) {
            log.debug("length = {}", length);
        }

        long data = ByteUtils.bytes2LongLE(buf, 3, length);
        if (data == 0) {
            return dto;
        }

        List<SinexcelErrorCode> sinexcelErrorList = new ArrayList<>();
        // 按位解析告警
        for (int i = 0; i < number; i++) {
            long temp = data & 0b0001;
            int code = startAddress + i;

            if (SinexcelErrorCode.RESERVED_ALARM_5.getCode() == code) {
                // 预留告警无需add()
            } else if (temp > 0) {
                sinexcelErrorList.add(SinexcelErrorCode.valueOf(code));
            }
            data >>= 1;
        }

//        int tempStatusAddress = startAddress;
//        do {
//            long i = data & 0b0001;
//            if (i > 0) {
//                sinexcelErrorList.add(SinexcelErrorCode.valueOf(tempStatusAddress));
//            }
//            data >>= 1;
//            tempStatusAddress += 1;
//            number--;
//        } while (number > 0);

        dto.setData(sinexcelErrorList.stream()
            .filter(e -> !SinexcelErrorCode.UNKNOWN.equals(e))
            .collect(Collectors.toList()));
        return dto;
    }

    public ListResponse<SinexcelErrorCode> parseMonitorError(String tid, String dno,
        InverterMsgDown msgDown,
        byte[] buf, boolean wLog) {
        boolean valid = parseCommonInfo(tid, dno, msgDown, buf, wLog);
        ListResponse<SinexcelErrorCode> dto = new ListResponse<>();
        if (!valid) {
            dto.setStatus(SinexcelConstants.MSG_PARSE_ERROR_RES);
            return dto;
        }

        List<SinexcelErrorCode> sinexcelErrorList = new ArrayList<>();

        long monitorAlarm = ByteUtils.bytes2LongBE(buf, 3, 4);
        this.parseMonitorAlarmByBit(sinexcelErrorList, monitorAlarm);

        dto.setData(sinexcelErrorList.stream()
            .filter(e -> !SinexcelErrorCode.UNKNOWN.equals(e))
            .collect(Collectors.toList()));
        return dto;
    }

    /**
     * 按位解析“监控告警”
     *
     * @param sinexcelErrorList
     * @param monitorAlarm
     */
    private void parseMonitorAlarmByBit(List<SinexcelErrorCode> sinexcelErrorList,
        long monitorAlarm) {
        if (monitorAlarm > 0) {
            int max = 4 * 8; // 监控告警共占4个字节
            int step = SinexcelErrorCode.DCDC_COMMUNICATION_FAILS.getCode();

            // 按位解析告警
            for (int i = 0; i < max; i++) {
                long temp = monitorAlarm & 0b0001;
                int code = step + i;

                if (temp > 0) {
                    sinexcelErrorList.add(SinexcelErrorCode.valueOf(code));
                }
                monitorAlarm >>= 1;
            }
        }
    }

    public SinexcelBasicInfo parseBasicInfo(boolean wLog, byte[] buf) {
        if (buf == null) {
            throw new DcServiceException("获取设备信息失败");
        }

        int idx = 0;
        int startByte = buf[idx++] & 0xFF;
        if (startByte != SinexcelConstants.BYTE_SINEXCEL_START) {
            throw new DcServiceException("获取设备信息失败..");
        }

        SinexcelBasicInfo info = new SinexcelBasicInfo();

        info.setCommandType(CommandType.valueOf(buf[idx++]));

        byte[] seq = Arrays.copyOfRange(buf, idx, idx + 4);
        idx += 4;
        info.setSeq(Arrays.toString(seq));

        info.setCommand(Command.valueOf(buf[idx++]));

        byte[] dataLength = Arrays.copyOfRange(buf, idx, idx + 2);
        idx += 2;
        info.setDataLength(ByteBuffer.wrap(dataLength).getLong());

        byte[] data = Arrays.copyOfRange(buf, idx, buf.length - 1);
        info.setData(data);

        return info;
    }

    // 这里是解析从0x1000地址开始的数据
    public ObjectResponse<InverterRtData> parseDeviceData(String tid, String dno,
        InverterMsgDown msgDown,
        boolean wLog,
        byte[] buf1, byte[] buf2, byte[] buf3, byte[] buf4) {
        boolean valid = parseCommonInfo(tid, dno, msgDown, buf1, wLog);
        valid = valid && parseCommonInfo(tid, dno, msgDown, buf2, wLog);
        valid = valid && parseCommonInfo(tid, dno, msgDown, buf3, wLog);
        valid = valid && parseCommonInfo(tid, dno, msgDown, buf4, wLog);
        ObjectResponse<InverterRtData> dto = new ObjectResponse<>();
        if (!valid) {
            dto.setStatus(SinexcelConstants.MSG_PARSE_ERROR_RES);
            return dto;
        }

        InverterRtData inverterRtData = new InverterRtData();
        int idx = 2;
        int buf1Len = buf1[idx] & 0xFF;
        int buf2Len = buf2[idx] & 0xFF;
        int buf3Len = buf3[idx] & 0xFF;
        int buf4Len = buf4[idx] & 0xFF;
        if (wLog) {
            log.debug("buf1Len = {}, buf2Len = {}, buf3Len = {}", buf1Len, buf2Len, buf3Len);
        }
        inverterRtData.setGridActivePowerA(getRealData(buf1, 24));
        inverterRtData.setGridActivePowerB(getRealData(buf1, 25));
        inverterRtData.setGridActivePowerC(getRealData(buf1, 26));
        inverterRtData.setLoadActivePowerA(getRealData(buf1, 59));
        inverterRtData.setLoadActivePowerB(getRealData(buf1, 60));
        inverterRtData.setLoadActivePowerC(getRealData(buf1, 61));

        inverterRtData.setInverterActivePowerA(getRealData(buf2, 36));
        inverterRtData.setInverterActivePowerB(getRealData(buf2, 37));
        inverterRtData.setInverterActivePowerC(getRealData(buf2, 38));

        inverterRtData.setPvVoltageList(List.of(getRealData(buf3, 1), getRealData(buf3, 4)));
        inverterRtData.setPvCurrentList(List.of(getRealData(buf3, 2), getRealData(buf3, 5)));
        inverterRtData.setPvPowerList(List.of(getRealData(buf3, 3), getRealData(buf3, 6)));
        inverterRtData.setBatVoltageList(List.of(getRealData(buf3, 7), getRealData(buf3, 12)));
        inverterRtData.setBatCurrentList(List.of(getRealData(buf3, 8), getRealData(buf3, 13)));
        inverterRtData.setBatPowerList(List.of(getRealData(buf3, 9), getRealData(buf3, 14)));
        inverterRtData.setBatCapacityPercentageList(
            List.of(getRealData(buf3, 10), getRealData(buf3, 15)));
        inverterRtData.setBatFullLoadSupportTimeList(
            List.of(getRealData(buf3, 11), getRealData(buf3, 16)));

        inverterRtData.setBatChargeToday(getRealData(buf4, 7));
        inverterRtData.setBatDischargeToday(getRealData(buf4, 8));
        dto.setData(inverterRtData);
        return dto;
    }

    /**
     * 从数据域取得真实的数据
     *
     * @param buf  返回的指令
     * @param item 第几条寄存器地址
     * @return
     */
    private BigDecimal getRealData(byte[] buf, int item) {
        //字节数量=寄存器数量*2
        float realData = Float.intBitsToFloat(
            (int) ByteUtils.bytes2LongBE(buf, 3 + (4 * (item - 1)), 4));
        return new BigDecimal(Float.toString(realData));
    }

    public ObjectResponse<InverterRtData> parseDeviceRtData(String tid, String dno,
        InverterMsgDown msgDown,
        byte[] buf, boolean wLog) {
        boolean valid = parseCommonInfo(tid, dno, msgDown, buf, wLog);
        ObjectResponse<InverterRtData> dto = new ObjectResponse<>();
        if (!valid) {
            dto.setStatus(SinexcelConstants.MSG_PARSE_ERROR_RES);
            return dto;
        }

        InverterRtData rtData = null;
        int startAddress = msgDown.getStartAddress();
        switch (startAddress) {
            case SinexcelConstants.DEVICE_REALTIME_DATA_QUEUE_START_ADDRESS_1:
                rtData = parseDeviceRtDataByAddress1(buf);
                break;
            case SinexcelConstants.DEVICE_REALTIME_DATA_QUEUE_START_ADDRESS_2:
                rtData = parseDeviceRtDataByAddress2(buf);
                break;
            case SinexcelConstants.DEVICE_REALTIME_DATA_QUEUE_START_ADDRESS_3:
                rtData = parseDeviceRtDataByAddress3(buf);
                break;
            case SinexcelConstants.DEVICE_REALTIME_DATA_QUEUE_START_ADDRESS_4:
                rtData = parseDeviceRtDataByAddress4(buf);
                break;
            case SinexcelConstants.DEVICE_REALTIME_DATA_QUEUE_START_ADDRESS_5:
                rtData = parseDeviceRtDataByAddress5(buf);
                break;
            default:
                break;
        }
        dto.setData(rtData);
        return dto;
    }

    private InverterRtData parseDeviceRtDataByAddress1(byte[] buf) {
        InverterRtData rtData = new InverterRtData();

        rtData.setGridVoltageA(getRealData(buf, 1))
            .setGridVoltageB(getRealData(buf, 2))
            .setGridVoltageC(getRealData(buf, 3))
            .setGridVoltageLineAB(getRealData(buf, 4))
            .setGridVoltageLineBC(getRealData(buf, 5))
            .setGridVoltageLineCA(getRealData(buf, 6))
            .setGridThduA(getRealData(buf, 7))
            .setGridThduB(getRealData(buf, 8))
            .setGridThduC(getRealData(buf, 9))
            .setGridVoltageFrequency(getRealData(buf, 10))
            .setGridCurrentEffectiveA(getRealData(buf, 11))
            .setGridCurrentEffectiveB(getRealData(buf, 12))
            .setGridCurrentEffectiveC(getRealData(buf, 13))
            .setGridCurrentEffectiveN(getRealData(buf, 14))
            .setGridCurrentThdA(getRealData(buf, 15))
            .setGridCurrentThdB(getRealData(buf, 16))
            .setGridCurrentThdC(getRealData(buf, 17))
            .setGridCurrentPeakRatioA(getRealData(buf, 18))
            .setGridCurrentPeakRatioB(getRealData(buf, 19))
            .setGridCurrentPeakRatioC(getRealData(buf, 20))
            .setGridApparentPowerA(getRealData(buf, 21))
            .setGridApparentPowerB(getRealData(buf, 22))
            .setGridApparentPowerC(getRealData(buf, 23))
            .setGridActivePowerA(getRealData(buf, 24))
            .setGridActivePowerB(getRealData(buf, 25))
            .setGridActivePowerC(getRealData(buf, 26))
            .setGridReactivePowerA(getRealData(buf, 27))
            .setGridReactivePowerB(getRealData(buf, 28))
            .setGridReactivePowerC(getRealData(buf, 29))
            .setGridFundamentalPowerFactorA(getRealData(buf, 30))
            .setGridFundamentalPowerFactorB(getRealData(buf, 31))
            .setGridFundamentalPowerFactorC(getRealData(buf, 32))
            .setGridPowerFactorA(getRealData(buf, 33))
            .setGridPowerFactorB(getRealData(buf, 34))
            .setGridPowerFactorC(getRealData(buf, 35))
        ;

        rtData
            .setLoadVoltageA(getRealData(buf, 36))
            .setLoadVoltageB(getRealData(buf, 37))
            .setLoadVoltageC(getRealData(buf, 38))
            .setLoadVoltageLineAB(getRealData(buf, 39))
            .setLoadVoltageLineBC(getRealData(buf, 40))
            .setLoadVoltageLineCA(getRealData(buf, 41))
            .setLoadThduA(getRealData(buf, 42))
            .setLoadThduB(getRealData(buf, 43))
            .setLoadThduC(getRealData(buf, 44))
            .setLoadVoltageFrequency(getRealData(buf, 45))
            .setLoadCurrentEffectiveA(getRealData(buf, 46))
            .setLoadCurrentEffectiveB(getRealData(buf, 47))
            .setLoadCurrentEffectiveC(getRealData(buf, 48))
            .setLoadCurrentEffectiveN(getRealData(buf, 49))
            .setLoadCurrentThdA(getRealData(buf, 50))
        ;
        return rtData;
    }


    private InverterRtData parseDeviceRtDataByAddress2(byte[] buf) {
        InverterRtData rtData = new InverterRtData();

        rtData.setLoadCurrentThdB(getRealData(buf, 1))
            .setLoadCurrentThdC(getRealData(buf, 2))
            .setLoadCurrentPeakRatioA(getRealData(buf, 3))
            .setLoadCurrentPeakRatioB(getRealData(buf, 4))
            .setLoadCurrentPeakRatioC(getRealData(buf, 5))
            .setLoadApparentPowerA(getRealData(buf, 6))
            .setLoadApparentPowerB(getRealData(buf, 7))
            .setLoadApparentPowerC(getRealData(buf, 8))
            .setLoadActivePowerA(getRealData(buf, 9))
            .setLoadActivePowerB(getRealData(buf, 10))
            .setLoadActivePowerC(getRealData(buf, 11))
            .setLoadReactivePowerA(getRealData(buf, 12))
            .setLoadReactivePowerB(getRealData(buf, 13))
            .setLoadReactivePowerC(getRealData(buf, 14))
            .setLoadFundamentalPowerFactorA(getRealData(buf, 15))
            .setLoadFundamentalPowerFactorB(getRealData(buf, 16))
            .setLoadFundamentalPowerFactorC(getRealData(buf, 17))
            .setLoadPowerFactorA(getRealData(buf, 18))
            .setLoadPowerFactorB(getRealData(buf, 19))
            .setLoadPowerFactorC(getRealData(buf, 20))
            .setLoadRateA(getRealData(buf, 21))
            .setLoadRateB(getRealData(buf, 22))
            .setLoadRateC(getRealData(buf, 23))
        ;

        rtData.setInverterVoltageA(getRealData(buf, 24))
            .setInverterVoltageB(getRealData(buf, 25))
            .setInverterVoltageC(getRealData(buf, 26))
            .setInverterVoltageLineAB(getRealData(buf, 27))
            .setInverterVoltageLineBC(getRealData(buf, 28))
            .setInverterVoltageLineCA(getRealData(buf, 29))
            .setInverterThduA(getRealData(buf, 30))
            .setInverterThduB(getRealData(buf, 31))
            .setInverterThduC(getRealData(buf, 32))
            .setInverterVoltageFrequency(getRealData(buf, 33))
            .setInverterCurrentEffectiveA(getRealData(buf, 34))
            .setInverterCurrentEffectiveB(getRealData(buf, 35))
            .setInverterCurrentEffectiveC(getRealData(buf, 36))
            .setInverterCurrentEffectiveN(getRealData(buf, 37))
            .setInverterCurrentThdA(getRealData(buf, 38))
            .setInverterCurrentThdB(getRealData(buf, 39))
            .setInverterCurrentThdC(getRealData(buf, 40))
            .setInverterCurrentPeakRatioA(getRealData(buf, 41))
            .setInverterCurrentPeakRatioB(getRealData(buf, 42))
            .setInverterCurrentPeakRatioC(getRealData(buf, 43))
            .setInverterApparentPowerA(getRealData(buf, 44))
            .setInverterApparentPowerB(getRealData(buf, 45))
            .setInverterApparentPowerC(getRealData(buf, 46))
            .setInverterActivePowerA(getRealData(buf, 47))
            .setInverterActivePowerB(getRealData(buf, 48))
            .setInverterActivePowerC(getRealData(buf, 49))
            .setInverterReactivePowerA(getRealData(buf, 50))
        ;

        return rtData;
    }


    private InverterRtData parseDeviceRtDataByAddress3(byte[] buf) {
        InverterRtData rtData = new InverterRtData();

        rtData
            .setInverterReactivePowerB(getRealData(buf, 1))
            .setInverterReactivePowerC(getRealData(buf, 2))
            .setInverterFundamentalPowerFactorA(getRealData(buf, 3))
            .setInverterFundamentalPowerFactorB(getRealData(buf, 4))
            .setInverterFundamentalPowerFactorC(getRealData(buf, 5))
            .setInverterPowerFactorA(getRealData(buf, 6))
            .setInverterPowerFactorB(getRealData(buf, 7))
            .setInverterPowerFactorC(getRealData(buf, 8))
            .setInverterRateA(getRealData(buf, 9))
            .setInverterRateB(getRealData(buf, 10))
            .setInverterRateC(getRealData(buf, 11))
        ;

        BigDecimal dcacTemp1 = getRealData(buf, 12);
        BigDecimal dcacTemp2 = getRealData(buf, 13);
        BigDecimal dcacTemp3 = getRealData(buf, 14);
        BigDecimal dcacTemp4 = getRealData(buf, 15);
        BigDecimal dcacTemp5 = getRealData(buf, 16);
        rtData.setDcacTempList(List.of(dcacTemp1, dcacTemp2,
            dcacTemp3, dcacTemp4,
            dcacTemp5));

        rtData.setDcacReserved1(getRealData(buf, 17))
            .setDcacReserved2(getRealData(buf, 18))
            .setDcacReserved3(getRealData(buf, 19));

        rtData.setPositiveDcBusVoltage(getRealData(buf, 20))
            .setNegativeDcBusVoltage(getRealData(buf, 21))
            .setYear(getRealData(buf, 22).intValue())
            .setMonth(getRealData(buf, 23).intValue())
            .setDay(getRealData(buf, 24).intValue())
            .setHour(getRealData(buf, 25).intValue())
            .setMinute(getRealData(buf, 26).intValue())
            .setSecond(getRealData(buf, 27).intValue())
        ;

        BigDecimal dcacDebugParam1 = getRealData(buf, 28);
        BigDecimal dcacDebugParam2 = getRealData(buf, 29);
        BigDecimal dcacDebugParam3 = getRealData(buf, 30);
        BigDecimal dcacDebugParam4 = getRealData(buf, 31);
        BigDecimal dcacDebugParam5 = getRealData(buf, 32);
        BigDecimal dcacDebugParam6 = getRealData(buf, 32);
        rtData.setDcacDebugParamList(List.of(dcacDebugParam1, dcacDebugParam2,
            dcacDebugParam3, dcacDebugParam4,
            dcacDebugParam5, dcacDebugParam6));

        return rtData;
    }


    private InverterRtData parseDeviceRtDataByAddress4(byte[] buf) {
        InverterRtData rtData = new InverterRtData();

        rtData
            .setPvVoltageList(List.of(getRealData(buf, 1), getRealData(buf, 4)))
            .setPvCurrentList(List.of(getRealData(buf, 2), getRealData(buf, 5)))
            .setPvPowerList(List.of(getRealData(buf, 3), getRealData(buf, 6)))
            .setBatVoltageList(List.of(getRealData(buf, 7), getRealData(buf, 12)))
            .setBatCurrentList(List.of(getRealData(buf, 8), getRealData(buf, 13)))
            .setBatPowerList(List.of(getRealData(buf, 9), getRealData(buf, 14)))
            .setBatCapacityPercentageList(List.of(getRealData(buf, 10), getRealData(buf, 15)))
            .setBatFullLoadSupportTimeList(List.of(getRealData(buf, 11), getRealData(buf, 16)))
        ;

        BigDecimal dcdcTemp1 = getRealData(buf, 17);
        BigDecimal dcdcTemp2 = getRealData(buf, 18);
        BigDecimal dcdcTemp3 = getRealData(buf, 19);
        BigDecimal dcdcTemp4 = getRealData(buf, 20);
        BigDecimal dcdcTemp5 = getRealData(buf, 21);
        rtData.setDcdcTempList(List.of(dcdcTemp1, dcdcTemp2,
            dcdcTemp3, dcdcTemp4,
            dcdcTemp5));

        rtData.setDcdcReservedList(List.of(getRealData(buf, 22),
            getRealData(buf, 23),
            getRealData(buf, 24)));

        rtData.setPositiveDcBusVoltage2(getRealData(buf, 25))
            .setNegativeDcBusVoltage2(getRealData(buf, 26))
            .setBalancedCircuitCurrent(getRealData(buf, 27))
            .setFanGear(getRealData(buf, 28));

        int bat1ChargeTimes = getRealData(buf, 29).intValue();
        int bat1DischargeTimes = getRealData(buf, 30).intValue();

        int bat2ChargeTimes = getRealData(buf, 31).intValue();
        int bat2DischargeTimes = getRealData(buf, 32).intValue();
        rtData.setBatChargeTimesList(List.of(bat1ChargeTimes, bat2ChargeTimes))
            .setBatDischargeTimesList(List.of(bat1DischargeTimes, bat2DischargeTimes))
            .setPurchaseElecAll(getRealData(buf, 33))
            .setFeedEelcAll(getRealData(buf, 34))
            .setPvLoadRateList(List.of(getRealData(buf, 35), getRealData(buf, 36)))
        ;

        return rtData;
    }


    private InverterRtData parseDeviceRtDataByAddress5(byte[] buf) {
        InverterRtData rtData = new InverterRtData();

        rtData.setBatLoadRateList(List.of(getRealData(buf, 1),
            getRealData(buf, 2)));

        BigDecimal dcdcDebugParam1 = getRealData(buf, 3);
        BigDecimal dcdcDebugParam2 = getRealData(buf, 4);
        BigDecimal dcdcDebugParam3 = getRealData(buf, 5);
        BigDecimal dcdcDebugParam4 = getRealData(buf, 6);
        BigDecimal dcdcDebugParam5 = getRealData(buf, 7);
        BigDecimal dcdcDebugParam6 = getRealData(buf, 8);
        rtData.setDcdcDebugParamList(List.of(dcdcDebugParam1, dcdcDebugParam2,
            dcdcDebugParam3, dcdcDebugParam4,
            dcdcDebugParam5, dcdcDebugParam6));

        rtData.setDcConverterVoltage(getRealData(buf, 9))
            .setDcConverterCurrent(getRealData(buf, 10))
            .setKwhAll(getRealData(buf, 11))
            .setBatChargeAll(getRealData(buf, 12))
            .setBatDischargeAll(getRealData(buf, 13))
            .setLoadKwhAll(getRealData(buf, 14))
            .setKwhToday(getRealData(buf, 15))
            .setPurchaseElecToday(getRealData(buf, 16))
            .setFeedEelcToday(getRealData(buf, 17))
            .setBatChargeToday(getRealData(buf, 18))
            .setBatDischargeToday(getRealData(buf, 19))
            .setLoadKwhToday(getRealData(buf, 20))
            .setPvPowerAll(getRealData(buf, 21))
            .setBatPowerAll(getRealData(buf, 22))
            .setLoadPowerAll(getRealData(buf, 23))
            .setGridPowerAll(getRealData(buf, 24))
            .setCriticalLoadPowerAll(getRealData(buf, 25))
            .setGeneralLoadPowerAll(getRealData(buf, 26))
        ;

        BigDecimal pvVoltage3 = getRealData(buf, 27);
        BigDecimal pvCurrent3 = getRealData(buf, 28);
        BigDecimal pvPower3 = getRealData(buf, 29);
        BigDecimal pvVoltage4 = getRealData(buf, 30);
        BigDecimal pvCurrent4 = getRealData(buf, 31);
        BigDecimal pvPower4 = getRealData(buf, 32);
        rtData.setPvVoltageList(this.itemMap(pvVoltage3, pvVoltage4))
            .setPvCurrentList(this.itemMap(pvCurrent3, pvCurrent4))
            .setPvPowerList(this.itemMap(pvPower3, pvPower4))
        ;
        BigDecimal pvLoadRate3 = getRealData(buf, 33);
        BigDecimal pvLoadRate4 = getRealData(buf, 34);
        rtData.setPvLoadRateList(this.itemMap(pvLoadRate3, pvLoadRate4));

        return rtData;
    }

    private List<BigDecimal> itemMap(BigDecimal item3, BigDecimal item4) {
        List<BigDecimal> list = new ArrayList<>();
        list.add(null);
        list.add(null);
        list.add(item3);
        list.add(item4);
        return list;
    }

    public ObjectResponse<EssFactoryDto> parseDeviceFactoryInfo(String tid, String dno,
        InverterMsgDown msgDown,
        byte[] buf, boolean wLog) {
        boolean valid = parseCommonInfo(tid, dno, msgDown, buf, wLog);
        ObjectResponse<EssFactoryDto> dto = new ObjectResponse<>();
        if (!valid) {
            dto.setStatus(SinexcelConstants.MSG_PARSE_ERROR_RES);
            return dto;
        }

        EssFactoryDto factoryDto = new EssFactoryDto();
        int idx = 3;

        int protocolVer = ByteUtils.bytes2IntBE(buf, idx, 2);
        if (protocolVer > 0) {
            String subEdition = String.valueOf(protocolVer & 0x0F);
            String majorEdition = String.valueOf(protocolVer >> 4);
            factoryDto.setProtocolVer(majorEdition.concat(".").concat(subEdition));
        }
        idx += 2;

        int temp1 = ByteUtils.bytes2IntBE(buf, idx, 2);
        if (temp1 > 0) {
            String subEdition = String.valueOf(temp1 & 0x0F);
            String majorEdition = String.valueOf(temp1 >> 4);
            factoryDto.setDsp1SoftwareVer(majorEdition.concat(".").concat(subEdition));
        }
        idx += 2;

        int temp2 = ByteUtils.bytes2IntBE(buf, idx, 2);
        if (temp2 > 0) {
            String subEdition = String.valueOf(temp2 & 0x0F);
            String majorEdition = String.valueOf(temp2 >> 4);
            factoryDto.setDsp2SoftwareVer(majorEdition.concat(".").concat(subEdition));
        }
        idx += 2;

        int temp3 = ByteUtils.bytes2IntBE(buf, idx, 2);
        if (temp3 > 0) {
            String subEdition = String.valueOf(temp3 & 0x0F);
            String majorEdition = String.valueOf(temp3 >> 4);
            factoryDto.setArmSoftwareVer(majorEdition.concat(".").concat(subEdition));
        }
        idx += 2;

        int cpldVer = ByteUtils.bytes2IntBE(buf, idx, 2);
        if (cpldVer > 0) {
            String subEdition = String.valueOf(cpldVer & 0x0F);
            String majorEdition = String.valueOf(cpldVer >> 4);
            factoryDto.setCpldVer(majorEdition.concat(".").concat(subEdition));
        }
        idx += 2;

//        int temp4 = LiteByteUtil.bytes2IntBE(buf, idx, 2);
//        if (temp4 > 0) {
//            String subEdition = String.valueOf(temp4 & 0x0F);
//            String majorEdition = String.valueOf(temp4 >> 4);
//            factoryDto.setAfciSoftwareVer(majorEdition.concat(".").concat(subEdition));
//        }
//        idx += 2;

//        int typeCode = LiteByteUtil.bytes2IntBE(buf, idx, 2);
//        factoryDto.setType(EssType.valueOf(typeCode));
//        idx += 2;

        dto.setData(factoryDto);
        return dto;
    }

    public ModuleAmmeterRtData parseAmmeterDataByAddress2(boolean wLog, int deviceId, byte[] buf) {
//        parseCommonInfo(wLog, deviceId, SinexcelOpCode.ANALOG_AND_WAVEFORM.getCode(), buf);

        ModuleAmmeterRtData rtData = new ModuleAmmeterRtData();
        rtData.setVoltageA(getRealData(buf, 1))
            .setVoltageB(getRealData(buf, 2))
            .setVoltageC(getRealData(buf, 3))
            .setCurrentA(getRealData(buf, 4))
            .setCurrentB(getRealData(buf, 5))
            .setCurrentC(getRealData(buf, 6))
            .setApparentPowerA(getRealData(buf, 7))
            .setApparentPowerB(getRealData(buf, 8))
            .setApparentPowerC(getRealData(buf, 9))
            .setActivePowerA(getRealData(buf, 10))
            .setActivePowerB(getRealData(buf, 11))
            .setActivePowerC(getRealData(buf, 12))
            .setReactivePowerA(getRealData(buf, 13))
            .setReactivePowerB(getRealData(buf, 14))
            .setReactivePowerC(getRealData(buf, 15))
            .setPowerFactorA(getRealData(buf, 16))
            .setPowerFactorB(getRealData(buf, 17))
            .setPowerFactorC(getRealData(buf, 18))
            .setCombinedActiveTotalElectricalEnergy(getRealData(buf, 19))
            .setForwardActiveEnergy(getRealData(buf, 20))
            .setBackwardActiveEnergy(getRealData(buf, 21))
            .setForwardReactiveEnergy(getRealData(buf, 22))
            .setBackwardReactiveEnergy(getRealData(buf, 23))
        ;
        return rtData;
    }


    public ModuleBmsRtData parseBmsDataByAddress1(boolean wLog, int deviceId,
        List<BmsProtocol> protocols, byte[] buf) {
//        parseCommonInfo(wLog, deviceId, SinexcelOpCode.ANALOG_AND_WAVEFORM.getCode(), buf);

        AtomicReference<BmsProtocol> protocolAto1 = new AtomicReference<>(null);
        AtomicReference<BmsProtocol> protocolAto2 = new AtomicReference<>(null);
        Optional.ofNullable(protocols)
            .filter(CollectionUtils::isNotEmpty)
            .ifPresent(e -> {
                if (e.get(0) != null) {
                    protocolAto1.set(e.get(0));
                }
                if (e.size() > 1 && e.get(1) != null) {
                    protocolAto2.set(e.get(1));
                }
            });

        int highVoltageFaultsMaxBit = 8;
        int highVoltageAlarmsMaxBit = 16;
        int highVoltageProtectionsMaxBit = 13;
        int lowVoltageAlarmsMaxBit = 12;
        int lowVoltageProtectionsMaxBit = 12;

        ModuleBmsRtData rtData = new ModuleBmsRtData();

        ModuleBmsRtDataTemplate template1 = new ModuleBmsRtDataTemplate();
        template1.setOpStatus(DecimalUtils.eq(BigDecimal.ONE, getRealData(buf, 1)))
            .setStatus(this.calcBmsBasicStatus(getRealData(buf, 2)))
            .setCycle(getRealData(buf, 3))
            .setHighVoltageFaults(
                this.calcValues(getRealData(buf, 4).longValue(), highVoltageFaultsMaxBit,
                    protocolAto1.get(), BmsHighVoltageFault.class))
            .setHighVoltageAlarms(
                this.calcValues(getRealData(buf, 5).longValue(), highVoltageAlarmsMaxBit,
                    protocolAto1.get(), BmsHighVoltageAlarm.class))
            .setHighVoltageProtections(
                this.calcValues(getRealData(buf, 6).longValue(), highVoltageProtectionsMaxBit,
                    protocolAto1.get(), BmsHighVoltageProtection.class))
//            .setLowVoltageFaults(getRealData(buf, 7))
            .setLowVoltageAlarms(
                this.calcValues(getRealData(buf, 8).longValue(), lowVoltageAlarmsMaxBit,
                    protocolAto1.get(), BmsLowVoltageAlarm.class))
            .setLowVoltageProtections(
                this.calcValues(getRealData(buf, 9).longValue(), lowVoltageProtectionsMaxBit,
                    protocolAto1.get(), BmsLowVoltageProtection.class))
        ;

        ModuleBmsRtDataTemplate template2 = new ModuleBmsRtDataTemplate();
        template2.setOpStatus(DecimalUtils.eq(BigDecimal.ONE, getRealData(buf, 33)))
            .setStatus(this.calcBmsBasicStatus(getRealData(buf, 34)))
            .setCycle(getRealData(buf, 35))
            .setHighVoltageFaults(
                this.calcValues(getRealData(buf, 36).longValue(), highVoltageFaultsMaxBit,
                    protocolAto2.get(), BmsHighVoltageFault.class))
            .setHighVoltageAlarms(
                this.calcValues(getRealData(buf, 37).longValue(), highVoltageAlarmsMaxBit,
                    protocolAto2.get(), BmsHighVoltageAlarm.class))
            .setHighVoltageProtections(
                this.calcValues(getRealData(buf, 38).longValue(), highVoltageProtectionsMaxBit,
                    protocolAto2.get(), BmsHighVoltageProtection.class))
//            .setLowVoltageFaults(getRealData(buf, 39))
            .setLowVoltageAlarms(
                this.calcValues(getRealData(buf, 40).longValue(), lowVoltageAlarmsMaxBit,
                    protocolAto2.get(), BmsLowVoltageAlarm.class))
            .setLowVoltageProtections(
                this.calcValues(getRealData(buf, 41).longValue(), lowVoltageProtectionsMaxBit,
                    protocolAto2.get(), BmsLowVoltageProtection.class))
        ;

        rtData.setBatteryCupboardList(List.of(template1, template2));
        return rtData;
    }

    private List<BmsBasicStatus> calcBmsBasicStatus(BigDecimal val) {
        if (val == null) {
            return null;
        }
        int valInt = val.intValue();
        List<BmsBasicStatus> collect = new ArrayList<>();

        int tempBasicStatus = valInt & 0b111;
        BmsBasicStatus basicStatus1 = BmsBasicStatus.valueOf(tempBasicStatus + 1); // 自定义适配逻辑
        valInt >>= 3;

        if ((valInt & 0b1) == 1) {
            collect.add(BmsBasicStatus.REQUEST_STRONG_CHARGE);
        }
        valInt >>= 1;

        if ((valInt & 0b1) == 1) {
            collect.add(BmsBasicStatus.REQUEST_EQUAL_CHARGE);
        }
        valInt >>= 1;

        if ((valInt & 0b1) == 1) {
            collect.add(BmsBasicStatus.CHARGE_PROHIBITION);
        }
        valInt >>= 1;

        if ((valInt & 0b1) == 1) {
            collect.add(BmsBasicStatus.DISCHARGE_PROHIBITION);
        }
        valInt >>= 1;

        if (CollectionUtils.isEmpty(collect)
            && basicStatus1 != null && !BmsBasicStatus.UNKNOWN.equals(basicStatus1)) {
            collect.add(basicStatus1);
        }
        return Optional.of(collect)
            .filter(CollectionUtils::isNotEmpty)
            .orElse(List.of(BmsBasicStatus.UNKNOWN));
    }

    private <T extends Enum<T>> List<T> calcValues(long val, int maxBit,
        BmsProtocol protocol, Class<T> clazz) {
        int stepValue = BmsProtocol.getProtocolStepValue(protocol);

        List<T> values = new ArrayList<>();
        // 按位解析值
        for (int i = 0; i < maxBit; i++) {
            long temp = val & 0b0001;
            int code = i + 1;

            if (temp > 0) {
                try {
                    Method valueOfMethod = clazz.getDeclaredMethod("valueOf", Object.class);
                    T invokeValue = (T) valueOfMethod.invoke(clazz, code + stepValue);
                    if (invokeValue != null &&
                        !Enum.valueOf(clazz, "UNKNOWN").equals(invokeValue)) {
                        values.add(invokeValue);
                    }
                } catch (Exception ex) {
                    log.error("calcValues error: {}", ex.getMessage(), ex);
                }
            }
            val >>= 1;
        }
        return values;
    }

    public ModuleBmsRtDataTemplate parseBmsDataByAddress2(boolean wLog, int deviceId, byte[] buf) {
//        parseCommonInfo(wLog, deviceId, SinexcelOpCode.ANALOG_AND_WAVEFORM.getCode(), buf);

        ModuleBmsRtDataTemplate template = new ModuleBmsRtDataTemplate();
        template
            .setChargeEnergyAll(getRealData(buf, 1))
            .setDischargeEnergyAll(getRealData(buf, 2))
            .setBatteryPackVoltageAll(getRealData(buf, 3))
            .setBatteryPackCurrentAll(getRealData(buf, 4))
            .setTemp(getRealData(buf, 5))
            .setSoc(getRealData(buf, 6))
            .setSoh(getRealData(buf, 7));

        BigDecimal maxVoltage = getRealData(buf, 8);
        BigDecimal minVoltage = getRealData(buf, 9);
        int maxVoltageNum = getRealData(buf, 10).intValue();
        int minVoltageNum = getRealData(buf, 11).intValue();

        BigDecimal maxTemp = getRealData(buf, 12);
        BigDecimal minTemp = getRealData(buf, 13);
        int maxTempNum = getRealData(buf, 14).intValue();
        int minTempNum = getRealData(buf, 15).intValue();

        template.setMaxBatteryVoltage(new BatterySummary(maxVoltageNum, maxVoltage))
            .setMinBatteryVoltage(new BatterySummary(minVoltageNum, minVoltage))
            .setMaxBatteryTemp(new BatterySummary(maxTempNum, maxTemp))
            .setMinBatteryTemp(new BatterySummary(minTempNum, minTemp))
            .setChargingCurrentLimiting(getRealData(buf, 16))
            .setDischargingCurrentLimiting(getRealData(buf, 17))
            .setChargingVoltageLimiting(getRealData(buf, 18))
            .setDischargingVoltageLimiting(getRealData(buf, 19));

        List<BigDecimal> cellVoltageList = IntStream.range(20, 36)
            .mapToObj(e -> getRealData(buf, e))
            .takeWhile(DecimalUtils::gtZero)
            .collect(Collectors.toList());
        template.setCellVoltageList(cellVoltageList);

        List<BigDecimal> cellTempList = IntStream.range(36, 40).mapToObj(e -> getRealData(buf, e))
            .takeWhile(DecimalUtils::gtZero)
            .collect(Collectors.toList());
        template.setCellTempList(cellTempList)
            .setMosTemp(getRealData(buf, 40))
            .setEnvTemp(getRealData(buf, 41))
            .setOnlineBatteryCount(getRealData(buf, 42).intValue())
            .setForbiddenChargingBatteryCount(getRealData(buf, 43).intValue())
            .setForbiddenDischargingBatteryCount(getRealData(buf, 44).intValue())
            .setOfflineBatteryCount(getRealData(buf, 45).intValue())
            .setInstalledCapacity(getRealData(buf, 46))
        ;

        return template;
    }

    private int getOffset(int item) {
        return 3 + (4 * (item - 1));
    }

    public ModuleBmsRtData parseBmsDataByAddress4(boolean wLog, int deviceId, byte[] buf) {
//        parseCommonInfo(wLog, deviceId, SinexcelOpCode.ANALOG_AND_WAVEFORM.getCode(), buf);

        ModuleBmsRtDataTemplate template1 = new ModuleBmsRtDataTemplate();
        ModuleBmsRtDataTemplate template2 = new ModuleBmsRtDataTemplate();

        template1.setProtocol(BmsProtocol.valueOf(getRealData(buf, 1).intValue()));

        int offset = this.getOffset(2);
        long version1 = ByteUtils.bytes2LongBE(buf, offset, 4);
        if (version1 > 0) {
            String subEdition = String.valueOf(version1 & 0x0F);
            String majorEdition = String.valueOf(version1 >> 4);
            template1.setSoftwareVer(majorEdition.concat(".").concat(subEdition));
        }

        template2.setProtocol(BmsProtocol.valueOf(getRealData(buf, 3).intValue()));

        offset = this.getOffset(4);
        long version2 = ByteUtils.bytes2LongBE(buf, offset, 4);
        if (version2 > 0) {
            String subEdition = String.valueOf(version2 & 0x0F);
            String majorEdition = String.valueOf(version2 >> 4);
            template2.setSoftwareVer(majorEdition.concat(".").concat(subEdition));
        }

        offset = this.getOffset(5);
        long time = ByteUtils.bytes2LongBE(buf, offset, 4);
        if (time > 0) {
            template1.setFirstActivationTime(new Date(time * 1000));
        }

        offset = this.getOffset(6);
        template1.setSerialNumber(ByteUtils.byteBuf2Ascii(buf, offset, 16));
        offset = this.getOffset(10);
        template1.setHardwareVer(ByteUtils.byteBuf2Ascii(buf, offset, 8));
        offset = this.getOffset(12);
        template1.setSoftwareVerAscii(ByteUtils.byteBuf2Ascii(buf, offset, 24));
/*
        List<String> softwareVerList1 = IntStream.range(12, 18)
            .map(this::getOffset)
            .mapToObj(oSet -> LiteByteUtil.byteBuf2Ascii(buf, oSet, 4))
            .takeWhile(Objects::nonNull)
            .collect(Collectors.toList());
        if (!softwareVerList1.stream().allMatch(String::isEmpty)) {
            template1.setSoftwareVerList(softwareVerList1);
        }
        */

        offset = this.getOffset(21);
        time = ByteUtils.bytes2LongBE(buf, offset, 4);
        if (time > 0) {
            template2.setFirstActivationTime(new Date(time * 1000));
        }

        offset = this.getOffset(22);
        template2.setSerialNumber(ByteUtils.byteBuf2Ascii(buf, offset, 16));
        offset = this.getOffset(26);
        template2.setHardwareVer(ByteUtils.byteBuf2Ascii(buf, offset, 8));
        offset = this.getOffset(28);
        template2.setSoftwareVerAscii(ByteUtils.byteBuf2Ascii(buf, offset, 24));
/*
        List<String> softwareVerList2 = IntStream.range(28, 34)
            .map(this::getOffset)
            .mapToObj(oSet -> LiteByteUtil.byteBuf2Ascii(buf, oSet, 4))
            .takeWhile(Objects::nonNull)
            .collect(Collectors.toList());
        if (!softwareVerList2.stream().allMatch(String::isEmpty)) {
            template2.setSoftwareVerList(softwareVerList2);
        }
        */

        ModuleBmsRtData rtData = new ModuleBmsRtData();
        rtData.setBatteryCupboardList(List.of(template1, template2));
        return rtData;
    }

    public InverterSysData parseDeviceSysDataByAddress1(byte[] buf) {
        InverterSysData inverterSysData = new InverterSysData();
        inverterSysData.setWorkModeSetting(PcsWorkMode.valueOf(getRealData(buf, 7).intValue()));
        inverterSysData.setActivePower(getRealData(buf, 39));
        return inverterSysData;
    }

    public InverterSysData parseDeviceSysDataByAddress2(byte[] buf) {
        InverterSysData inverterSysData = new InverterSysData();
        Optional.ofNullable(getRealData(buf, 1))
            .ifPresent(x -> inverterSysData.setOtherStrategy(x.intValue()));
        return inverterSysData;
    }

    public InverterDevMonitor parseDevMonitorByAddress1(byte[] buf) {
        InverterDevMonitor monitor = new InverterDevMonitor();

        monitor
            .setClearKwhAll(getRealData(buf, 1).intValue())
            .setClearKwhAll(getRealData(buf, 2).intValue())
            .setMeterCtRatio(getRealData(buf, 3))
            .setMeterModel(getRealData(buf, 4).intValue())
            .setBatteryProtocolList(List.of(BmsProtocol.valueOf(getRealData(buf, 5).longValue()),
                BmsProtocol.valueOf(getRealData(buf, 6).longValue())))
            .setPvType(MonitorPvType.valueOf(getRealData(buf, 7).longValue()))
            .setPvVendor(MonitorPvVendor.valueOf(getRealData(buf, 8).longValue()))
//            .setParallelSetup(DecimalUtils.gtZero(getRealData(buf, 9)))
            .setFactoryDataReset(DecimalUtils.gtZero(getRealData(buf, 10)));

        ChargeDisChargePeriods p1 = new ChargeDisChargePeriods(
            getRealData(buf, 11).intValue(), getRealData(buf, 12).intValue(),
            ChargeFlowType.CHARGE);
        ChargeDisChargePeriods p2 = new ChargeDisChargePeriods(
            getRealData(buf, 13).intValue(), getRealData(buf, 14).intValue(),
            ChargeFlowType.DISCHARGE);
        ChargeDisChargePeriods p3 = new ChargeDisChargePeriods(
            getRealData(buf, 15).intValue(), getRealData(buf, 16).intValue(),
            ChargeFlowType.CHARGE);
        ChargeDisChargePeriods p4 = new ChargeDisChargePeriods(
            getRealData(buf, 17).intValue(), getRealData(buf, 18).intValue(),
            ChargeFlowType.DISCHARGE);
        ChargeDisChargePeriods p5 = new ChargeDisChargePeriods(
            getRealData(buf, 19).intValue(), getRealData(buf, 20).intValue(),
            ChargeFlowType.CHARGE);
        ChargeDisChargePeriods p6 = new ChargeDisChargePeriods(
            getRealData(buf, 21).intValue(), getRealData(buf, 22).intValue(),
            ChargeFlowType.DISCHARGE);
        ChargeDisChargePeriods p7 = new ChargeDisChargePeriods(
            getRealData(buf, 23).intValue(), getRealData(buf, 24).intValue(),
            ChargeFlowType.CHARGE);
        ChargeDisChargePeriods p8 = new ChargeDisChargePeriods(
            getRealData(buf, 25).intValue(), getRealData(buf, 26).intValue(),
            ChargeFlowType.DISCHARGE);
        ChargeDisChargePeriods p9 = new ChargeDisChargePeriods(
            getRealData(buf, 27).intValue(), getRealData(buf, 28).intValue(),
            ChargeFlowType.CHARGE);
        ChargeDisChargePeriods p10 = new ChargeDisChargePeriods(
            getRealData(buf, 29).intValue(), getRealData(buf, 30).intValue(),
            ChargeFlowType.DISCHARGE);
        ChargeDisChargePeriods p11 = new ChargeDisChargePeriods(
            getRealData(buf, 31).intValue(), getRealData(buf, 32).intValue(),
            ChargeFlowType.CHARGE);
        ChargeDisChargePeriods p12 = new ChargeDisChargePeriods(
            getRealData(buf, 33).intValue(), getRealData(buf, 34).intValue(),
            ChargeFlowType.DISCHARGE);
        monitor.setPeriods(
            List.of(p1, p2, p3, p4, p5, p6, p7, p8, p9, p10, p11, p12));

//        monitor.setOpStatus(DecimalUtils.gtZero(getRealData(buf, 41)));

        monitor.setAlarmList(this.calcMonitorAlarms(getRealData(buf, 42).longValue()));
        return monitor;
    }

    public InverterDevMonitor parseDevMonitorByAddress2(byte[] buf) {
        InverterDevMonitor monitor = new InverterDevMonitor();

        int offset = this.getOffset(1);
        long repeatCycle = ByteUtils.bytes2LongBE(buf, offset, 4);
        offset = this.getOffset(2);
        long startTime = ByteUtils.bytes2LongBE(buf, offset, 4);
        offset = this.getOffset(3);
        long endTime = ByteUtils.bytes2LongBE(buf, offset, 4);

        monitor
            .setRepeatCycle((int) repeatCycle)
            .setEffectiveStartTime(startTime)
            .setEffectiveEndTime(endTime)
            .setChargingPowerPeriods(List.of(getRealData(buf, 4),
                getRealData(buf, 6),
                getRealData(buf, 8),
                getRealData(buf, 10),
                getRealData(buf, 12),
                getRealData(buf, 14)
            ))
            .setDischargingPowerPeriods(List.of(getRealData(buf, 5),
                getRealData(buf, 7),
                getRealData(buf, 9),
                getRealData(buf, 11),
                getRealData(buf, 13),
                getRealData(buf, 15)
            ))
        ;
        return monitor;
    }

    private List<SinexcelErrorCode> calcMonitorAlarms(long val) {

        List<SinexcelErrorCode> sinexcelErrorList = new ArrayList<>();
        this.parseMonitorAlarmByBit(sinexcelErrorList, val);

        List<SinexcelErrorCode> collect = sinexcelErrorList.stream()
            .filter(e -> !SinexcelErrorCode.UNKNOWN.equals(e))
            .collect(Collectors.toList());

        return Optional.of(collect)
            .filter(CollectionUtils::isNotEmpty)
            .orElse(null);
    }

    protected BaseResponse parseParamSetMsg(String tid, String dno, InverterMsgDown msgDown,
        byte[] buf, boolean wLog) {
        /*
        int code = msgDown.getOpCode().getCode();
        BaseResponse res = new BaseResponse();
        if (buf == null) {
            return new BaseResponse(DcConstants.KEY_RES_CODE_ARGUMENT_ERROR, "buf参数为空");
        } else if (buf[0] != (byte) (msgDown.getDeviceId() & 0xFF)) {
            return new BaseResponse(DcConstants.KEY_RES_CODE_SERVICE_ERROR, "通讯地址不匹配");
        } else if (buf[1] == (code + 0x80)) {
            // 错误应答帧 = 请求帧功能码 + 0x80
            ErrorReplyCode errorReplyCode = ErrorReplyCode.valueOf(buf[2] & 0xFF);
            return new BaseResponse(errorReplyCode.getCode(), errorReplyCode.getDesc());
        } else if (buf[1] != code) {
            return new BaseResponse(DcConstants.KEY_RES_CODE_SERVICE_ERROR, "功能码地址不匹配");
        }

        Integer len = buf[2] & 0xFF;
        if (!lengthMatch(tid, dno, msgDown, len)) {
            return new BaseResponse(DcConstants.KEY_RES_CODE_SERVICE_ERROR, "上下行数据长度不匹配");
        }

        int crc;
        int crcMsg;
        if (SinexcelOpCode.SET_PARAMS.getCode() == code) {
            int headAndBody = 6;
            crc = LiteByteUtil.crc16(buf, headAndBody);
            crcMsg = LiteByteUtil.bytes2IntLE(buf, headAndBody, 2);    // 报文里的CRC
        } else {
            int length = buf[2] & 0xFF;
            crc = LiteByteUtil.crc16(buf, 3 + length);
            crcMsg = LiteByteUtil.bytes2IntLE(buf, 3 + length, 2);    // 报文里的CRC
        }
        if (crc != crcMsg) {
            if (wLog) {
                log.error("CRC校验不匹配. 报文crc = {}, 计算crc = {}", crcMsg, crc);
            }
            res.setStatus(DcConstants.KEY_RES_CODE_SERVICE_ERROR)
                .setError("CRC校验不匹配");
        }
        */

        boolean valid = parseCommonInfo(tid, dno, msgDown, buf, wLog);
        BaseResponse res = new BaseResponse();
        if (!valid) {
            res.setStatus(SinexcelConstants.MSG_PARSE_ERROR_RES);
            return res;
        }
        return res;
    }

    public ObjectResponse<InverterUpgradeRes> parseUpgradeResp(String tid, String dno,
        InverterMsgDown msgDown, byte[] buf,
        boolean wLog) {

        boolean valid = parseCommonInfo(tid, dno, msgDown, buf, wLog);
        ObjectResponse<InverterUpgradeRes> res = new ObjectResponse<>();
        if (!valid) {
            res.setStatus(SinexcelConstants.MSG_PARSE_ERROR_RES);
            return res;
        }

        InverterUpgradeRes data = new InverterUpgradeRes();
        int tempCode = ByteUtils.bytes2IntBE(buf, 4, 2);
        data.setFileType(UpgradeFileType.valueOf(tempCode & 0xFF00));
        data.setStatusCode(UpgradeStatusCode.valueOf(tempCode & 0xFF));

        res.setData(data);
        return res;
    }

}
