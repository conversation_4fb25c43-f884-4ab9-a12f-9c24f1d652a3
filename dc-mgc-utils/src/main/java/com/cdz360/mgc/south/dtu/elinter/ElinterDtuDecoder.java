package com.cdz360.mgc.south.dtu.elinter;

import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.es.type.hi.EssDtuCommunicationWay;
import com.cdz360.base.model.es.type.hi.EssDtuType;
import com.cdz360.base.utils.ByteUtils;
import com.cdz360.mgc.model.ElinterEssDtu;
import com.cdz360.mgc.model.dtu.elinter.ElinterMsgType;
import java.util.Arrays;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ElinterDtuDecoder {

    protected void checkSign(boolean wLog, byte[] buf) {
        if (buf == null) {
            throw new DcServiceException("获取设备信息失败");
        }
        int startByte = ByteUtils.bytes2IntBE(buf, 0, 2);
        if (startByte != ElinterConstants.BYTE_ELINTER_START) {
            throw new DcServiceException("获取设备信息失败..");
        }

        int offset = 10;
        int length = ByteUtils.bytes2IntBE(buf, offset, 2);
        int crc = ByteUtils.crc16(buf, offset + 2 + length);
        int crcMsg = ByteUtils.bytes2IntLE(buf, offset + 2 + length, 2);    // 报文里的CRC
        if (crc != crcMsg) {
            if (wLog) {
                log.error("CRC校验不匹配. 报文crc = {}, 计算crc = {}", crcMsg, crc);
            }
            throw new DcServiceException("CRC校验不匹配");
        }
    }

    public ElinterEssDtu parseBasicInfo(boolean wLog, byte[] buf) {
        checkSign(wLog, buf);

        int idx = 2;

        ElinterEssDtu essDtu = new ElinterEssDtu();

        essDtu.setVer(buf[idx++] & 0xFF);

        essDtu.setHighByte(buf[idx++]);
        essDtu.setLowByte(buf[idx++]);
        essDtu.setMsgType(ElinterMsgType.codeOf(essDtu.getHighByte(), essDtu.getLowByte()));

        int param = buf[idx++] & 0xFF;
        essDtu.setPara(param);

        byte[] msgId = Arrays.copyOfRange(buf, idx, idx + 4);
        idx += 4;
        essDtu.setMsgId(Arrays.toString(msgId));

        essDtu.setDataLength(ByteUtils.bytes2LongBE(buf, idx, 2));
        idx += 2;

        essDtu.setSerialNo(ByteUtils.byteBuf2Ascii(buf, idx, 16));
        idx += 16;

        essDtu.setEssDtuType(EssDtuType.valueOf(buf[idx++] & 0xFF));

        essDtu.setCommunicationWay(EssDtuCommunicationWay.valueOf(buf[idx++] & 0xFF));

        essDtu.setDeviceName(ByteUtils.byteBuf2Ascii(buf, idx, 16));
        idx += 16;

        essDtu.setDeviceModel(ByteUtils.byteBuf2Ascii(buf, idx, 16));
        idx += 16;

        essDtu.setHardwareVer(ByteUtils.byteBuf2Ascii(buf, idx, 20));
        idx += 20;

        essDtu.setSoftwareVer(ByteUtils.byteBuf2Ascii(buf, idx, 20));
        idx += 20;

        idx += 20; // 实际调试时多传了20位无效字节，所以跳过

        essDtu.setIccid(ByteUtils.byteBuf2Ascii(buf, idx, 16)); // 实际调试为16字节
        idx += 16;

        return essDtu;
    }


}
