package com.cdz360.mgc.south.hybridInverter;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.es.dto.EssFactoryDto;
import com.cdz360.base.model.es.dto.InOutTimeRangeDto;
import com.cdz360.base.model.es.type.hi.BmsProtocol;
import com.cdz360.base.model.es.type.hi.SinexcelErrorCode;
import com.cdz360.base.model.es.type.hi.UpgradeFileType;
import com.cdz360.base.model.es.vo.hi.InverterRtData;
import com.cdz360.base.model.es.vo.hi.InverterRtInfo;
import com.cdz360.base.model.es.vo.hi.InverterSysData;
import com.cdz360.base.model.es.vo.hi.InverterUpgradeRes;
import com.cdz360.base.model.es.vo.hi.module.InverterDevMonitor;
import com.cdz360.base.model.es.vo.hi.module.ModuleAmmeterRtData;
import com.cdz360.base.model.es.vo.hi.module.ModuleBmsRtData;
import com.cdz360.mgc.model.hybridInverter.InverterMsgDown;
import java.util.List;

public interface InverterService {

    /**
     * 读取设备运行状态
     *
     * @param wLog     是否打印日志
     * @param deviceId 通讯地址
     * @return
     */
    InverterMsgDown readOpStatus(String tid, String dno, int deviceId, boolean wLog);

    /**
     * 解析设备运行状态
     *
     * @param wLog 是否打印日志
     * @param buf  设备回复数据
     * @return
     */
    ObjectResponse<InverterRtInfo> parseOpStatus(String tid, String dno, InverterMsgDown msgDown,
        byte[] buf,
        boolean wLog);

    /**
     * 读取设备异常告警数据
     *
     * @param wLog     是否打印日志
     * @param deviceId 通讯地址
     */
    List<InverterMsgDown> readErrorList(String tid, String dno, int deviceId, boolean wLog);

    /**
     * 解析设备异常告警数据
     *
     * @param wLog 是否打印日志
     * @param buf  设备回复数据
     * @return
     */
    ListResponse<SinexcelErrorCode> parseErrorList(String tid, String dno, InverterMsgDown msgDown,
        byte[] buf, boolean wLog);

    /**
     * 读取设备组件异常告警数据
     *
     * @param wLog     是否打印日志
     * @param deviceId 通讯地址
     */
    List<InverterMsgDown> readModuleErrorList(String tid, String dno, int deviceId, boolean wLog);

    /**
     * 解析设备组件异常告警数据
     *
     * @param wLog 是否打印日志
     * @param buf  设备回复数据
     * @return
     */
    ListResponse<SinexcelErrorCode> parseModuleErrorList(String tid, String dno,
        InverterMsgDown msgDown,
        byte[] buf, boolean wLog);

    /**
     * 读取设备的模拟量
     *
     * @param wLog     是否打印日志
     * @param deviceId 通讯地址
     * @return
     */
    List<InverterMsgDown> readDeviceData(String tid, String dno, int deviceId, boolean wLog);


    /**
     * 解析设备的模拟量
     *
     * @param wLog 是否打印日志
     * @param buf  设备响应的数据
     * @return
     */
    ObjectResponse<InverterRtData> parseDeviceData(String tid, String dno, InverterMsgDown msgDown,
        boolean wLog, byte[]... buf);


    /**
     * 读取设备的模拟量
     *
     * @param wLog     是否打印日志
     * @param deviceId 通讯地址
     * @return
     */
    List<InverterMsgDown> readDeviceRtData(String tid, String dno, int deviceId, boolean wLog);


    /**
     * 解析设备的模拟量
     *
     * @param wLog 是否打印日志
     * @param buf  设备回复数据
     * @return
     */
    ObjectResponse<InverterRtData> parseDeviceRtData(String tid, String dno,
        InverterMsgDown msgDown,
        byte[] buf, boolean wLog);


    /**
     * 读取设备厂家信息
     *
     * @param wLog     是否打印日志
     * @param deviceId 通讯地址
     * @return
     */
    InverterMsgDown readDeviceFactoryInfo(String tid, String dno, int deviceId, boolean wLog);


    /**
     * 解析设备厂家信息
     *
     * @param wLog 是否打印日志
     * @param buf  设备回复数据
     * @return
     */
    ObjectResponse<EssFactoryDto> parseDeviceFactoryInfo(String tid, String dno,
        InverterMsgDown msgDown,
        byte[] buf, boolean wLog);


    /**
     * 读取设备系统设置
     *
     * @param wLog     是否打印日志
     * @param deviceId 通讯地址
     * @return
     */
    List<InverterMsgDown> readDevSysSetting(String tid, String dno, int deviceId, boolean wLog);

    /**
     * 解析设备系统设置参数
     *
     * @param wLog 是否打印日志
     * @param buf  设备回复数据
     * @return
     */
    ObjectResponse<InverterSysData> parseDevSysSetting(String tid, String dno,
        InverterMsgDown msgDown,
        byte[] buf, boolean wLog);

    /**
     * 读取电表数据
     *
     * @param wLog
     * @param deviceId
     * @return
     */
    List<InverterMsgDown> readAmmeterData(String tid, String dno, int deviceId, boolean wLog);

    /**
     * 解析电表数据
     *
     * @param wLog 是否打印日志
     * @return
     */
    ObjectResponse<ModuleAmmeterRtData> parseAmmeterData(String tid, String dno,
        InverterMsgDown msgDown,
        byte[] buf, boolean wLog);

    /**
     * 读取BMS数据
     *
     * @param wLog     是否打印日志
     * @param deviceId 通讯地址
     * @return
     */
    List<InverterMsgDown> readBmsData(String tid, String dno, int deviceId, boolean wLog);

    /**
     * 解析BMS数据
     *
     * @param wLog 是否打印日志
     * @return
     */
    ObjectResponse<ModuleBmsRtData> parseBmsData(String tid, String dno, InverterMsgDown msgDown,
        List<BmsProtocol> protocols, byte[] buf, boolean wLog);

    /**
     * 读取监控设备
     *
     * @param wLog     是否打印日志
     * @param deviceId 通讯地址
     * @return
     */
    List<InverterMsgDown> readDevMonitor(String tid, String dno, int deviceId, boolean wLog);

    /**
     * 解析监控设备参数
     *
     * @param wLog 是否打印日志
     * @param buf  设备回复数据
     * @return
     */
    ObjectResponse<InverterDevMonitor> parseDevMonitor(String tid, String dno,
        InverterMsgDown msgDown,
        byte[] buf, boolean wLog);

    /**
     * 配置更改（工作模式和充放电时段）
     *
     * @param wLog
     * @param deviceId
     * @param dto
     * @return
     */
    List<InverterMsgDown> inOutTimeRangeConfigModify(String tid, String dno, int deviceId,
        InOutTimeRangeDto dto, boolean wLog);

    /**
     * 解析参数设置应答帧
     *
     * @param wLog
     * @param msgDown
     * @param buf
     * @return
     */
    BaseResponse parseParamSetMsg(String tid, String dno, InverterMsgDown msgDown, byte[] buf,
        boolean wLog);

    /**
     * 生成开关机指令报文
     *
     * @param wLog
     * @param deviceId
     * @return
     */
    List<InverterMsgDown> generateOnOffInstructionMsg(String tid, String dno, int deviceId,
        boolean on, boolean wLog);

    /**
     * 读取故障清除地址数据
     *
     * @param wLog
     * @param deviceId
     * @return
     */
    List<InverterMsgDown> readClearAlarm(String tid, String dno, int deviceId, boolean wLog);

    /**
     * 生成故障清除报文
     *
     * @param wLog
     * @param deviceId
     * @return
     */
    List<InverterMsgDown> generateClearAlarmMsg(String tid, String dno, int deviceId, boolean wLog);

    /**
     * 升级前连接设备
     */
    InverterMsgDown connectUpgradeDevice(String tid, String dno, int deviceId,
        UpgradeFileType fileType, long fileSize, boolean wLog);

    /**
     * 启动升级
     */
    InverterMsgDown startUpgrade(String tid, String dno, int deviceId,
        int packetCount, int mtuSize, boolean wLog);

    /**
     * 发送升级包数据
     */
    List<InverterMsgDown> sendUpgradePackageData(String tid, String dno, int deviceId,
        String binFilePath, int mtuSize, boolean wLog);

    /**
     * 升级完成
     */
    InverterMsgDown upgradeCompleted(String tid, String dno, int deviceId,
        String binFilePath, boolean wLog);

    /**
     * 解析升级返回帧
     */
    ObjectResponse<InverterUpgradeRes> parseUpgradeResp(String tid, String dno,
        InverterMsgDown msgDown, byte[] buf, boolean wLog);


}
