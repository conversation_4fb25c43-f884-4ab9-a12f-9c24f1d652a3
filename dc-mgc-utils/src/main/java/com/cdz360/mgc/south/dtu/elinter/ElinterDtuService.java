package com.cdz360.mgc.south.dtu.elinter;

import com.cdz360.base.model.es.dto.EssDtuDto;
import com.cdz360.mgc.south.dtu.DtuService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ElinterDtuService implements DtuService {

    private ElinterDtuDecoder decoder;

    private ElinterDtuEncoder encoder;

    public void setDecoder(ElinterDtuDecoder decoder) {
        this.decoder = decoder;
    }

    public void setEncoder(ElinterDtuEncoder encoder) {
        this.encoder = encoder;
    }

    @Override
    public String readEssDtu(boolean wLog, Integer protocolVer, int msgId) {
        return encoder.encodeEssDtu(wLog, protocolVer, msgId);
    }

    @Override
    public EssDtuDto parseEssDtu(boolean wLog, byte[] buf) {
        return decoder.parseBasicInfo(wLog, buf);
    }
}
