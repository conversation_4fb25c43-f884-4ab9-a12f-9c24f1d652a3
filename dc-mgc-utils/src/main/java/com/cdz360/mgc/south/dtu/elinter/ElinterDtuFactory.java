package com.cdz360.mgc.south.dtu.elinter;

import com.cdz360.mgc.model.dtu.Dtu;
import com.cdz360.mgc.south.dtu.DtuAbstractFactory;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ElinterDtuFactory implements DtuAbstractFactory {

    @Override
    public Dtu createDtuService() {
        ElinterDtuService dtuService = new ElinterDtuService();
        dtuService.setDecoder(new ElinterDtuDecoder());
        dtuService.setEncoder(new ElinterDtuEncoder());

        Dtu dtu = new Dtu();
        dtu.setDtuService(dtuService);
        return dtu;
    }

}
