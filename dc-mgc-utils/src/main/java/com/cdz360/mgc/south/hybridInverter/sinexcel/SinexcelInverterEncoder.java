package com.cdz360.mgc.south.hybridInverter.sinexcel;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.ByteUtils;
import com.cdz360.mgc.model.hybridInverter.sinexcel.SinexcelOpCode;
import java.io.ByteArrayOutputStream;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;

/**
 * 盛弘家储
 */
@Slf4j
public class SinexcelInverterEncoder {

    /**
     * 拼装生成报文
     *
     * @param deviceAddress 设备通信地址（范围为0~247，0xff为广播地址，默认地址为1）
     * @param opCode        功能码
     * @param startAddress  起始地址 例如：0x1000
     * @param number        查询的地址个数 例如：0x10
     * @return 16进制
     */
    public String encodeByOpCode(boolean wLog, int deviceAddress,
        SinexcelOpCode opCode, int startAddress,
        int number) {
        if (opCode == null) {
            throw new DcArgumentException("功能码不能为空");
        } else if (SinexcelOpCode.SET_PARAMS.equals(opCode)) {
            throw new DcArgumentException("暂不支持该功能码");
        }

        try {
            return ByteUtils.bytesToHex(
                generalMsg(deviceAddress, opCode, startAddress, number, (Float) null));
        } catch (Exception ex) {
            if (wLog) {
                log.error("encodeByOpCode error: {}", ex.getMessage(), ex);
            }
            throw new DcServiceException("编码异常");
        }
    }

    /**
     * 生成写报文
     *
     * @param deviceAddress 设备通信地址（范围为0~247，0xff为广播地址，默认地址为1）
     * @param startAddress  寄存器起始地址 例如：0x1000
     * @param number        寄存器数量 例如：0x10
     * @param value         16功能码时设置的参数(例如1.0)
     * @return 16进制
     */
    public String encodeWriteMsg(boolean wLog, int deviceAddress,
        int startAddress,
        int number, Float... value) {
        try {
            return ByteUtils.bytesToHex(
                generalMsg(deviceAddress, SinexcelOpCode.SET_PARAMS,
                    startAddress, number, value));
        } catch (Exception ex) {
            if (wLog) {
                log.error("encodeByOpCode error: {}", ex.getMessage(), ex);
            }
            throw new DcServiceException("编码异常");
        }
    }

    public String encodeWriteMsg(boolean wLog, int deviceAddress,
        int startAddress,
        int number, Long... value) {
        try {
            return ByteUtils.bytesToHex(
                generalMsg(deviceAddress, SinexcelOpCode.SET_PARAMS,
                    startAddress, number, value));
        } catch (Exception ex) {
            if (wLog) {
                log.error("encodeByOpCode error: {}", ex.getMessage(), ex);
            }
            throw new DcServiceException("编码异常");
        }
    }

    public byte[] generalUpgradeMsg(String tid, String dno, int deviceAddress,
        SinexcelOpCode opCode, int num,
        List<byte[]> data) {
        try {
            ByteArrayOutputStream bas = new ByteArrayOutputStream(8);
            bas.write(deviceAddress & 0xFF);
            bas.write(opCode.getCode());
            bas.write(ByteUtils.intToByte2BE(num));

            if (!data.isEmpty()) {
                for (byte[] bytes : data) {
                    bas.write(bytes);
                }
            }
            appendCRC16LE(bas);
            return bas.toByteArray();
        } catch (Exception ex) {
            log.error("[{} {}] encodeByOpCode error: {}", tid, dno, ex.getMessage(), ex);
            throw new DcServiceException("编码异常");
        }
    }

    private byte[] generalMsg(int deviceAddress, SinexcelOpCode opCode, int startAddress,
        int number, Float... data) throws Exception {
        ByteArrayOutputStream bas = new ByteArrayOutputStream(8);
        bas.write(deviceAddress & 0xFF);
        bas.write(opCode.getCode());
        bas.write(ByteUtils.intToByte2BE(startAddress));
        bas.write(ByteUtils.intToByte2BE(number));

        if (opCode.equals(SinexcelOpCode.SET_PARAMS)) {
            if (data == null || data.length == 0) {
                bas.write(0);
            } else {
                List<byte[]> collect = Arrays.stream(data).map(ByteUtils::floatToByteBE)
                    .collect(Collectors.toList());
                Integer sum = collect.stream().map(e -> e.length).reduce(0, Integer::sum);
                bas.write(sum);
                for (byte[] bytes : collect) {
                    bas.write(bytes);
                }
            }
        }
        appendCRC16LE(bas);
        return bas.toByteArray();
    }

    private byte[] generalMsg(int deviceAddress, SinexcelOpCode opCode, int startAddress,
        int number, Long... data) throws Exception {
        ByteArrayOutputStream bas = new ByteArrayOutputStream(8);
        bas.write(deviceAddress & 0xFF);
        bas.write(opCode.getCode());
        bas.write(ByteUtils.intToByte2BE(startAddress));
        bas.write(ByteUtils.intToByte2BE(number));

        if (opCode.equals(SinexcelOpCode.SET_PARAMS)) {
            if (data == null || data.length == 0) {
                bas.write(0);
            } else {
                List<byte[]> collect = Arrays.stream(data).map(ByteUtils::longToByteBE)
                    .collect(Collectors.toList());
                Integer sum = collect.stream().map(e -> e.length).reduce(0, Integer::sum);
                bas.write(sum);
                for (byte[] bytes : collect) {
                    bas.write(bytes);
                }
            }
        }
        appendCRC16LE(bas);
        return bas.toByteArray();
    }


    private void appendCRC16LE(ByteArrayOutputStream bas) {
        byte[] buf = bas.toByteArray();
        int crc = ByteUtils.crc16(buf, buf.length);
        bas.write((byte) (crc & 0xFF));
        bas.write((byte) (crc >> 8 & 0xFF));
    }


}
