package com.cdz360.mgc.south.GeneralConfig.modbus;

import com.cdz360.mgc.model.generalConfig.GeneralConfig;
import com.cdz360.mgc.south.GeneralConfig.GeneralConfigFactory;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ModbusConfigFactory implements GeneralConfigFactory {

    @Override
    public GeneralConfig createGeneralConfigService() {
        ModbusConfigService service = new ModbusConfigService();
        service.setDecoder(new ModbusConfigDecoder());
        service.setEncoder(new ModbusConfigEncoder());

        GeneralConfig config = new GeneralConfig();
        config.setService(service);
        return config;
    }

}
