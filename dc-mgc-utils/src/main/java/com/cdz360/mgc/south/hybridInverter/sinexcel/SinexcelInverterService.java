package com.cdz360.mgc.south.hybridInverter.sinexcel;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.es.dto.EssFactoryDto;
import com.cdz360.base.model.es.dto.InOutTimeRangeDto;
import com.cdz360.base.model.es.type.ChargeFlowType;
import com.cdz360.base.model.es.type.PcsWorkMode;
import com.cdz360.base.model.es.type.hi.BmsProtocol;
import com.cdz360.base.model.es.type.hi.SinexcelErrorCode;
import com.cdz360.base.model.es.type.hi.UpgradeFileType;
import com.cdz360.base.model.es.vo.EssInOutStrategyItem;
import com.cdz360.base.model.es.vo.hi.InverterRtData;
import com.cdz360.base.model.es.vo.hi.InverterRtInfo;
import com.cdz360.base.model.es.vo.hi.InverterSysData;
import com.cdz360.base.model.es.vo.hi.InverterUpgradeRes;
import com.cdz360.base.model.es.vo.hi.module.InverterDevMonitor;
import com.cdz360.base.model.es.vo.hi.module.ModuleAmmeterRtData;
import com.cdz360.base.model.es.vo.hi.module.ModuleBmsRtData;
import com.cdz360.base.model.es.vo.hi.module.ModuleBmsRtDataTemplate;
import com.cdz360.base.utils.BooleanUtils;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.ByteUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.mgc.model.hybridInverter.InverterMsgDown;
import com.cdz360.mgc.model.hybridInverter.sinexcel.SinexcelConstants;
import com.cdz360.mgc.model.hybridInverter.sinexcel.SinexcelOpCode;
import com.cdz360.mgc.south.hybridInverter.InverterService;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class SinexcelInverterService implements InverterService {

    private final int NUMBER_OF_ERROR_PER_QUERY = 0x40; // 每次查询异常的数量

    private SinexcelInverterDecoder decoder;

    private SinexcelInverterEncoder encoder;

    public void setDecoder(SinexcelInverterDecoder decoder) {
        this.decoder = decoder;
    }

    public void setEncoder(SinexcelInverterEncoder encoder) {
        this.encoder = encoder;
    }

    @Override
    public InverterMsgDown readOpStatus(String tid, String dno, int deviceId, boolean wLog) {
        SinexcelOpCode opCode = SinexcelOpCode.STATUS_AND_ALARM;
        InverterMsgDown msg = new InverterMsgDown();
        int startAddress = SinexcelConstants.STATUS_AND_ALARM_START_ADDRESS;
        int num = 0x18;
        msg.setDeviceId(deviceId)
            .setOpCode(opCode)
            .setStartAddress(startAddress)
            .setNum(num)
            .setDownMsg(encoder.encodeByOpCode(wLog, deviceId,
                opCode, startAddress,
                num));
        return msg;
    }

    @Override
    public ObjectResponse<InverterRtInfo> parseOpStatus(String tid, String dno,
        InverterMsgDown msgDown, byte[] buf,
        boolean wLog) {
        return decoder.parseOpStatus(tid, dno, msgDown, buf, wLog);
    }

    @Override
    public List<InverterMsgDown> readErrorList(String tid, String dno, int deviceId, boolean wLog) {
        SinexcelOpCode opCode = SinexcelOpCode.STATUS_AND_ALARM;

        List<InverterMsgDown> res = new ArrayList<>();

        InverterMsgDown msg1 = new InverterMsgDown();
        int startAddress = SinexcelErrorCode.GRID_VOLTAGE_ABNORMAL.getCode();
        int number = NUMBER_OF_ERROR_PER_QUERY;
        msg1.setDeviceId(deviceId)
            .setOpCode(opCode)
            .setStartAddress(startAddress)
            .setNum(number)
            .setDownMsg(encoder.encodeByOpCode(wLog, deviceId,
                opCode, startAddress,
                number));
        res.add(msg1);

        InverterMsgDown msg2 = new InverterMsgDown();
        int startAddress2 = SinexcelErrorCode.PCB_OVERTEMPERATURE.getCode();
        int number2 = NUMBER_OF_ERROR_PER_QUERY + 8;
        msg2.setDeviceId(deviceId)
            .setOpCode(opCode)
            .setStartAddress(startAddress2)
            .setNum(number2)
            .setDownMsg(encoder.encodeByOpCode(wLog, deviceId,
                opCode, startAddress2,
                number2));
        res.add(msg2);

        return res;
    }

    @Override
    public ListResponse<SinexcelErrorCode> parseErrorList(String tid, String dno,
        InverterMsgDown msgDown,
        byte[] buf, boolean wLog) {
        return decoder.parseErrorCode(tid, dno, msgDown, buf, wLog);
    }

    @Override
    public List<InverterMsgDown> readModuleErrorList(String tid, String dno, int deviceId,
        boolean wLog) {
        SinexcelOpCode opCode = SinexcelOpCode.ANALOG_AND_WAVEFORM;

        List<InverterMsgDown> res = new ArrayList<>();

        InverterMsgDown msg1 = new InverterMsgDown();
        int startAddress = SinexcelConstants.MONITOR_ALARM_ADDRESS;
        int number = 0x02;
        msg1.setDeviceId(deviceId)
            .setOpCode(opCode)
            .setStartAddress(startAddress)
            .setNum(number)
            .setDownMsg(encoder.encodeByOpCode(wLog, deviceId,
                opCode, startAddress,
                number));
        res.add(msg1);

        return res;
    }

    @Override
    public ListResponse<SinexcelErrorCode> parseModuleErrorList(String tid, String dno,
        InverterMsgDown msgDown,
        byte[] buf, boolean wLog) {
        ListResponse<SinexcelErrorCode> dto = new ListResponse<>();

        Integer startAddress = msgDown.getStartAddress();
        switch (startAddress) {
            case SinexcelConstants.MONITOR_ALARM_ADDRESS:
                dto = decoder.parseMonitorError(tid, dno, msgDown, buf, wLog);
                break;
        }
        return dto;
    }

    @Override
    public List<InverterMsgDown> readDeviceData(String tid, String dno, int deviceId,
        boolean wLog) {
        InverterMsgDown inverterMsgDown = new InverterMsgDown();
        //读到0x107A(不包括本身)   负载C相有功功率
        int num = 0x7A;
        inverterMsgDown.setDownMsg(encoder.encodeByOpCode(wLog, deviceId,
            SinexcelOpCode.ANALOG_AND_WAVEFORM,
            0x1000,
            num));
        inverterMsgDown.setStartAddress(0x1000);
        inverterMsgDown.setNum(num);

        //读到0x10DE(不包括本身)   逆变C相负载率
        InverterMsgDown inverterMsgDown1 = new InverterMsgDown();
        num = 0x64;
        inverterMsgDown1.setDownMsg(encoder.encodeByOpCode(wLog, deviceId,
            SinexcelOpCode.ANALOG_AND_WAVEFORM,
            0x107A
            , num));
        inverterMsgDown1.setStartAddress(0x107A);
        inverterMsgDown1.setNum(num);

        //读到0x1220(不包括本身)   电池满载支撑时间2
        InverterMsgDown inverterMsgDown2 = new InverterMsgDown();
        num = 0x20;
        inverterMsgDown2.setDownMsg(encoder.encodeByOpCode(wLog, deviceId,
            SinexcelOpCode.ANALOG_AND_WAVEFORM,
            0x1200,
            num));
        inverterMsgDown2.setStartAddress(0x1200);
        inverterMsgDown2.setNum(num);

        //读取到0x1270(不包括本身) 当天负载用电量
        InverterMsgDown inverterMsgDown3 = new InverterMsgDown();
        num = 0x12;
        inverterMsgDown3.setDownMsg(encoder.encodeByOpCode(wLog, deviceId,
            SinexcelOpCode.ANALOG_AND_WAVEFORM,
            0x125E,
            num));
        inverterMsgDown3.setStartAddress(0x125E);
        inverterMsgDown3.setNum(num);

        return List.of(inverterMsgDown, inverterMsgDown1, inverterMsgDown2, inverterMsgDown3);
    }


    @Override
    public ObjectResponse<InverterRtData> parseDeviceData(String tid, String dno,
        InverterMsgDown msgDown,
        boolean wLog, byte[]... buf) {
        return decoder.parseDeviceData(tid, dno, msgDown, wLog, buf[0], buf[1], buf[2], buf[3]);
    }

    @Override
    public List<InverterMsgDown> readDeviceRtData(String tid, String dno, int deviceId,
        boolean wLog) {
        SinexcelOpCode opCode = SinexcelOpCode.ANALOG_AND_WAVEFORM;
        int numberA = SinexcelConstants.NUMBER_OF_REALTIME_DATA_PER_QUERY_A;

        InverterMsgDown inverterMsgDown1 = new InverterMsgDown();
        int address1 = SinexcelConstants.DEVICE_REALTIME_DATA_QUEUE_START_ADDRESS_1;
        inverterMsgDown1.setDeviceId(deviceId)
            .setOpCode(opCode)
            .setStartAddress(address1)
            .setNum(numberA)
            .setDownMsg(encoder.encodeByOpCode(wLog, deviceId,
                opCode, address1,
                numberA));

        InverterMsgDown inverterMsgDown2 = new InverterMsgDown();
        int address2 = SinexcelConstants.DEVICE_REALTIME_DATA_QUEUE_START_ADDRESS_2;
        int number2 = numberA + 2;
        inverterMsgDown2.setDeviceId(deviceId)
            .setOpCode(opCode)
            .setStartAddress(address2)
            .setNum(number2)
            .setDownMsg(encoder.encodeByOpCode(wLog, deviceId,
                opCode, address2,
                number2));

        InverterMsgDown inverterMsgDown3 = new InverterMsgDown();
        int address3 = SinexcelConstants.DEVICE_REALTIME_DATA_QUEUE_START_ADDRESS_3;
        int number3 = numberA + 4;
        inverterMsgDown3.setDeviceId(deviceId)
            .setOpCode(opCode)
            .setStartAddress(address3)
            .setNum(number3)
            .setDownMsg(encoder.encodeByOpCode(wLog, deviceId,
                opCode, address3,
                number3));

        int numberB = SinexcelConstants.NUMBER_OF_REALTIME_DATA_PER_QUERY_B;
        InverterMsgDown inverterMsgDown4 = new InverterMsgDown();
        int address4 = SinexcelConstants.DEVICE_REALTIME_DATA_QUEUE_START_ADDRESS_4;
        inverterMsgDown4.setDeviceId(deviceId)
            .setOpCode(opCode)
            .setStartAddress(address4)
            .setNum(numberB)
            .setDownMsg(encoder.encodeByOpCode(wLog, deviceId,
                opCode, address4,
                numberB));

        InverterMsgDown inverterMsgDown5 = new InverterMsgDown();
        int address5 = SinexcelConstants.DEVICE_REALTIME_DATA_QUEUE_START_ADDRESS_5;
        int number5 = numberB + 2;
        inverterMsgDown5.setDeviceId(deviceId)
            .setOpCode(opCode)
            .setStartAddress(address5)
            .setNum(number5)
            .setDownMsg(encoder.encodeByOpCode(wLog, deviceId,
                opCode, address5,
                number5));

        return List.of(inverterMsgDown1, inverterMsgDown2, inverterMsgDown3,
            inverterMsgDown4, inverterMsgDown5);
    }

    @Override
    public ObjectResponse<InverterRtData> parseDeviceRtData(String tid, String dno,
        InverterMsgDown msgDown,
        byte[] buf, boolean wLog) {
        return decoder.parseDeviceRtData(tid, dno, msgDown, buf, wLog);
    }

    @Override
    public InverterMsgDown readDeviceFactoryInfo(String tid, String dno, int deviceId,
        boolean wLog) {
        InverterMsgDown msgDown = new InverterMsgDown();
        int address = SinexcelConstants.DEVICE_VENDOR_INFO_START_ADDRESS;
        SinexcelOpCode opCode = SinexcelOpCode.ANALOG_AND_WAVEFORM;
        int num = 0x0A;
        msgDown.setDeviceId(deviceId)
            .setOpCode(opCode)
            .setStartAddress(address)
            .setNum(num)
            .setDownMsg(encoder.encodeByOpCode(wLog, deviceId,
                opCode, address,
                num));
        return msgDown;
    }

    @Override
    public ObjectResponse<EssFactoryDto> parseDeviceFactoryInfo(String tid, String dno,
        InverterMsgDown msgDown,
        byte[] buf, boolean wLog) {
        return decoder.parseDeviceFactoryInfo(tid, dno, msgDown, buf, wLog);
    }

    @Override
    public List<InverterMsgDown> readDevSysSetting(String tid, String dno, int deviceId,
        boolean wLog) {
        SinexcelOpCode opCode = SinexcelOpCode.ANALOG_AND_WAVEFORM;

        InverterMsgDown inverterMsgDown1 = new InverterMsgDown();
        int address1 = SinexcelConstants.DEVICE_SYS_SETTING_START_ADDRESS_1;
        int numberA = SinexcelConstants.NUMBER_OF_REALTIME_DATA_PER_QUERY_A;
        inverterMsgDown1.setDeviceId(deviceId)
            .setOpCode(opCode)
            .setStartAddress(address1)
            .setNum(numberA)
            .setDownMsg(encoder.encodeByOpCode(wLog, deviceId,
                opCode, address1,
                numberA));

        InverterMsgDown inverterMsgDown2 = new InverterMsgDown();
        int address2 = SinexcelConstants.OTHER_TIME_MODEL_ADDRESS;
        int numberB = 2;
        inverterMsgDown2.setDeviceId(deviceId)
            .setOpCode(opCode)
            .setStartAddress(address2)
            .setNum(numberB)
            .setDownMsg(encoder.encodeByOpCode(wLog, deviceId,
                opCode, address2,
                numberB));

        return List.of(inverterMsgDown1, inverterMsgDown2);
    }

    @Override
    public ObjectResponse<InverterSysData> parseDevSysSetting(String tid, String dno,
        InverterMsgDown msgDown,
        byte[] buf, boolean wLog) {
        boolean valid = decoder.parseCommonInfo(tid, dno, msgDown, buf, wLog);
        ObjectResponse<InverterSysData> dto = new ObjectResponse<>();
        if (!valid) {
            dto.setStatus(SinexcelConstants.MSG_PARSE_ERROR_RES);
            return dto;
        }

        Integer startAddress = msgDown.getStartAddress();
        InverterSysData sysData = null;
        switch (startAddress) {
            case SinexcelConstants.DEVICE_SYS_SETTING_START_ADDRESS_1:
                sysData = decoder.parseDeviceSysDataByAddress1(buf);
                break;
            case SinexcelConstants.OTHER_TIME_MODEL_ADDRESS:
                sysData = decoder.parseDeviceSysDataByAddress2(buf);
                break;
        }
        dto.setData(sysData);
        return dto;
    }

    @Override
    public List<InverterMsgDown> readAmmeterData(String tid, String dno, int deviceId,
        boolean wLog) {
        SinexcelOpCode opCode = SinexcelOpCode.ANALOG_AND_WAVEFORM;

        InverterMsgDown inverterMsgDown1 = new InverterMsgDown();
        int address1 = SinexcelConstants.DEVICE_AMMETER_START_ADDRESS_2;
        int num = SinexcelConstants.DEVICE_AMMETER_NUMBER;
        inverterMsgDown1.setDeviceId(deviceId)
            .setOpCode(opCode)
            .setStartAddress(address1)
            .setNum(num)
            .setDownMsg(encoder.encodeByOpCode(wLog, deviceId,
                opCode, address1,
                num));
        return List.of(inverterMsgDown1);
    }

    @Override
    public ObjectResponse<ModuleAmmeterRtData> parseAmmeterData(String tid, String dno,
        InverterMsgDown msgDown,
        byte[] buf, boolean wLog) {
        int deviceId = msgDown.getDeviceId();
        boolean valid = decoder.parseCommonInfo(tid, dno, msgDown, buf, wLog);
        ObjectResponse<ModuleAmmeterRtData> dto = new ObjectResponse<>();
        if (!valid) {
            dto.setStatus(SinexcelConstants.MSG_PARSE_ERROR_RES);
            return dto;
        }

        Integer startAddress = msgDown.getStartAddress();
        ModuleAmmeterRtData rtData = null;
        switch (startAddress) {
            case SinexcelConstants.DEVICE_AMMETER_START_ADDRESS_2:
                rtData = decoder.parseAmmeterDataByAddress2(wLog, deviceId, buf);
                break;
            default:
                break;
        }
        dto.setData(rtData);
        return dto;
    }

    @Override
    public List<InverterMsgDown> readBmsData(String tid, String dno, int deviceId, boolean wLog) {
        SinexcelOpCode opCode = SinexcelOpCode.ANALOG_AND_WAVEFORM;

        InverterMsgDown inverterMsgDown1 = new InverterMsgDown();
        int address1 = SinexcelConstants.DEVICE_BMS_START_ADDRESS_1;
        int num1 = SinexcelConstants.DEVICE_BMS_NUMBER_1;
        inverterMsgDown1.setDeviceId(deviceId)
            .setOpCode(opCode)
            .setStartAddress(address1)
            .setNum(num1)
            .setDownMsg(encoder.encodeByOpCode(wLog, deviceId,
                opCode, address1,
                num1));

        InverterMsgDown inverterMsgDown2 = new InverterMsgDown();
        int address2 = SinexcelConstants.DEVICE_BMS_START_ADDRESS_2;
        int num2 = SinexcelConstants.DEVICE_BMS_NUMBER_2;
        inverterMsgDown2.setDeviceId(deviceId)
            .setOpCode(opCode)
            .setStartAddress(address2)
            .setNum(num2)
            .setDownMsg(encoder.encodeByOpCode(wLog, deviceId,
                opCode, address2,
                num2));

        InverterMsgDown inverterMsgDown3 = new InverterMsgDown();
        int address3 = SinexcelConstants.DEVICE_BMS_START_ADDRESS_3;
        int num3 = SinexcelConstants.DEVICE_BMS_NUMBER_2 + 2;
        inverterMsgDown3.setDeviceId(deviceId)
            .setOpCode(opCode)
            .setStartAddress(address3)
            .setNum(num3)
            .setDownMsg(encoder.encodeByOpCode(wLog, deviceId,
                opCode, address3,
                num3));

        InverterMsgDown inverterMsgDown4 = new InverterMsgDown();
        int address4 = SinexcelConstants.DEVICE_BMS_START_ADDRESS_4;
        int num4 = 66;
        inverterMsgDown4.setDeviceId(deviceId)
            .setOpCode(opCode)
            .setStartAddress(address4)
            .setNum(num4)
            .setDownMsg(encoder.encodeByOpCode(wLog, deviceId,
                opCode, address4,
                num4));
        return List.of(inverterMsgDown4, // 先查询BMS协议，才能正确解析故障和告警
            inverterMsgDown1,
            inverterMsgDown2,
            inverterMsgDown3);
    }

    @Override
    public ObjectResponse<ModuleBmsRtData> parseBmsData(String tid, String dno,
        InverterMsgDown msgDown,
        List<BmsProtocol> protocols, byte[] buf, boolean wLog) {
        int deviceId = msgDown.getDeviceId();
        boolean valid = decoder.parseCommonInfo(tid, dno, msgDown, buf, wLog);
        ObjectResponse<ModuleBmsRtData> dto = new ObjectResponse<>();
        if (!valid) {
            dto.setStatus(SinexcelConstants.MSG_PARSE_ERROR_RES);
            return dto;
        }

        Integer startAddress = msgDown.getStartAddress();
        ModuleBmsRtData rtData = null;
        switch (startAddress) {
            case SinexcelConstants.DEVICE_BMS_START_ADDRESS_1:
                rtData = decoder.parseBmsDataByAddress1(wLog, deviceId, protocols, buf);
                break;
            case SinexcelConstants.DEVICE_BMS_START_ADDRESS_2:
                ModuleBmsRtDataTemplate template1 = decoder.parseBmsDataByAddress2(wLog, deviceId,
                    buf);
                List<ModuleBmsRtDataTemplate> list1 = List.of(template1,
                    new ModuleBmsRtDataTemplate());
                rtData = new ModuleBmsRtData().setBatteryCupboardList(list1);
                break;
            case SinexcelConstants.DEVICE_BMS_START_ADDRESS_3:
                ModuleBmsRtDataTemplate template2 = decoder.parseBmsDataByAddress2(wLog, deviceId,
                    buf);
                List<ModuleBmsRtDataTemplate> list2 = List.of(new ModuleBmsRtDataTemplate(),
                    template2);
                rtData = new ModuleBmsRtData().setBatteryCupboardList(list2);
                break;
            case SinexcelConstants.DEVICE_BMS_START_ADDRESS_4:
                rtData = decoder.parseBmsDataByAddress4(wLog, deviceId, buf);
                break;
            default:
                break;
        }
        dto.setData(rtData);
        return dto;
    }

    @Override
    public List<InverterMsgDown> readDevMonitor(String tid, String dno, int deviceId,
        boolean wLog) {
        SinexcelOpCode opCode = SinexcelOpCode.ANALOG_AND_WAVEFORM;

        InverterMsgDown inverterMsgDown1 = new InverterMsgDown();
        int address1 = SinexcelConstants.DEVICE_MONITOR_SETTING_START_ADDRESS_1;
        int numberA = SinexcelConstants.NUMBER_OF_REALTIME_DATA_PER_QUERY_A;
        inverterMsgDown1.setDeviceId(deviceId)
            .setOpCode(opCode)
            .setStartAddress(address1)
            .setNum(numberA)
            .setDownMsg(encoder.encodeByOpCode(wLog, deviceId,
                opCode, address1,
                numberA));

        InverterMsgDown inverterMsgDown2 = new InverterMsgDown();
        int address2 = SinexcelConstants.REPEAT_CYCLE_ADDRESS;
        int numberB = 0x1E;
        inverterMsgDown2.setDeviceId(deviceId)
            .setOpCode(opCode)
            .setStartAddress(address2)
            .setNum(numberB)
            .setDownMsg(encoder.encodeByOpCode(wLog, deviceId,
                opCode, address2,
                numberB));

        return List.of(inverterMsgDown1, inverterMsgDown2);
    }

    @Override
    public ObjectResponse<InverterDevMonitor> parseDevMonitor(String tid, String dno,
        InverterMsgDown msgDown,
        byte[] buf, boolean wLog) {
        boolean valid = decoder.parseCommonInfo(tid, dno, msgDown, buf, wLog);
        ObjectResponse<InverterDevMonitor> dto = new ObjectResponse<>();
        if (!valid) {
            dto.setStatus(SinexcelConstants.MSG_PARSE_ERROR_RES);
            return dto;
        }

        Integer startAddress = msgDown.getStartAddress();
        InverterDevMonitor monitor = null;
        switch (startAddress) {
            case SinexcelConstants.DEVICE_MONITOR_SETTING_START_ADDRESS_1:
                monitor = decoder.parseDevMonitorByAddress1(buf);
                break;
            case SinexcelConstants.REPEAT_CYCLE_ADDRESS:
                monitor = decoder.parseDevMonitorByAddress2(buf);
                break;
        }
        dto.setData(monitor);
        return dto;
    }

    @Override
    public List<InverterMsgDown> inOutTimeRangeConfigModify(String tid, String dno, int deviceId,
        InOutTimeRangeDto dto, boolean wLog) {
        SinexcelOpCode opCode = SinexcelOpCode.SET_PARAMS;

        Integer targetValue = null;
        switch (dto.getStrategy()) {
            case SELF_USE:
                targetValue = PcsWorkMode.SELF_USE.getCode();
                break;
            case TIMING_CHARGING_DISCHARGING:
                targetValue = PcsWorkMode.TIMED_CHARGE_DISCHARGE.getCode();
                break;
            case DISASTER_SPARE:
                targetValue = PcsWorkMode.EMERGENCY_BACKUP.getCode();
                break;
            default:
                if (wLog) {
                    log.error("[{}] 暂不支持的策略类型 strategy: {}", tid, dto.getStrategy());
                }
                break;
        }
        if (targetValue == null) {
            return null;
        }

        List<InverterMsgDown> msgDowns = new ArrayList<>();

        this.generateWorkModeMsg(deviceId, opCode, msgDowns, targetValue, wLog);

        if (targetValue == PcsWorkMode.TIMED_CHARGE_DISCHARGE.getCode()) {
            this.generateTimedConfigMsg(deviceId, opCode, msgDowns, dto, wLog);
        }

        if (CollectionUtils.isNotEmpty(dto.getInOutItems())) {
            List<EssInOutStrategyItem> collect = dto.getInOutItems().stream()
                .filter(e -> NumberUtils.gteZero(e.getStart()) && NumberUtils.gtZero(e.getEnd()))
                .sorted((a, b) -> NumberUtils.compareInteger(a.getStart(), b.getStart()))
                .collect(Collectors.toList());

            this.generateChargeDischargeRangeMsg(deviceId, opCode, msgDowns, collect, dto, wLog);
        }

        return msgDowns;
    }

    /**
     * 生成“工作模式”下发报文
     */
    private void generateWorkModeMsg(int deviceId, SinexcelOpCode opCode,
        List<InverterMsgDown> msgDowns, Integer targetValue,
        boolean wLog) {
        int number = 2;
        int wordModeAddress = SinexcelConstants.DEVICE_WORD_MODE_ADDRESS;
        InverterMsgDown inverterMsgDown1 = new InverterMsgDown();
        inverterMsgDown1.setDeviceId(deviceId)
            .setOpCode(opCode)
            .setStartAddress(wordModeAddress)
            .setNum(number)
            .setDownMsg(encoder.encodeWriteMsg(wLog, deviceId,
                wordModeAddress,
                number, (float) targetValue));
        msgDowns.add(inverterMsgDown1);
    }

    /**
     * 生成 充放电时间段 和 充放电功率段 的下发报文
     */
    private void generateChargeDischargeRangeMsg(int deviceId, SinexcelOpCode opCode,
        List<InverterMsgDown> msgDowns,
        List<EssInOutStrategyItem> collect, InOutTimeRangeDto dto,
        boolean wLog) {

        Map<ChargeFlowType, List<EssInOutStrategyItem>> flowTypeMap = collect.stream()
            .collect(Collectors.groupingBy(EssInOutStrategyItem::getFlowType));

        int maxItem = SinexcelConstants.MAXIMUM_NUMBER_OF_SUPPORTED_PROTOCOLS;
        List<Float> valueList = new ArrayList<>();
        List<BigDecimal> powerList = new ArrayList<>();

        float zero = this.toCustomeTimeStr(0);
        List<Float> twoZeros = List.of(zero, zero);
        List<Float> fourZeros = List.of(zero, zero, zero, zero);
        for (int i = 0; i < maxItem; i++) {
            int finalI = i;
            Optional.ofNullable(flowTypeMap.get(ChargeFlowType.CHARGE))
                .filter(e -> e.size() > 0 && finalI <= (e.size() - 1))
                .ifPresentOrElse(e -> {
                    EssInOutStrategyItem item = e.get(finalI);
                    if (item != null) {
                        valueList.add(this.toCustomeTimeStr(item.getStart()));
                        valueList.add(this.toCustomeTimeStr(item.getEnd()));
                        powerList.add(item.getActivePower());
                    } else {
                        valueList.addAll(twoZeros);
                        powerList.add(BigDecimal.ZERO);
                    }
                }, () -> {
                    valueList.addAll(twoZeros);
                    powerList.add(BigDecimal.ZERO);
                });

            Optional.ofNullable(flowTypeMap.get(ChargeFlowType.DISCHARGE))
                .filter(e -> e.size() > 0 && finalI <= (e.size() - 1))
                .ifPresentOrElse(e -> {
                    EssInOutStrategyItem item = e.get(finalI);
                    if (item != null) {
                        valueList.add(this.toCustomeTimeStr(item.getStart()));
                        valueList.add(this.toCustomeTimeStr(item.getEnd()));
                        powerList.add(item.getActivePower());
                    } else {
                        valueList.addAll(twoZeros);
                        powerList.add(BigDecimal.ZERO);
                    }
                }, () -> {
                    valueList.addAll(twoZeros);
                    powerList.add(BigDecimal.ZERO);
                });
        }

        /*
            Map<ChargeFlowType, List<EssInOutStrategyItem>> typeListMap = dto.getInOutItems()
                .stream()
                .filter(e -> NumberUtils.gteZero(e.getStart()) && NumberUtils.gtZero(e.getEnd()))
                .sorted((a, b) -> NumberUtils.compareInteger(a.getStart(), b.getStart()))
                .collect(Collectors.groupingBy(EssInOutStrategyItem::getFlowType));
            List<EssInOutStrategyItem> chargeItems = Optional.ofNullable(typeListMap.get(
                ChargeFlowType.CHARGE)).orElse(new ArrayList<>());
            List<EssInOutStrategyItem> dischargeItems = Optional.ofNullable(typeListMap.get(
                ChargeFlowType.DISCHARGE)).orElse(new ArrayList<>());
            List<Float> valueList = new ArrayList<>(Collections.nCopies(maxItem * 4, 0F));
            int chargeIdx = 0;
            for (EssInOutStrategyItem e : chargeItems) {
                valueList.set(chargeIdx, this.toCustomeTimeStr(e.getStart()));
                valueList.set(chargeIdx + 1, this.toCustomeTimeStr(e.getEnd()));
                chargeIdx += 4;
            }
            int dischargeIdx = 2;
            for (EssInOutStrategyItem e : dischargeItems) {
                valueList.set(dischargeIdx, this.toCustomeTimeStr(e.getStart()));
                valueList.set(dischargeIdx + 1, this.toCustomeTimeStr(e.getEnd()));
                dischargeIdx += 4;
            }
            */

        // 不支持时，下发完真正时段就上报结果，后续置零时段不做考虑
        Map<ChargeFlowType, Long> cntMap = collect.stream().filter(e -> e.getFlowType() != null)
            .collect(Collectors.groupingBy(EssInOutStrategyItem::getFlowType,
                Collectors.counting()));
        final int itemNums = 4; // 一个充放电时段为4条
        long meaningfulLength = Long.max(cntMap.getOrDefault(ChargeFlowType.CHARGE, 0L),
            cntMap.getOrDefault(ChargeFlowType.DISCHARGE, 0L)) * itemNums;
        int meaningfulIndex = (int) (meaningfulLength - 1);

        if (BooleanUtils.equals(false, dto.getMultiParamDelivery())) {

            final int registerNumber = 2;

            // 充放电功率段报文
            int chargeDischargePowerRangeStartAddress = SinexcelConstants.CHARGE_DISCHARGE_POWER_RANGE_START_ADDRESS;
            for (BigDecimal decimal : powerList) {
                InverterMsgDown powerMsgDown = new InverterMsgDown();
                powerMsgDown.setDeviceId(deviceId)
                    .setOpCode(opCode)
                    .setStartAddress(chargeDischargePowerRangeStartAddress)
                    .setNum(registerNumber)
                    .setDownMsg(encoder.encodeWriteMsg(wLog, deviceId,
                        chargeDischargePowerRangeStartAddress,
                        registerNumber, decimal.floatValue()));
                msgDowns.add(powerMsgDown);
                chargeDischargePowerRangeStartAddress += registerNumber;
            }

            // 充放电时间段报文
            int tempStart = SinexcelConstants.DEVICE_CHARGE_DISCHARGE_PERIOD_START_ADDRESS;
            for (int i = 0; i < valueList.size(); i++) {
                Float aFloat = valueList.get(i);
                InverterMsgDown inverterMsgDown2 = new InverterMsgDown();
                inverterMsgDown2.setDeviceId(deviceId)
                    .setOpCode(opCode)
                    .setStartAddress(tempStart)
                    .setNum(registerNumber)
                    .setDownMsg(encoder.encodeWriteMsg(wLog, deviceId,
                        tempStart,
                        registerNumber, aFloat));
                if (i == meaningfulIndex) {
                    inverterMsgDown2.setLastMeaningfulFlag(true);
                }
                msgDowns.add(inverterMsgDown2);
                tempStart += registerNumber;
            }

        } else {

            // 充放电功率段报文
            int chargeDischargePowerRangeStartAddress = SinexcelConstants.CHARGE_DISCHARGE_POWER_RANGE_START_ADDRESS;
            int registerNumber = maxItem * 2 * 2; // 一个功率段Item占2个寄存器地址，充电放电认为一体即乘2（便于下发）
            InverterMsgDown powerMsgDown = new InverterMsgDown();
            powerMsgDown.setDeviceId(deviceId)
                .setOpCode(opCode)
                .setStartAddress(chargeDischargePowerRangeStartAddress)
                .setNum(registerNumber)
                .setDownMsg(encoder.encodeWriteMsg(wLog, deviceId,
                    chargeDischargePowerRangeStartAddress,
                    registerNumber,
                    powerList.stream().map(BigDecimal::floatValue).toArray(Float[]::new)
                ));
            msgDowns.add(powerMsgDown);

            // 充放电时间段报文
            int periodStartAddress = SinexcelConstants.DEVICE_CHARGE_DISCHARGE_PERIOD_START_ADDRESS;
            registerNumber = maxItem * 4 * 2; // 一个时间段Item占4个寄存器地址，充电放电认为一体即乘2（便于下发）
            InverterMsgDown inverterMsgDown2 = new InverterMsgDown();
            inverterMsgDown2.setDeviceId(deviceId)
                .setOpCode(opCode)
                .setStartAddress(periodStartAddress)
                .setNum(registerNumber)
                .setDownMsg(encoder.encodeWriteMsg(wLog, deviceId,
                    periodStartAddress,
                    registerNumber, valueList.toArray(new Float[0])));
            msgDowns.add(inverterMsgDown2);

        }

    }

    /**
     * 生成定时关联字段的下发报文
     */
    private void generateTimedConfigMsg(int deviceId, SinexcelOpCode opCode,
        List<InverterMsgDown> msgDowns, InOutTimeRangeDto dto,
        boolean wLog) {
        if (dto.getOtherStrategy() != null) {
            int otherTimeModelAddress = SinexcelConstants.OTHER_TIME_MODEL_ADDRESS;
            int number = 2;
            InverterMsgDown inverterMsgDown1 = new InverterMsgDown();
            inverterMsgDown1.setDeviceId(deviceId)
                .setOpCode(opCode)
                .setStartAddress(otherTimeModelAddress)
                .setNum(number)
                .setDownMsg(encoder.encodeWriteMsg(wLog, deviceId,
                    otherTimeModelAddress,
                    number, dto.convertOtherStrategy()));
            msgDowns.add(inverterMsgDown1);
        }

        if (null != dto.getRepeatCycle()) {
            int repeatCycleAddress = SinexcelConstants.REPEAT_CYCLE_ADDRESS;
            int number = 2;
            long val = dto.getRepeatCycle();
//            if (dto.getRepeatCycle().contains(7)) {
//                val = (val | 1);
//            }
//            if (dto.getRepeatCycle().contains(1)) {
//                val = (val | (1 << 1));
//            }
//            if (dto.getRepeatCycle().contains(2)) {
//                val = (val | (1 << 2));
//            }
//            if (dto.getRepeatCycle().contains(3)) {
//                val = (val | (1 << 3));
//            }
//            if (dto.getRepeatCycle().contains(4)) {
//                val = (val | (1 << 4));
//            }
//            if (dto.getRepeatCycle().contains(5)) {
//                val = (val | (1 << 5));
//            }
//            if (dto.getRepeatCycle().contains(6)) {
//                val = (val | (1 << 6));
//            }
            InverterMsgDown inverterMsgDown2 = new InverterMsgDown();
            inverterMsgDown2.setDeviceId(deviceId)
                .setOpCode(opCode)
                .setStartAddress(repeatCycleAddress)
                .setNum(number)
                .setDownMsg(encoder.encodeWriteMsg(wLog, deviceId,
                    repeatCycleAddress,
                    number, val));
            msgDowns.add(inverterMsgDown2);
        }

        if (dto.getEffectiveStartTime() != null && dto.getEffectiveEndTime() != null) {
            int effectiveStartDateAddress = SinexcelConstants.EFFECTIVE_START_DATE_ADDRESS;

            Long[] array = new Long[2];
            array[0] = dto.getEffectiveStartTime();
            array[1] = dto.getEffectiveEndTime();
            if (BooleanUtils.equals(false, dto.getMultiParamDelivery())) {
                int number = 2;
                for (Long date : array) {
                    InverterMsgDown inverterMsgDown3 = new InverterMsgDown();
                    inverterMsgDown3.setDeviceId(deviceId)
                        .setOpCode(opCode)
                        .setStartAddress(effectiveStartDateAddress)
                        .setNum(number)
                        .setDownMsg(encoder.encodeWriteMsg(wLog, deviceId,
                            effectiveStartDateAddress,
                            number, date));
                    msgDowns.add(inverterMsgDown3);
                    effectiveStartDateAddress += number;
                }
            } else {
                int number = array.length * 2;
                InverterMsgDown inverterMsgDown3 = new InverterMsgDown();
                inverterMsgDown3.setDeviceId(deviceId)
                    .setOpCode(opCode)
                    .setStartAddress(effectiveStartDateAddress)
                    .setNum(number)
                    .setDownMsg(encoder.encodeWriteMsg(wLog, deviceId,
                        effectiveStartDateAddress,
                        number, array));
                msgDowns.add(inverterMsgDown3);
            }
        }

    }

    private float toCustomeTimeStr(int minutes) {
        int hour = minutes / 60;
        int minute = minutes % 60;
        return Float.parseFloat((String.format("%02d%02d", hour, minute)));
    }

    public BaseResponse parseParamSetMsg(String tid, String dno, InverterMsgDown msgDown,
        byte[] buf, boolean wLog) {
        return decoder.parseParamSetMsg(tid, dno, msgDown, buf, wLog);
    }

    @Override
    public List<InverterMsgDown> generateOnOffInstructionMsg(String tid, String dno, int deviceId,
        boolean on, boolean wLog) {
        SinexcelOpCode opCode = SinexcelOpCode.SET_PARAMS;

        List<InverterMsgDown> msgDowns = new ArrayList<>();

        int address = on ? SinexcelConstants.POWER_ON_ADDRESS
            : SinexcelConstants.POWER_OFF_ADDRESS;
        int number = 2;
        InverterMsgDown msgDown = new InverterMsgDown();
        msgDown.setDeviceId(deviceId)
            .setOpCode(opCode)
            .setStartAddress(address)
            .setNum(number)
            .setDownMsg(encoder.encodeWriteMsg(wLog, deviceId,
                address,
                number, 1F));
        msgDowns.add(msgDown);

        return msgDowns;
    }

    @Override
    public List<InverterMsgDown> readClearAlarm(String tid, String dno, int deviceId,
        boolean wLog) {
        SinexcelOpCode opCode = SinexcelOpCode.ANALOG_AND_WAVEFORM;

        List<InverterMsgDown> msgDowns = new ArrayList<>();

        int cleanAlarmAddress = SinexcelConstants.CLEAR_ALARM_ADDRESS;
        int number = 2;
        InverterMsgDown msgDown = new InverterMsgDown();
        msgDown.setDeviceId(deviceId)
            .setOpCode(opCode)
            .setStartAddress(cleanAlarmAddress)
            .setNum(number)
            .setDownMsg(encoder.encodeByOpCode(wLog, deviceId,
                opCode, cleanAlarmAddress,
                number));
        msgDowns.add(msgDown);

        return msgDowns;
    }

    @Override
    public List<InverterMsgDown> generateClearAlarmMsg(String tid, String dno, int deviceId,
        boolean wLog) {
        SinexcelOpCode opCode = SinexcelOpCode.SET_PARAMS;

        List<InverterMsgDown> msgDowns = new ArrayList<>();

        int cleanAlarmAddress = SinexcelConstants.CLEAR_ALARM_ADDRESS;
        int number = 2;
        InverterMsgDown msgDown = new InverterMsgDown();
        msgDown.setDeviceId(deviceId)
            .setOpCode(opCode)
            .setStartAddress(cleanAlarmAddress)
            .setNum(number)
            .setDownMsg(encoder.encodeWriteMsg(wLog, deviceId,
                cleanAlarmAddress,
                number, 1F));
        msgDowns.add(msgDown);

        return msgDowns;
    }

    @Override
    public InverterMsgDown connectUpgradeDevice(String tid, String dno, int deviceId,
        UpgradeFileType fileType, long fileSize,
        boolean wLog) {
        SinexcelOpCode opCode = SinexcelOpCode.UPGRADE_CONNECT_DEVICE;
        InverterMsgDown msg = new InverterMsgDown();

        List<byte[]> data = new ArrayList<>();
        data.add(fileType.getDataType()); // 文件类型
        data.add(ByteUtils.longToByteBE(fileSize)); // BIN文件大小
        int num = data.stream().map(e -> e.length).reduce(0, Integer::sum);

        msg.setDeviceId(deviceId)
            .setOpCode(opCode)
            .setNum(num)
            .setByteMsg(encoder.generalUpgradeMsg(tid, dno, deviceId, opCode, num, data));
        return msg;
    }

    @Override
    public InverterMsgDown startUpgrade(String tid, String dno, int deviceId, int packetCount,
        int mtuSize,
        boolean wLog) {
        SinexcelOpCode opCode = SinexcelOpCode.UPGRADE_START;
        InverterMsgDown msg = new InverterMsgDown();

        List<byte[]> data = new ArrayList<>();
        data.add(ByteUtils.intToByte2BE(packetCount)); // 总包数
        data.add(ByteUtils.intToByte2BE(mtuSize)); // 单包最大长度
        int num = data.stream().map(e -> e.length).reduce(0, Integer::sum);

        msg.setDeviceId(deviceId)
            .setOpCode(opCode)
            .setNum(num)
            .setByteMsg(encoder.generalUpgradeMsg(tid, dno, deviceId, opCode, num, data));
        return msg;
    }

    @Override
    public List<InverterMsgDown> sendUpgradePackageData(String tid, String dno, int deviceId,
        String binFilePath, int mtuSize, boolean wLog) {
        List<InverterMsgDown> res = new ArrayList<>();
        SinexcelOpCode opCode = SinexcelOpCode.UPGRADE_SEND_DATA;
        if (mtuSize == 0) {
            return null;
        }

        try {
            // BIN文件转二进制流
            byte[] fileBytes = Files.readAllBytes(Paths.get(binFilePath));
            int idx = 0;
            for (int i = 0; i < fileBytes.length; i += mtuSize) {
                int end = Math.min(i + mtuSize, fileBytes.length);
                byte[] subArray = new byte[end - i];
                System.arraycopy(fileBytes, i, subArray, 0, end - i);

                InverterMsgDown msg = new InverterMsgDown();

                List<byte[]> data = new ArrayList<>();
                data.add(ByteUtils.intToByte2BE(idx));
                data.add(subArray);
                int num = data.stream().map(e -> e.length).reduce(0, Integer::sum);

                msg.setDeviceId(deviceId)
                    .setOpCode(opCode)
                    .setNum(num)
                    .setByteMsg(encoder.generalUpgradeMsg(tid, dno, deviceId, opCode, num, data));
                res.add(msg);
                idx++;
            }
            return res;
        } catch (Exception ex) {
            log.error("[{} {}] sendUpgradePackageData error: {}", tid, dno, ex.getMessage(), ex);
            throw new DcServiceException("编码异常");
        }
    }

    @Override
    public InverterMsgDown upgradeCompleted(String tid, String dno, int deviceId,
        String binFilePath, boolean wLog) {
        SinexcelOpCode opCode = SinexcelOpCode.UPGRADE_COMPLETED;

        try {
            InverterMsgDown msg = new InverterMsgDown();

            // BIN文件转二进制流
            byte[] fileBytes = Files.readAllBytes(Paths.get(binFilePath));
            int sum = calcTheTotalSumOfUpgradeFile(fileBytes);

            List<byte[]> data = new ArrayList<>();
            data.add(ByteUtils.intToByteBE(sum));
            int num = data.stream().map(e -> e.length).reduce(0, Integer::sum);

            msg.setDeviceId(deviceId)
                .setOpCode(opCode)
                .setNum(num)
                .setByteMsg(encoder.generalUpgradeMsg(tid, dno, deviceId, opCode, num, data));
            return msg;
        } catch (Exception ex) {
            log.error("[{} {}] upgradeCompleted error: {}", tid, dno, ex.getMessage(), ex);
            throw new DcServiceException("编码异常");
        }
    }

    /*
     * 计算BIN文件总累加和
     * 例如：{0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07}
     * 总累加和：0x0001 + 0x0203 + 0x0405 + 0x0607 = 0x0C10
     */
    public int calcTheTotalSumOfUpgradeFile(byte[] byteArray) {
        int sum = 0;
        for (int i = 0; i < byteArray.length; i += 2) {
            int high = byteArray[i] & 0xFF;
            int low = (i + 1 < byteArray.length) ? (byteArray[i + 1] & 0xFF) : 0;
            int value = (high << 8) | low;
            sum += value;
        }
        return sum;
    }

    @Override
    public ObjectResponse<InverterUpgradeRes> parseUpgradeResp(String tid, String dno,
        InverterMsgDown msgDown, byte[] buf, boolean wLog) {
        return decoder.parseUpgradeResp(tid, dno, msgDown, buf, wLog);
    }

}
