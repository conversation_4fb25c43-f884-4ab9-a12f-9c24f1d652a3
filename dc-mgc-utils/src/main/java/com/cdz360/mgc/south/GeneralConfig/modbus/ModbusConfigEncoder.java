package com.cdz360.mgc.south.GeneralConfig.modbus;

import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.ByteUtils;
import java.io.ByteArrayOutputStream;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ModbusConfigEncoder {


    /**
     * 生成写报文
     *
     * @param deviceAddress 设备通信地址（范围为0~247，0xff为广播地址，默认地址为1）
     * @param startAddress  寄存器起始地址 例如：0x1000
     * @param number        寄存器数量 例如：0x10
     * @param value         16功能码时设置的参数(例如1.0)
     * @return 16进制
     */
    public String encodeWriteMsg(int deviceAddress, int opCode, int startAddress,
        int number, Float... value) {
        try {
            return ByteUtils.bytesToHex(
                generalCode(deviceAddress, opCode,
                    startAddress, number, value));
        } catch (Exception ex) {
            log.error("encodeByOpCode error: {}", ex.getMessage(), ex);
            throw new DcServiceException("编码异常");
        }
    }

    private byte[] generalCode(int deviceAddress, int opCode, int startAddress,
        int number, Float... data) throws Exception {
        ByteArrayOutputStream bas = new ByteArrayOutputStream(8);
        bas.write(deviceAddress & 0xFF);
        bas.write(opCode);
        bas.write(ByteUtils.intToByte2BE(startAddress));
        bas.write(ByteUtils.intToByte2BE(number));

        if (0x10 == opCode) {
            if (data == null || data.length == 0) {
                bas.write(0);
            } else {
                List<byte[]> collect = Arrays.stream(data).map(ByteUtils::floatToByteBE)
                    .collect(Collectors.toList());
                Integer sum = collect.stream().map(e -> e.length).reduce(0, Integer::sum);
                bas.write(sum);
                for (byte[] bytes : collect) {
                    bas.write(bytes);
                }
            }
        }
        appendCRC16LE(bas);
        return bas.toByteArray();
    }

    private void appendCRC16LE(ByteArrayOutputStream bas) {
        byte[] buf = bas.toByteArray();
        int crc = ByteUtils.crc16(buf, buf.length);
        bas.write((byte) (crc & 0xFF));
        bas.write((byte) (crc >> 8 & 0xFF));
    }


}
