package com.cdz360.mgc.south.dtu.elinter;

import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.ByteUtils;
import com.cdz360.mgc.model.dtu.elinter.ElinterMsgType;
import java.io.ByteArrayOutputStream;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;

/**
 * 易联智通数据棒
 */
@Slf4j
public class ElinterDtuEncoder {

    /**
     * 拼装生成报文
     *
     * @param msgId
     * @return 16进制
     */
    public String encodeEssDtu(boolean wLog, Integer protocolVer, int msgId) {
        try {
            ByteArrayOutputStream bas = new ByteArrayOutputStream(8);

            bas.write(ByteUtils.intToByte2BE(ElinterConstants.BYTE_ELINTER_START));

            bas.write(Optional.ofNullable(protocolVer).orElse(1));

            bas.write(ElinterMsgType.READ_DATA_DOWN.getHighByte());
            bas.write(ElinterMsgType.READ_DATA_DOWN.getLowByte());

            bas.write(0x01);

            bas.write(ByteUtils.intToByteBE(msgId));

            bas.write(ByteUtils.intToByte2BE(0));

            appendCRC16LE(bas);
            return ByteUtils.bytesToHex(bas.toByteArray());
        } catch (Exception ex) {
            if (wLog) {
                log.error("encodeByOpCode error: {}", ex.getMessage(), ex);
            }
            throw new DcServiceException("编码异常");
        }
    }

    private void appendCRC16LE(ByteArrayOutputStream bas) {
        byte[] buf = bas.toByteArray();
        int crc = ByteUtils.crc16(buf, buf.length);
        bas.write((byte) (crc & 0xFF));
        bas.write((byte) (crc >> 8 & 0xFF));
    }


}
