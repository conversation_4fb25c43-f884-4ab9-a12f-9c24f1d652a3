package com.cdz360.mgc.south.hybridInverter.sinexcel;

import com.cdz360.mgc.model.hybridInverter.Inverter;
import com.cdz360.mgc.south.hybridInverter.InverterAbstractFactory;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class SinexcelInverterFactory implements InverterAbstractFactory {

    @Override
    public Inverter createInverterService() {
        SinexcelInverterService inverterService = new SinexcelInverterService();
        inverterService.setDecoder(new SinexcelInverterDecoder());
        inverterService.setEncoder(new SinexcelInverterEncoder());

        Inverter inverter = new Inverter();
        inverter.setInverterService(inverterService);
        return inverter;
    }
}
