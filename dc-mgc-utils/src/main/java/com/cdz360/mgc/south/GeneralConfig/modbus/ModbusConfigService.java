package com.cdz360.mgc.south.GeneralConfig.modbus;

import com.cdz360.mgc.model.generalConfig.BaseMsgDown;
import com.cdz360.mgc.model.hybridInverter.trans.DataItemValue;
import com.cdz360.mgc.model.hybridInverter.trans.ManualConfig;
import com.cdz360.mgc.south.GeneralConfig.GeneralConfigService;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
//import lombok.extern.slf4j.Slf4j;

//@Slf4j
public class ModbusConfigService implements GeneralConfigService {

    private ModbusConfigDecoder decoder;

    private ModbusConfigEncoder encoder;

    public void setDecoder(ModbusConfigDecoder decoder) {
        this.decoder = decoder;
    }

    public void setEncoder(ModbusConfigEncoder encoder) {
        this.encoder = encoder;
    }


    @Override
    public List<BaseMsgDown> generateMsg(int deviceId, ManualConfig manualConfig) {
        List<BaseMsgDown> result = new ArrayList<>();
        manualConfig.getFunctionCodeDataList().forEach(data -> {
            if (data.getFunctionCode() == 0x10) {
                result.addAll(encodeByCode0x10(deviceId, data.getSize(), data.getDataItemList()));
            }
        });
        return result;
    }

    private List<BaseMsgDown> encodeByCode0x10(int deviceId, int defaultSize,
        List<DataItemValue> dataItemList) {
        int opCode = 0x10;

        return dataItemList.stream().map(item -> {
                BaseMsgDown msg = new BaseMsgDown();
                int startAddress = item.getAddress();
                msg.setDeviceId(deviceId)
                    .setOpCode(opCode)
                    .setStartAddress(startAddress)
                    .setDownMsg(encoder.encodeWriteMsg(deviceId,
                        opCode,
                        startAddress,
                        item.getOccupy(),
                        item.getValue().floatValue()));
                return msg;
            })
            .collect(Collectors.toList());
    }
}
