package com.cdz360.mgc.biz;

import com.cdz360.base.model.es.type.StickVendor;
import com.cdz360.mgc.model.dtu.Dtu;
import com.cdz360.mgc.south.dtu.DtuAbstractFactory;
import com.cdz360.mgc.south.dtu.elinter.ElinterDtuFactory;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class DtuMgmService {

    public Dtu generateService(StickVendor vendor) {

        DtuAbstractFactory dtuAbstractFactory = null;
        if (StickVendor.E_LINTER.equals(vendor)) {
            dtuAbstractFactory = new ElinterDtuFactory();
            return dtuAbstractFactory.createDtuService();
        } else {
            log.warn("不支持的类型 vendor = {}", vendor);
            return null;
        }
    }

}
