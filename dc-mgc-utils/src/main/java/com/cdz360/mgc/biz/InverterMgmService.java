package com.cdz360.mgc.biz;

import com.cdz360.base.model.es.type.hi.HybridInverterVendor;
import com.cdz360.mgc.model.hybridInverter.Inverter;
import com.cdz360.mgc.south.hybridInverter.InverterAbstractFactory;
import com.cdz360.mgc.south.hybridInverter.sinexcel.SinexcelInverterFactory;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class InverterMgmService {

    public Inverter generateService(HybridInverterVendor vendor) {

        InverterAbstractFactory inverterFactory = null;
        if (HybridInverterVendor.SINEXCEL.equals(vendor)) {
            inverterFactory = new SinexcelInverterFactory();
            return inverterFactory.createInverterService();
        } else {
            log.warn("不支持的类型 vendor = {}", vendor);
            return null;
        }
    }

}
