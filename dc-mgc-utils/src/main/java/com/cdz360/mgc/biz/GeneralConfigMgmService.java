package com.cdz360.mgc.biz;

import com.cdz360.mgc.model.generalConfig.GeneralConfig;
import com.cdz360.mgc.south.GeneralConfig.GeneralConfigFactory;
import com.cdz360.mgc.south.GeneralConfig.modbus.ModbusConfigFactory;
import lombok.extern.slf4j.Slf4j;

/**
 * 通用配置读写服务
 */
@Slf4j
public class GeneralConfigMgmService {

    public GeneralConfig parse(String protocolType) {

        GeneralConfigFactory factory = null;
        if ("Modbus".equals(protocolType)) {
            factory = new ModbusConfigFactory();
            return factory.createGeneralConfigService();
        } else {
            log.warn("不支持的类型 protocolType = {}", protocolType);
            return null;
        }
    }

}
