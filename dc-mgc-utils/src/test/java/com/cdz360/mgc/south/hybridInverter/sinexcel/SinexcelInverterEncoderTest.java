package com.cdz360.mgc.south.hybridInverter.sinexcel;

import com.cdz360.mgc.model.hybridInverter.sinexcel.SinexcelOpCode;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class SinexcelInverterEncoderTest {

    private final SinexcelInverterEncoder sinexcelInverterEncoder = new SinexcelInverterEncoder();

    @Test
    void encodeByOpCode() {
        int address = 1;
        SinexcelOpCode opCode = SinexcelOpCode.STATUS_AND_ALARM;
        int startAddress = 0x1028;
        int number = 0x10;
        String hexString = sinexcelInverterEncoder.encodeByOpCode(true, address,
            opCode, startAddress,
            number);

        String expand = "01 02 10 28 00 10 FD 0E".replaceAll("\\s*", "");
        Assertions.assertEquals(expand, hexString);
    }
}