package com.cdz360.mgc.south.GeneralConfig.modbus;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.ByteUtils;
import com.cdz360.mgc.model.generalConfig.BaseMsgDown;
import com.cdz360.mgc.model.hybridInverter.ManualConfigSingleton;
import com.cdz360.mgc.model.hybridInverter.trans.DataItemValue;
import com.cdz360.mgc.model.hybridInverter.trans.ManualConfig;
import com.cdz360.mgc.model.hybridInverter.trans.ModbusCommunicationContext;
import java.nio.ByteBuffer;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class ModbusConfigServiceTest {

    private ModbusConfigService service;

    @BeforeEach
    public void init() {
        service = new ModbusConfigService();
        service.setDecoder(new ModbusConfigDecoder());
        service.setEncoder(new ModbusConfigEncoder());
    }

    @Test
    void generateMsg() {
        int deviceId = 1;
        String json = "{\"ver\":\"0.1\",\"fcs\":[{\"fc\":16,\"s\":2,\"dis\":[{\"ad\":12358,\"desc\":\"电池1放电深度\",\"oc\":2,\"s\":2,\"dt\":\"float\",\"enumList\":[],\"val\":1.1}]}]}";
        ManualConfig data = JsonUtils.fromJson(json, ManualConfig.class);
        System.out.println(data.toString());
        List<BaseMsgDown> baseMsgDowns = service.generateMsg(deviceId, data);
        System.out.printf("baseMsgDowns: " + JsonUtils.toJsonString(baseMsgDowns));
    }

    @Test
    void opConfig() {
//        String json = "{\"ver\":\"0.1\",\"fcs\":[{\"fc\":16,\"s\":2,\"dis\":[{\"ad\":12358,\"desc\":\"电池1放电深度\",\"oc\":2,\"s\":2,\"dt\":\"float\",\"enumList\":[],\"val\":1}]}]}";
        String json = "{\"ver\":\"0.1\",\"hc\":{\"desc\":\"主机配置\",\"i18nMap\":{\"en\":\"Host Config\",\"zh\":\"主机配置\"},\"fc\":16,\"ads\":[49160]},\"bc\":{\"desc\":\"电池配置\",\"i18nMap\":{\"en\":\"Battery Config\",\"zh\":\"电池配置\"},\"fc\":16,\"ads\":[49160]},\"sc\":{\"desc\":\"高级配置\",\"i18nMap\":{\"en\":\"Senior Config\",\"zh\":\"主机配置\"},\"fc\":16,\"ads\":[49160]},\"fcs\":[{\"fc\":16,\"s\":2,\"dis\":[{\"ad\":49160,\"desc\":\"电池协议\",\"oc\":2,\"s\":2,\"dt\":\"int\",\"enumList\":[{\"l\":\"无\",\"v\":0},{\"l\":\"Pylon\",\"v\":1},{\"l\":\"Dyness\",\"v\":2},{\"l\":\"Greenway\",\"v\":3},{\"l\":\"无\",\"v\":0},{\"l\":\"无\",\"v\":0}]},{\"ad\":4108,\"desc\":\"DRED状态\",\"oc\":4,\"s\":1,\"dt\":\"int\",\"enumList\":[{\"l\":\"0\",\"v\":0},{\"l\":\"1\",\"v\":1},{\"l\":\"2\",\"v\":2},{\"l\":\"3\",\"v\":3},{\"l\":\"4\",\"v\":5},{\"l\":\"5\",\"v\":5},{\"l\":\"6\",\"v\":6},{\"l\":\"7\",\"v\":7},{\"l\":\"8\",\"v\":8}]},{\"ad\":12290,\"desc\":\"防逆流使能\",\"oc\":2,\"s\":2,\"dt\":\"int\",\"enumList\":[{\"l\":\"禁止\",\"v\":0},{\"l\":\"使能\",\"v\":1}]},{\"ad\":12292,\"desc\":\"防逆流功率设定\",\"oc\":2,\"s\":2,\"dt\":\"float\",\"enumList\":[]},{\"ad\":12294,\"desc\":\"三相三线/三相四线制\",\"oc\":2,\"s\":2,\"dt\":\"int\",\"enumList\":[{\"l\":\"三相四线制\",\"v\":0},{\"l\":\"三相三线制\",\"v\":1}]}]}]}";
        System.out.println(json);
        ManualConfigSingleton mc = ManualConfigSingleton.configJson(json);
        DataItemValue item = JsonUtils.fromJson(
            "{\"ad\":12358,\"desc\":\"电池1放电深度\",\"oc\":2,\"s\":2,\"dt\":\"float\",\"enumList\":[],\"val\":1}",
            DataItemValue.class);
//        0103304600047F39
        mc.hostConfigItems().forEach(x -> {
            String msg = ManualConfigSingleton.readItemBufferString((byte) 1, x);
            System.out.println(msg);
        });
        mc.batConfigItems().forEach(x -> {
            String msg = ManualConfigSingleton.readItemBufferString((byte) 1, x);
            System.out.println(msg);
        });
        mc.seniorConfigItems().forEach(x -> {
            String msg = ManualConfigSingleton.readItemBufferString((byte) 1, x);
            System.out.println(msg);
        });
    }

    @Test
    void opConfig2() {
//        String json = "{\"ver\":\"0.1\",\"fcs\":[{\"fc\":16,\"s\":2,\"dis\":[{\"ad\":12358,\"desc\":\"电池1放电深度\",\"oc\":2,\"s\":2,\"dt\":\"float\",\"enumList\":[],\"val\":1}]}]}";
        String json = "{\"ver\":\"0.1\",\"hc\":{\"desc\":\"主机配置\",\"i18nMap\":{\"en\":\"Host Config\",\"zh\":\"主机配置\"},\"fc\":16,\"ads\":[49160]},\"bc\":{\"desc\":\"电池配置\",\"i18nMap\":{\"en\":\"Battery Config\",\"zh\":\"电池配置\"},\"fc\":16,\"ads\":[49160]},\"sc\":{\"desc\":\"高级配置\",\"i18nMap\":{\"en\":\"Senior Config\",\"zh\":\"主机配置\"},\"fc\":16,\"ads\":[49160]},\"fcs\":[{\"fc\":16,\"s\":2,\"dis\":[{\"ad\":49160,\"desc\":\"电池协议\",\"oc\":2,\"s\":2,\"dt\":\"int\",\"enumList\":[{\"l\":\"无\",\"v\":0},{\"l\":\"Pylon\",\"v\":1},{\"l\":\"Dyness\",\"v\":2},{\"l\":\"Greenway\",\"v\":3},{\"l\":\"无\",\"v\":0},{\"l\":\"无\",\"v\":0}]},{\"ad\":4108,\"desc\":\"DRED状态\",\"oc\":4,\"s\":1,\"dt\":\"int\",\"enumList\":[{\"l\":\"0\",\"v\":0},{\"l\":\"1\",\"v\":1},{\"l\":\"2\",\"v\":2},{\"l\":\"3\",\"v\":3},{\"l\":\"4\",\"v\":5},{\"l\":\"5\",\"v\":5},{\"l\":\"6\",\"v\":6},{\"l\":\"7\",\"v\":7},{\"l\":\"8\",\"v\":8}]},{\"ad\":12290,\"desc\":\"防逆流使能\",\"oc\":2,\"s\":2,\"dt\":\"int\",\"enumList\":[{\"l\":\"禁止\",\"v\":0},{\"l\":\"使能\",\"v\":1}]},{\"ad\":12292,\"desc\":\"防逆流功率设定\",\"oc\":2,\"s\":2,\"dt\":\"float\",\"enumList\":[]},{\"ad\":12294,\"desc\":\"三相三线/三相四线制\",\"oc\":2,\"s\":2,\"dt\":\"int\",\"enumList\":[{\"l\":\"三相四线制\",\"v\":0},{\"l\":\"三相三线制\",\"v\":1}]}]}]}";
        System.out.println(json);
        ManualConfigSingleton mc = ManualConfigSingleton.configJson(json);
        DataItemValue item = JsonUtils.fromJson(
            "{\"ad\":12358,\"desc\":\"电池1放电深度\",\"oc\":2,\"s\":2,\"dt\":\"float\",\"enumList\":[],\"val\":1}",
            DataItemValue.class);
//        0103304600047F39
//        mc.hostConfigItems().forEach(x -> {
//            String msg = ManualConfigSingleton.readItemBufferString((byte) 1, x);
//            System.out.println(msg);
//        });
//        mc.batConfigItems().forEach(x -> {
//            String msg = ManualConfigSingleton.readItemBufferString((byte) 1, x);
//            System.out.println(msg);
//        });
//        mc.seniorConfigItems().forEach(x -> {
//            String msg = ManualConfigSingleton.readItemBufferString((byte) 1, x);
//            System.out.println(msg);
//        });

        item.setValue(20F);
//        0110 3046 0004 04 41A00000 1665
        String msg = ManualConfigSingleton.writeItemBufferString((byte) 1, item);
        System.out.println(msg);
    }

    @Test
    void opConfig3() {
//        String json = "{\"ver\":\"0.1\",\"fcs\":[{\"fc\":16,\"s\":2,\"dis\":[{\"ad\":12358,\"desc\":\"电池1放电深度\",\"oc\":2,\"s\":2,\"dt\":\"float\",\"enumList\":[],\"val\":1}]}]}";
        String json = "{\"ver\":\"0.1\",\"hc\":{\"desc\":\"主机配置\",\"i18nMap\":{\"en\":\"Host Config\",\"zh\":\"主机配置\"},\"fc\":16,\"ads\":[49160]},\"bc\":{\"desc\":\"电池配置\",\"i18nMap\":{\"en\":\"Battery Config\",\"zh\":\"电池配置\"},\"fc\":16,\"ads\":[49160]},\"sc\":{\"desc\":\"高级配置\",\"i18nMap\":{\"en\":\"Senior Config\",\"zh\":\"主机配置\"},\"fc\":16,\"ads\":[49160]},\"fcs\":[{\"fc\":16,\"s\":2,\"dis\":[{\"ad\":49160,\"desc\":\"电池协议\",\"oc\":2,\"s\":2,\"dt\":\"int\",\"enumList\":[{\"l\":\"无\",\"v\":0},{\"l\":\"Pylon\",\"v\":1},{\"l\":\"Dyness\",\"v\":2},{\"l\":\"Greenway\",\"v\":3},{\"l\":\"无\",\"v\":0},{\"l\":\"无\",\"v\":0}]},{\"ad\":4108,\"desc\":\"DRED状态\",\"oc\":4,\"s\":1,\"dt\":\"int\",\"enumList\":[{\"l\":\"0\",\"v\":0},{\"l\":\"1\",\"v\":1},{\"l\":\"2\",\"v\":2},{\"l\":\"3\",\"v\":3},{\"l\":\"4\",\"v\":5},{\"l\":\"5\",\"v\":5},{\"l\":\"6\",\"v\":6},{\"l\":\"7\",\"v\":7},{\"l\":\"8\",\"v\":8}]},{\"ad\":12290,\"desc\":\"防逆流使能\",\"oc\":2,\"s\":2,\"dt\":\"int\",\"enumList\":[{\"l\":\"禁止\",\"v\":0},{\"l\":\"使能\",\"v\":1}]},{\"ad\":12292,\"desc\":\"防逆流功率设定\",\"oc\":2,\"s\":2,\"dt\":\"float\",\"enumList\":[]},{\"ad\":12294,\"desc\":\"三相三线/三相四线制\",\"oc\":2,\"s\":2,\"dt\":\"int\",\"enumList\":[{\"l\":\"三相四线制\",\"v\":0},{\"l\":\"三相三线制\",\"v\":1}]}]}]}";
        System.out.println(json);
        ManualConfigSingleton mc = ManualConfigSingleton.configJson(json);
        DataItemValue item = JsonUtils.fromJson(
            "{\"ad\":12358,\"desc\":\"电池1放电深度\",\"oc\":2,\"s\":2,\"dt\":\"float\",\"enumList\":[],\"val\":1}",
            DataItemValue.class);

        item.setValue(20F);
//        0110 3046 0004 04 41A00000 1665
//        String msg = ManualConfigSingleton.writeItemBufferString((byte) 1, item);
//        System.out.println(msg);

        String msg = "01030441200000EFC5"; // EFC5
//
//        ModbusCommunicationContext cnt = new ModbusCommunicationContext()
//            .setDevId((byte) 1)
//            .setFc(3)
//            .setDataItem(item)
//            .setBuffer(ByteBuffer.wrap(LiteByteUtil.hexStringToByteArray(msg)));
//        cnt.parse();

        ModbusCommunicationContext.builder()
            .devId((byte) 1)
            .fc(3)
            .dataItem(item)
            .buffer(ByteBuffer.wrap(ByteUtils.hexToBytes(msg)))
            .build()
            .parse();
        System.out.println(item.getValue());
        System.out.println(item.getVisibleValues());
    }

}