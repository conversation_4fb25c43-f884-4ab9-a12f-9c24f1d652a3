package com.cdz360.mgc.south.dtu.elinter;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class ElinterDtuEncoderTest {

    private final ElinterDtuEncoder encoder = new ElinterDtuEncoder();

    @Test
    void encodeEssDtu() {
        String hexString = encoder.encodeEssDtu(true, 1, 0x4A);
        System.out.println("res: " + hexString);

        String expand = "AA55010201010000004A00005769";
        Assertions.assertEquals(expand, hexString);
    }
}