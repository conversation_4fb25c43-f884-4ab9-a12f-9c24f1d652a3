package com.cdz360.mgc.south.hybridInverter.sinexcel;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.es.type.hi.HybridInverterOpStatus;
import com.cdz360.base.model.es.type.hi.SinexcelErrorCode;
import com.cdz360.base.model.es.vo.hi.InverterRtInfo;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.ByteUtils;
import com.cdz360.mgc.model.hybridInverter.InverterMsgDown;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class SinexcelInverterDecoderTest {

    private SinexcelInverterDecoder sinexcelInverterDecoder = new SinexcelInverterDecoder();
    private String dno = "dno";
    private String tid = "tid";

    @Test
    void parseOpStatus() {
        String msgDownJson = "{\"deviceId\":1,\"opCode\":2,\"startAddress\":4096,\"num\":24,\"downMsg\":\"0102100000187CC0\"}";
        InverterMsgDown msgDown = JsonUtils.fromJson(msgDownJson, InverterMsgDown.class);

        byte[] buf = ByteUtils.hexToBytes("01020306E000D18f".replaceAll("\\s*", ""));
        ObjectResponse<InverterRtInfo> res = sinexcelInverterDecoder.parseOpStatus(tid, dno,
            msgDown, buf, true);
        InverterRtInfo rtInfo = res.getData();
        System.out.println("rtInfo: " + rtInfo.toString());
        Assertions.assertEquals(HybridInverterOpStatus.ON_GRID,
            rtInfo.getHybridInverterOpStatus());
        Assertions.assertEquals(14, rtInfo.getDredStatus());
    }

    @Test
    void parseErrorCode() {
        String msgDownJson = "{\"deviceId\":2,\"opCode\":2,\"startAddress\":4232,\"num\":64,\"downMsg\":\"020210880040FD23\"}";
        InverterMsgDown msgDown = JsonUtils.fromJson(msgDownJson, InverterMsgDown.class);

        String str = "02020800800100000000004B4F";
        byte[] buf = ByteUtils.hexToBytes(str.replaceAll("\\s*", ""));
        ListResponse<SinexcelErrorCode> res = sinexcelInverterDecoder.parseErrorCode(tid, dno,
            msgDown, buf, true);
        List<SinexcelErrorCode> errorCodes = res.getData();
        System.out.println("rtInfo: " + JsonUtils.toJsonString(errorCodes));

        List<SinexcelErrorCode> expand = List.of(SinexcelErrorCode.BATTERY_NOT_CHARGE_1,
            SinexcelErrorCode.BATTERY_NOT_DISCHARGE_1);
        Assertions.assertEquals(expand, errorCodes);
    }

    @Test
    void parseMonitorError() {
        String msgDownJson = "{\"deviceId\":1,\"opCode\":3,\"startAddress\":49234,\"num\":2,\"downMsg\":\"0103C052000259DA\"}";
        InverterMsgDown msgDown = JsonUtils.fromJson(msgDownJson, InverterMsgDown.class);

        String str = "010304000000053a30";
        byte[] buf = ByteUtils.hexToBytes(str.replaceAll("\\s*", ""));
        ListResponse<SinexcelErrorCode> res = sinexcelInverterDecoder.parseMonitorError(tid, dno,
            msgDown, buf, true);
        List<SinexcelErrorCode> errorCodes = res.getData();
        System.out.println("rtInfo: " + JsonUtils.toJsonString(errorCodes));

        List<SinexcelErrorCode> expand = List.of(SinexcelErrorCode.DCDC_COMMUNICATION_FAILS,
            SinexcelErrorCode.SLAVE_COMMUNICATION_FAILS);
        Assertions.assertEquals(expand, errorCodes);
    }

}