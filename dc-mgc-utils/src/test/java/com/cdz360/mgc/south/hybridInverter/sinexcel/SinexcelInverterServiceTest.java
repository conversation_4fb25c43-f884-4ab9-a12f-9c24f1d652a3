package com.cdz360.mgc.south.hybridInverter.sinexcel;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.es.dto.EssFactoryDto;
import com.cdz360.base.model.es.dto.InOutTimeRangeDto;
import com.cdz360.base.model.es.type.ChargeFlowType;
import com.cdz360.base.model.es.type.EssCfgStrategy;
import com.cdz360.base.model.es.type.hi.BmsBasicStatus;
import com.cdz360.base.model.es.type.hi.BmsProtocol;
import com.cdz360.base.model.es.type.hi.HybridInverterVendor;
import com.cdz360.base.model.es.type.hi.SinexcelErrorCode;
import com.cdz360.base.model.es.type.hi.UpgradeFileType;
import com.cdz360.base.model.es.type.hi.UpgradeStatusCode;
import com.cdz360.base.model.es.vo.EssInOutStrategyItem;
import com.cdz360.base.model.es.vo.hi.InverterRtData;
import com.cdz360.base.model.es.vo.hi.InverterUpgradeRes;
import com.cdz360.base.model.es.vo.hi.module.ModuleBmsRtData;
import com.cdz360.base.utils.DateUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.ByteUtils;
import com.cdz360.mgc.biz.InverterMgmService;
import com.cdz360.mgc.model.hybridInverter.Inverter;
import com.cdz360.mgc.model.hybridInverter.InverterMsgDown;
import com.cdz360.mgc.model.hybridInverter.sinexcel.SinexcelConstants;
import com.cdz360.mgc.model.hybridInverter.sinexcel.SinexcelOpCode;
import com.cdz360.mgc.south.MgcUtilsTestBase;
import com.cdz360.mgc.south.hybridInverter.InverterService;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

@Slf4j
class SinexcelInverterServiceTest extends MgcUtilsTestBase {

    private InverterService inverterService;
    private String tid = "tid";
    private String dno = "dno";

    @BeforeEach
    void before() {
        System.out.println("BeforeEach execute");
        InverterMgmService inverterMgmService = new InverterMgmService();
        Inverter inverter = inverterMgmService.generateService(HybridInverterVendor.SINEXCEL);
        inverterService = inverter.getInverterService();
    }

    @Test
    void readOpStatus() {
        InverterMsgDown inverterMsgDown = inverterService.readOpStatus(tid, dno, 1, true);
        System.out.println("inverterMsgDown: " + JsonUtils.toJsonString(inverterMsgDown));
        Assertions.assertNotNull(inverterMsgDown.getDownMsg());
    }

    @Test
    void readErrorList() {
        List<InverterMsgDown> inverterMsgDowns = inverterService.readErrorList(tid, dno, 1, true);
        System.out.println("inverterMsgDowns: " + JsonUtils.toJsonString(inverterMsgDowns));
        Assertions.assertNotNull(inverterMsgDowns);
        Assertions.assertFalse(inverterMsgDowns.isEmpty());

        List<InverterMsgDown> inverterMsgDowns2 = inverterService.readErrorList(tid, dno, 2, true);
        System.out.println("inverterMsgDowns2: " + JsonUtils.toJsonString(inverterMsgDowns2));
        Assertions.assertNotNull(inverterMsgDowns);
        Assertions.assertFalse(inverterMsgDowns.isEmpty());
    }

    @Test
    void parseErrorList() {
        String msgDownJson = "{\"deviceId\":1,\"opCode\":2,\"startAddress\":4168,\"num\":64,\"downMsg\":\"010210480040FD2C\"}";
        InverterMsgDown msgDown1 = JsonUtils.fromJson(msgDownJson, InverterMsgDown.class);

        String hexStr = "010208010000000000000005de";

        ListResponse<SinexcelErrorCode> res = inverterService.parseErrorList(tid, dno, msgDown1,
            ByteUtils.hexToBytes(hexStr), true);
        List<SinexcelErrorCode> errorCodes = res.getData();
        System.out.println("rtInfo: " + JsonUtils.toJsonString(errorCodes));
        List<SinexcelErrorCode> expected = List.of(SinexcelErrorCode.GRID_VOLTAGE_ABNORMAL);
        Assertions.assertEquals(expected, errorCodes);

        msgDownJson = "{\"deviceId\":1,\"opCode\":2,\"startAddress\":4232,\"num\":64,\"downMsg\":\"010210880040FD10\"}";
        InverterMsgDown msgDown2 = JsonUtils.fromJson(msgDownJson, InverterMsgDown.class);

        hexStr = "0102080000000000400000c5c6";
        ListResponse<SinexcelErrorCode> res2 = inverterService.parseErrorList(tid, dno, msgDown2,
            ByteUtils.hexToBytes(hexStr), true);
        List<SinexcelErrorCode> errorCodes2 = res2.getData();
        System.out.println("rtInfo2: " + JsonUtils.toJsonString(errorCodes2));
        expected = List.of(SinexcelErrorCode.PV_POWER_TUBE_IS_FAULTY_4);
        Assertions.assertEquals(expected, errorCodes2);
    }

    @Test
    void readAndParseModuleErrorList() {
        List<InverterMsgDown> inverterMsgDowns = inverterService.readModuleErrorList(tid, dno, 1,
            true);
        System.out.println("readModuleErrorList: " + JsonUtils.toJsonString(inverterMsgDowns));
        Assertions.assertNotNull(inverterMsgDowns);
        Assertions.assertFalse(inverterMsgDowns.isEmpty());

        String hexStr = "010304000000053a30";
        ListResponse<SinexcelErrorCode> res2 = inverterService.parseModuleErrorList(tid, dno,
            inverterMsgDowns.get(0),
            ByteUtils.hexToBytes(hexStr), true);
        List<SinexcelErrorCode> errorCodes = res2.getData();
        System.out.println("rtInfo2: " + JsonUtils.toJsonString(errorCodes));
        List<SinexcelErrorCode> expand = List.of(SinexcelErrorCode.DCDC_COMMUNICATION_FAILS,
            SinexcelErrorCode.SLAVE_COMMUNICATION_FAILS);
        Assertions.assertEquals(expand, errorCodes);

    }

    @Test
    void readDeviceData() {
        List<InverterMsgDown> inverterMsgDowns = inverterService.readDeviceData(tid, dno, 1, true);
        System.out.println("inverterMsgDowns: " + JsonUtils.toJsonString(inverterMsgDowns));
        Assertions.assertNotNull(inverterMsgDowns);
        Assertions.assertFalse(inverterMsgDowns.isEmpty());
    }

    @Test
    void readDeviceRtData() {
        List<InverterMsgDown> inverterMsgDowns = inverterService.readDeviceRtData(tid, dno, 1,
            true);
        System.out.println("inverterMsgDowns: " + JsonUtils.toJsonString(inverterMsgDowns));
        Assertions.assertNotNull(inverterMsgDowns);
        Assertions.assertFalse(inverterMsgDowns.isEmpty());
    }

    @Test
    void parseDeviceRtData() {
        String hexString5 =
            "0103903e642d913e97acb6000000000000000000000000000000000000000000000000000000000000000040e577be4180f3"
                + "c1417cde84be9fb0cb0000000000000000000000004146afda40eb9732c09ea67b000000000000000000000000000000000000000000000000000000000000000000000000000"
                + "000000000000000000000000000000000000000000000000000000462";
        byte[] bytes = ByteUtils.hexToBytes(hexString5);

        String json = "{\"deviceId\":1,\"opCode\":3,\"startAddress\":4680,\"num\":72,\"downMsg\":\"010312480048C092\"}";
        InverterMsgDown msgDown = JsonUtils.fromJson(json, InverterMsgDown.class);
        ObjectResponse<InverterRtData> res = inverterService.parseDeviceRtData(tid, dno, msgDown,
            bytes, true);
        System.out.println("rtData: " + JsonUtils.toJsonString(res));
        InverterRtData data = res.getData();
        Assertions.assertEquals(data.getBatLoadRateList(),
            List.of(new BigDecimal("0.22283007"), new BigDecimal("0.29623955")));
    }

    @Test
    void readDeviceFactoryInfo() {
        InverterMsgDown msgDown = inverterService.readDeviceFactoryInfo(tid, dno, 1, true);
        System.out.println("inverterMsgDowns: " + JsonUtils.toJsonString(msgDown));
        Assertions.assertNotNull(msgDown.getDownMsg());
    }

    @Test
    void readDevSysSetting() {
        List<InverterMsgDown> msgDowns = inverterService.readDevSysSetting(tid, dno, 1, true);
        System.out.println("inverterMsgDowns: " + JsonUtils.toJsonString(msgDowns));
        Assertions.assertNotNull(msgDowns);
        Assertions.assertFalse(msgDowns.isEmpty());
    }

    @Test
    void parseDeviceFactoryInfo() {
//        String hexStr = "01030e0030001b001bd0303fdacf2000003eac";
//        String hexStr = "0103140030001b001b000a0019703d0000000000000000a173";
        String hexStr = "010314"
            + "0045"
            + "0025"
            + "0025"
            + "000b"
            + "0019"
            + "00000000000000000000"
            + "f533";
        byte[] bytes = ByteUtils.hexToBytes(hexStr);
        String json = "{\"deviceId\":1,\"opCode\":3,\"startAddress\":8192,\"num\":10,\"downMsg\":\"01032000000ACE0D\"}";
        InverterMsgDown msgDown = JsonUtils.fromJson(json, InverterMsgDown.class);
        ObjectResponse<EssFactoryDto> res = inverterService.parseDeviceFactoryInfo(tid, dno,
            msgDown, bytes,
            true);
        System.out.println("factoryDto: " + JsonUtils.toJsonString(res));
        EssFactoryDto factoryDto = res.getData();
        Assertions.assertEquals(factoryDto.getProtocolVer(), "4.5");
    }

    @Test
    void readAmmeterData() {
        List<InverterMsgDown> msgDowns = inverterService.readAmmeterData(tid, dno, 1, true);
        System.out.println("inverterMsgDowns: " + JsonUtils.toJsonString(msgDowns));
        Assertions.assertNotNull(msgDowns);
        Assertions.assertFalse(msgDowns.isEmpty());
    }

    @Test
    void readBmsData() {
        List<InverterMsgDown> msgDowns = inverterService.readBmsData(tid, dno, 1, true);
        System.out.println("inverterMsgDowns: " + JsonUtils.toJsonString(msgDowns));
        Assertions.assertNotNull(msgDowns);
        Assertions.assertFalse(msgDowns.isEmpty());
    }

    @Test
    void parseBmsData() {
        String hexStr =
            "0103a40000000041000000000000003F8000004000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000"
                + "00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000003f8000003f8000"
                + "004130000000000000000000000000000000000000000000000000000068FB";
        byte[] bytes = ByteUtils.hexToBytes(hexStr);

        String json = "{\"deviceId\":1,\"opCode\":3,\"startAddress\":45056,\"num\":82,\"downMsg\":\"0103B0000052E2F7\"}";
        InverterMsgDown msgDown = JsonUtils.fromJson(json, InverterMsgDown.class);

        List<BmsProtocol> protocols = List.of(BmsProtocol.SOLUNA, BmsProtocol.PYLON);
        ObjectResponse<ModuleBmsRtData> res = inverterService.parseBmsData(tid, dno, msgDown,
            protocols,
            bytes, true);
        System.out.println("res: " + JsonUtils.toJsonString(res));
        ModuleBmsRtData bmsRtData = res.getData();
        Assertions.assertEquals(bmsRtData.getBatteryCupboardList().get(0).getStatus(), List.of(
            BmsBasicStatus.REQUEST_STRONG_CHARGE));
    }

    @Test
    void readDevMonitor() {
        List<InverterMsgDown> msgDowns = inverterService.readDevMonitor(tid, dno, 1, true);
        System.out.println("inverterMsgDowns: " + JsonUtils.toJsonString(msgDowns));
        Assertions.assertNotNull(msgDowns);
        Assertions.assertFalse(msgDowns.isEmpty());
    }

    @Test
    void inOutTimeRangeConfigModify() {
        InOutTimeRangeDto dto = new InOutTimeRangeDto();
        dto.setStrategy(EssCfgStrategy.TIMING_CHARGING_DISCHARGING);

        List<EssInOutStrategyItem> inOutItems = new ArrayList<>();
        inOutItems.add(new EssInOutStrategyItem()
            .setFlowType(ChargeFlowType.CHARGE)
            .setStart(840)
            .setEnd(900));
        inOutItems.add(new EssInOutStrategyItem()
            .setFlowType(ChargeFlowType.DISCHARGE)
            .setStart(900)
            .setEnd(930));
        dto.setInOutItems(inOutItems);

//        String json = "{\"serialNo\":\"E47F23500003\",\"strategy\":\"TIMING_CHARGING_DISCHARGING\",\"supportDivision\":false,\"inOutItems\":[{\"start\":846,\"end\":960,\"flowType\":1,\"activePower\":5},{\"start\":1050,\"end\":1080,\"flowType\":2,\"activePower\":5},{\"start\":1080,\"end\":1110,\"flowType\":1,\"activePower\":5},{\"start\":1110,\"end\":1140,\"flowType\":2,\"activePower\":5},{\"start\":1140,\"end\":1170,\"flowType\":1,\"activePower\":5},{\"start\":1170,\"end\":1200,\"flowType\":2,\"activePower\":5},{\"start\":1200,\"end\":1210,\"flowType\":1,\"activePower\":5},{\"start\":1210,\"end\":1220,\"flowType\":2,\"activePower\":5},{\"start\":1220,\"end\":1225,\"flowType\":1,\"activePower\":5},{\"start\":1225,\"end\":1240,\"flowType\":2,\"activePower\":5},{\"start\":1240,\"end\":1250,\"flowType\":1,\"activePower\":5},{\"start\":1250,\"end\":1260,\"flowType\":2,\"activePower\":5}]}";
        String json = "{\"cfgId\":2256,\"dno\":\"ZV4JK8UL\",\"serialNo\":\"E47F23500005\",\"strategy\":\"TIMING_CHARGING_DISCHARGING\",\"supportDivision\":false,\"inOutItems\":[{\"start\":0,\"end\":540,\"flowType\":1,\"activePower\":3},{\"start\":540,\"end\":1440,\"flowType\":2,\"activePower\":3}]}";
        dto = JsonUtils.fromJson(json, InOutTimeRangeDto.class);
        dto.setMultiParamDelivery(true);
        dto.setOtherStrategy(EssCfgStrategy.STANDBY);
        dto.setRepeatCycle(0x63);
        dto.setEffectiveStartTime(DateUtils.fromYyyyMmDd("2024-08-01")
            .toInstant().getEpochSecond());
        dto.setEffectiveEndTime(DateUtils.fromYyyyMmDd("2024-08-31")
            .toInstant().getEpochSecond());

        List<InverterMsgDown> msgDowns = inverterService.inOutTimeRangeConfigModify(tid, dno, 1,
            dto, true);
        System.out.println("inverterMsgDowns: " + JsonUtils.toJsonString(msgDowns));
        Assertions.assertNotNull(msgDowns);
        Assertions.assertFalse(msgDowns.isEmpty());
    }

    @Test
    void parseParamSetMsg() {
        String hexStr = "01103c460002ac4d";
        byte[] bytes = ByteUtils.hexToBytes(hexStr);

        InverterMsgDown msgDown = new InverterMsgDown();
        msgDown.setDeviceId(1)
            .setOpCode(SinexcelOpCode.SET_PARAMS)
            .setStartAddress(SinexcelConstants.CLEAR_ALARM_ADDRESS)
            .setNum(2);
        BaseResponse res = inverterService.parseParamSetMsg(tid, dno, msgDown, bytes,
            true);
        System.out.println("res: " + res);
        Assertions.assertEquals(0, res.getStatus());

        hexStr = "0190030c01";
        bytes = ByteUtils.hexToBytes(hexStr);
        res = inverterService.parseParamSetMsg(tid, dno, msgDown, bytes,
            true);
        System.out.println("res: " + res);
        Assertions.assertEquals(1, res.getStatus());
    }

    @Test
    void generateOnOffInstructionMsg() {
        boolean on = false;
        List<InverterMsgDown> msgDowns = inverterService.generateOnOffInstructionMsg(tid, dno, 1,
            on, true);
        System.out.println("inverterMsgDowns: " + JsonUtils.toJsonString(msgDowns));
        Assertions.assertNotNull(msgDowns);
        Assertions.assertFalse(msgDowns.isEmpty());
    }

    @Test
    void readClearAlarm() {
        List<InverterMsgDown> msgDowns = inverterService.readClearAlarm(tid, dno, 1, true);
        System.out.println("inverterMsgDowns: " + JsonUtils.toJsonString(msgDowns));
        Assertions.assertNotNull(msgDowns);
        Assertions.assertFalse(msgDowns.isEmpty());
    }

    @Test
    void generaClearAlarmMsg() {
        List<InverterMsgDown> msgDowns = inverterService.generateClearAlarmMsg(tid, dno, 1, true);
        System.out.println("inverterMsgDowns: " + JsonUtils.toJsonString(msgDowns));
        Assertions.assertNotNull(msgDowns);
        Assertions.assertFalse(msgDowns.isEmpty());
    }

    @Test
    void connectUpgradeDevice() {
        InverterMsgDown msgDown = inverterService.connectUpgradeDevice(tid, dno, 1,
            UpgradeFileType.ARM, 123L, true);
        System.out.println("inverterMsgDowns: " + JsonUtils.toJsonString(msgDown));
        Assertions.assertNotNull(msgDown);
        System.out.println("byteMsg: " + ByteUtils.bytesToHex(msgDown.getByteMsg()));
    }

    @Test
    void startUpgrade() {
        InverterMsgDown msgDown = inverterService.startUpgrade(tid, dno, 1,
            33, SinexcelConstants.MAXIMUM_PACKET_LENGTH, true);
        System.out.println("inverterMsgDowns: " + JsonUtils.toJsonString(msgDown));
        Assertions.assertNotNull(msgDown);
        System.out.println("byteMsg: " + ByteUtils.bytesToHex(msgDown.getByteMsg()));
    }

    @Test
    void sendUpgradePackageData() {
        String binFilePath = "/tmp/Isuna6000S_ARM_App_V120B008.bin";
        List<InverterMsgDown> msgDowns = inverterService.sendUpgradePackageData(tid, dno, 1,
            binFilePath, SinexcelConstants.MAXIMUM_PACKET_LENGTH, true);
        System.out.println("inverterMsgDowns: " + JsonUtils.toJsonString(msgDowns));
        Assertions.assertNotNull(msgDowns);
        Assertions.assertFalse(msgDowns.isEmpty());
        msgDowns.forEach(e ->
            System.out.println("byteMsg item: " + ByteUtils.bytesToHex(e.getByteMsg())));
    }

    @Test
    void upgradeCompleted() {
        String binFilePath = "/tmp/Isuna6000S_ARM_App_V120B008.bin";
        InverterMsgDown msgDowns = inverterService.upgradeCompleted(tid, dno, 1,
            binFilePath, true);
        System.out.println("inverterMsgDowns: " + JsonUtils.toJsonString(msgDowns));
        Assertions.assertNotNull(msgDowns);
        System.out.println("byteMsg: " + ByteUtils.bytesToHex(msgDowns.getByteMsg()));
    }

    @Test
    void parseUpgradeResp() {
        String json = "{\"deviceId\":1,\"opCode\":35,\"num\":4,\"byteMsg\":\"ASMABB+IVepb5A==\"}";
        InverterMsgDown msgDown = JsonUtils.fromJson(json, InverterMsgDown.class);

        String hexStr = "01230002AA05DB6E";
        byte[] bytes = ByteUtils.hexToBytes(hexStr);

        ObjectResponse<InverterUpgradeRes> res = inverterService.parseUpgradeResp(tid, dno, msgDown,
            bytes, true);
        System.out.println("res: " + JsonUtils.toJsonString(res));
        Assertions.assertEquals(0, res.getStatus());
        InverterUpgradeRes upgradeRes = res.getData();
        Assertions.assertEquals(upgradeRes.getFileType(), UpgradeFileType.ARM);
        Assertions.assertEquals(upgradeRes.getStatusCode(), UpgradeStatusCode.UPGRADE_SUCCESS);
    }

}