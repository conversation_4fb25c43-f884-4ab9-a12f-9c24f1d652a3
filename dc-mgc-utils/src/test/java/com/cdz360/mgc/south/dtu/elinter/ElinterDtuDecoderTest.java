package com.cdz360.mgc.south.dtu.elinter;

import com.cdz360.base.utils.ByteUtils;
import com.cdz360.mgc.model.ElinterEssDtu;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class ElinterDtuDecoderTest {

    private ElinterDtuDecoder elinterDtuDecoder = new ElinterDtuDecoder();

    @Test
    void parseBasicInfo() {
        String hexString = "aa5502a2010000000004007e4534374632333530303030330000000007026d616770696500000000000000000000454553572d4134313300000000000000414547342d303031312d30300000000000000000343746303232334132315200000000000000000000000000000000000000000000000000000000004142574834514c4b0000000000000000276c";
        byte[] buf = ByteUtils.hexToBytes(hexString.replaceAll("\\s*", ""));
        ElinterEssDtu elinterEssDtu = elinterDtuDecoder.parseBasicInfo(true, buf);
        System.out.println("elinterEssDtu: " + elinterEssDtu.toString());
        Assertions.assertEquals("E47F23500003", elinterEssDtu.getSerialNo());
    }
}