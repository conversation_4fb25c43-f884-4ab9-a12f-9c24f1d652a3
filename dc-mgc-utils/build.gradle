plugins {
    id 'java'
}

jar {
    enabled = true
}


dependencies {

    implementation project(':dc-base-model')
    implementation project(':dc-base-utils')

    implementation("org.slf4j:slf4j-api:${slf4jVersion}")

    implementation("com.fasterxml.jackson.core:jackson-core:${jacksonVersion}")
    implementation("com.fasterxml.jackson.core:jackson-annotations:${jacksonVersion}")
    implementation("com.fasterxml.jackson.core:jackson-databind:${jacksonVersion}")

    testImplementation("com.fasterxml.jackson.datatype:jackson-datatype-jsr310:${jacksonVersion}")
//    implementation     "com.fasterxml.jackson.datatype:jackson-datatype-jsr310"

    // https://mvnrepository.com/artifact/com.google.code.gson/gson
    implementation group: 'com.google.code.gson', name: 'gson', version: '2.8.9'

}