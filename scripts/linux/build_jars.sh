#!/bin/bash

#export JAVA_HOME=/usr/lib/jvm/java-17-openjdk-amd64/


echo "BRANCH = ${BRANCH}"
if [ -z "$BRANCH" ]
then
  echo "miss parameter: BRANCH !!!"
  exit -1
fi

VERSION="`date +%Y%m%d_%H%M`_${BRANCH^^}-SNAPSHOT"
echo "VERSION = ${VERSION}"

if [ ! -z "$JAVA_HOME" ]
then
  PATH="${JAVA_HOME}/bin:${PATH}"
fi

echo "JAVA_HOME=${JAVA_HOME}"
echo "PATH=${PATH}"
java --version

git checkout build.gradle
sed -i "s/###!!!DC_BASE_VERSION!!!###/${VERSION}/g" build.gradle

echo ""
echo "################################"
echo ""
echo "reversion to: ${VERSION} "
echo ""
echo "################################"
echo ""

./gradlew clean
if [ $? -ne 0 ]; then
  echo "gradle clean failed!!!"
  echo ""
  git checkout build.gradle
  exit -1
else
  echo "gradle clean success"
fi

packages=( dc-base-model dc-base-utils dc-base-ds dc-data-cache dc-data-cache-reader dc-data-cache-writer dc-data-sync dc-data-sync-publisher dc-mgc-utils ess-sync-publisher )
for package in "${packages[@]}"; do
    echo "start build package: $package"
    echo ""
    ./gradlew publish -p $package

    if [ $? -ne 0 ]; then
      echo "build package ${package} failed!!!"
      echo ""
      git checkout build.gradle
      exit -1
    else
      echo "build package success: ${package}"
      echo ""
    fi
done
git checkout build.gradle
echo ""
echo "build all jars success"
echo ""


