plugins {
    id 'java'
}

jar {
    enabled = true
}


dependencies {

    implementation project(':dc-base-model')

    implementation("org.slf4j:slf4j-api:${slf4jVersion}")

    implementation("com.fasterxml.jackson.core:jackson-core:${jacksonVersion}")
    implementation("com.fasterxml.jackson.core:jackson-annotations:${jacksonVersion}")
    implementation("com.fasterxml.jackson.core:jackson-databind:${jacksonVersion}")

    implementation("com.fasterxml.jackson.datatype:jackson-datatype-jsr310:${jacksonVersion}")
//    implementation     "com.fasterxml.jackson.datatype:jackson-datatype-jsr310"
}