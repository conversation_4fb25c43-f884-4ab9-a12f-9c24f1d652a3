package com.cdz360.base.utils;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;

@Slf4j
public class TestDecimalUtils {
    @Test
    public void test_device100_Integer() {
        BigDecimal ret = DecimalUtils.divide100(124);


        ret = DecimalUtils.divide100(125);

        BigDecimal a = null;
        DecimalUtils.multiply100(a);
        DecimalUtils.yuan2fen(a);
    }


    @Test
    public void test_device100_Long() {
        BigDecimal ret = DecimalUtils.divide100(124L);
        Assertions.assertEquals("1.24", ret.toPlainString());


        ret = DecimalUtils.divide100(125L);
        Assertions.assertEquals("1.25", ret.toString());

    }

    @Test
    public void test_device10000() {
        BigDecimal ret = DecimalUtils.divide10000(124L);
        Assertions.assertEquals("0.0124", ret.toPlainString());


        ret = DecimalUtils.divide10000(125L);
        Assertions.assertEquals("0.0125", ret.toString());
    }

    @Test
    public void test_getDecimalPlacesNum() {
        BigDecimal val = new BigDecimal("1.000");
        int res = DecimalUtils.getDecimalPlacesNum(val);
        Assertions.assertEquals(res, 3);
        val = new BigDecimal("1."); // == new BigDecimal("1")
        log.info("val={}", val.toPlainString());
        res = DecimalUtils.getDecimalPlacesNum(val);
        Assertions.assertEquals(res, 0);

//        int i = new BigDecimal(val.multiply(BigDecimal.valueOf(100)).longValue()).compareTo(val.multiply(BigDecimal.valueOf(100)));
//        Assertions.assertTrue(i == 0);
    }

    @Test
    public void test_isZero() {
        Assertions.assertTrue(DecimalUtils.isZero(new BigDecimal("0.00")));
        Assertions.assertTrue(DecimalUtils.isZero(new BigDecimal("0")));
        Assertions.assertTrue(DecimalUtils.isZero(BigDecimal.valueOf(0L)));
        Assertions.assertFalse(DecimalUtils.isZero(new BigDecimal("0.01")));
    }

    @Test
    public void test_lt() {
        Assertions.assertTrue(DecimalUtils.lt(BigDecimal.valueOf(100), BigDecimal.valueOf(123)));
        Assertions.assertFalse(DecimalUtils.lt(BigDecimal.valueOf(100), BigDecimal.valueOf(100)));
        Assertions.assertFalse(DecimalUtils.lt(BigDecimal.valueOf(123), BigDecimal.valueOf(100)));
    }

    @Test
    public void test_lte() {
        Assertions.assertTrue(DecimalUtils.lte(BigDecimal.valueOf(100), BigDecimal.valueOf(123)));
        Assertions.assertTrue(DecimalUtils.lte(BigDecimal.valueOf(100), BigDecimal.valueOf(100)));
        Assertions.assertFalse(DecimalUtils.lte(BigDecimal.valueOf(123), BigDecimal.valueOf(100)));
    }

    @Test
    public void test_gt() {
        Assertions.assertFalse(DecimalUtils.gt(BigDecimal.valueOf(100), BigDecimal.valueOf(123)));
        Assertions.assertFalse(DecimalUtils.gt(BigDecimal.valueOf(100), BigDecimal.valueOf(100)));
        Assertions.assertTrue(DecimalUtils.gt(BigDecimal.valueOf(123), BigDecimal.valueOf(100)));
    }

    @Test
    public void test_gte() {
        Assertions.assertFalse(DecimalUtils.gte(BigDecimal.valueOf(100), BigDecimal.valueOf(123)));
        Assertions.assertTrue(DecimalUtils.gte(BigDecimal.valueOf(100), BigDecimal.valueOf(100)));
        Assertions.assertTrue(DecimalUtils.gte(BigDecimal.valueOf(123), BigDecimal.valueOf(100)));
    }

    @Test
    public void test_roundP2() {
        Assertions.assertEquals(new BigDecimal("123.45"), DecimalUtils.roundS2(new BigDecimal("123.454")) );
        Assertions.assertEquals(new BigDecimal("123.46"), DecimalUtils.roundS2(new BigDecimal("123.455")) );
        Assertions.assertEquals(new BigDecimal("123.40"), DecimalUtils.roundS2(new BigDecimal("123.4")) );
    }
}
