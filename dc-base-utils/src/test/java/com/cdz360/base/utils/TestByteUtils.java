package com.cdz360.base.utils;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

@Slf4j
public class TestByteUtils {



    @Test
    public void test_reverseBytes_01() {
        String hex = "0102030405060708090A0B0C0D0E0F10";
        byte[] bytes = ByteUtils.hexToBytes(hex);
        byte[] result = ByteUtils.reverseBytes(bytes);
        log.info("result = {}", ByteUtils.bytesToHex(result));
    }

    @Test
    public void test_reverseBytes_02() {
        String hex = "0102030405060708090A0B0C0D0E0F10";
        byte[] bytes = ByteUtils.hexToBytes(hex);
        byte[] result = ByteUtils.reverseBytes(bytes, 2,3);
        log.info("result = {}", ByteUtils.bytesToHex(result));
        Assertions.assertEquals("050403", ByteUtils.bytesToHex(result));
    }
}
