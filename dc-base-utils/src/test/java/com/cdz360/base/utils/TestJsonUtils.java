package com.cdz360.base.utils;

import com.cdz360.base.model.base.type.ChargeOrderStatus;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.charge.type.OrderStartType;
import com.cdz360.base.model.charge.vo.ChargeOrderVo;
import com.cdz360.base.utils.model.DateTimeVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.UUID;
import lombok.Data;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class TestJsonUtils {
    private static final Logger logger = LoggerFactory.getLogger(TestJsonUtils.class);


    @Test
    public void test_fromJson() {
        String str = " {\"orderNo\":\"87c74b37-8661-46d1-823c-db17f80e96b2\",\"status\":\"START\",\"evseNo\":\"13000e29-b7be-4814-8f39-575dc9eaf83d\",\"plugIdx\":3,\"cusId\":123456,\"cusPhone\":\"***********\",\"cusCommId\":33212,\"cusCommName\":\"a70e993f-cc24-4758-988e-6d64c1c160fa\",\"siteId\":\"cb825dbe-f4ed-405e-8c8e-e3b4845c4c06\",\"siteCommId\":456789,\"cardNo\":\"0a30df06-38e7-40e1-b1a3-b92f8a2b374f\",\"cardChipNo\":\"b7989761-7ef8-4144-9bb0-55cc57372b94\",\"vin\":\"19f78c35-7710-4e6d-9a40-a55826c13ab5\",\"payAccountId\":123455,\"payAccountType\":\"PERSONAL\",\"kwh\":233456,\"elecFee\":3452334,\"servFee\":*********,\"startTime\":*************,\"stopTime\":*************,\"startSoc\":34,\"curSoc\":39,\"createTime\":*************}";
        long startTime = System.nanoTime();
        for (int i = 0; i < 50000; i++) {
            ChargeOrderVo o = JsonUtils.fromJson(str, ChargeOrderVo.class);
        }
        long endTime = System.nanoTime();

        //logger.info(JsonUtils.toJsonString(o));
        logger.info("time = {}", (endTime - startTime) / 1000);
    }


    @Test
    public void test_toJsonList_string() {
        List<String> list = new ArrayList<>();
        list.add("aaa");
        list.add("bbb");
        String str = JsonUtils.toJsonString(list);
        logger.info("str = {}", str);
        List<String> result =   JsonUtils.toJsonList(str);
        logger.info("result = {}", result);
    }

    @Test
    public void test_toJsonList_json() {

        ArrayNode jsonArray = JsonNodeFactory.instance.arrayNode();
        ObjectNode rootNode = JsonNodeFactory.instance.objectNode();
        jsonArray.add("aaa");
        jsonArray.add("bbb");
        jsonArray.add("ccc");
        rootNode.put("xList", jsonArray);

        logger.info("str = {}", JsonUtils.toJsonString(rootNode));
        List<String> strList = JsonUtils.toJsonList(rootNode, "xList");
        logger.info("strList = {}", strList);
//        List<String> result =   JsonUtils.toJsonList(str);
//        logger.info("result = {}", result);
    }

    @Data
    public static class Ta {
        private OrderStartType type;

        @JsonSerialize(using = LocalDateTimeSerializer.class)
        @JsonDeserialize(using = LocalDateTimeDeserializer.class)
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
        private LocalDateTime time;
    }

    @Test
    public void test_localDateTime() {
        Ta ta = new Ta();
        ta.setType(OrderStartType.EVSE_AUTO);
        ta.setTime(LocalDateTime.now());
        String json = JsonUtils.toJsonString(ta);
        logger.info("json = {}", json);

        Ta tb = JsonUtils.fromJson(json, Ta.class);
        logger.info("tb = {}", tb);

    }

    @Test
    public void test_toJsonString_localDateTime() {
        Map<String, Object> map = new HashMap<>();
        map.put("aaa", LocalDateTime.now());
        map.put("bbb", ZonedDateTime.now());
        map.put("ccc", LocalDate.now());
        map.put("ddd", new Date());
        logger.info("aaa = {}", map.get("aaa"));
        logger.info("bbb = {}", map.get("bbb"));
        logger.info("ccc = {}", map.get("ccc"));
        logger.info("ddd = {}", map.get("ddd"));

        String jsonStr = JsonUtils.toJsonString(map, "yyyy-MM-dd HH:mm:ss",
            Locale.CHINA);
        logger.info("jsonStr = {}", jsonStr);

        jsonStr = JsonUtils.toJsonTimeString(map);
        logger.info("jsonStr = {}", jsonStr);

        Map<String, Object> map1 = JsonUtils.fromJsonTime(jsonStr, new TypeReference<Map<String, Object>>() {});
        logger.info("aaa = {}", map1.get("aaa"));
        logger.info("bbb = {}", map1.get("bbb"));
        logger.info("ccc = {}", map1.get("ccc"));
        logger.info("ddd = {}", map1.get("ddd"));

        String jsonStr2 = "{\"connectorId\":1,\"errorCode\":\"NoError\",\"status\":\"Finishing\",\"info\":\"\",\"timestamp3\":\"2025-06-06T08:44:38.958Z\",\"timestamp2\":\"2025-06-11 14:29:47\",\"timestamp1\":\"2025-06-11\",\"timestamp4\":\"1749623387788\",\"timestamp5\":\"2025-06-11T14:36:19+08:00\",\"vendorId\":\"winline\",\"vendorErrorCode\":\"\"}";
        DateTimeVo dateTimeVo = JsonUtils.fromJsonTime(jsonStr2, DateTimeVo.class);
        logger.info("dateTimeVo: {} ", JsonUtils.toJsonTimeString(dateTimeVo));
         jsonStr = JsonUtils.toJsonString(dateTimeVo, "yyyy-MM-dd HH:mm:ss",
            Locale.CHINA);
        logger.info("jsonStr end = {}", jsonStr);
    }

    @Test
    public void test_toJsonString() {
        ChargeOrderVo o = new ChargeOrderVo();
        o.setOrderNo(UUID.randomUUID().toString())
                .setStatus(ChargeOrderStatus.START)
                .setEvseNo(UUID.randomUUID().toString())
                .setPlugIdx(3)
                .setCusId(123456L)
                .setCusPhone("***********")
                .setCusCommId(33212L)
                .setCusCommName(UUID.randomUUID().toString())
                .setSiteId(UUID.randomUUID().toString())
                .setSiteCommId(456789L)
                .setCardNo(UUID.randomUUID().toString())
                .setCardChipNo(UUID.randomUUID().toString())
                .setVin(UUID.randomUUID().toString())
                .setPayAccountId(123455L)
                .setPayAccountType(PayAccountType.PERSONAL)
                .setKwh(new BigDecimal("23.3456"))
                .setElecFee(new BigDecimal("34523.34"))
                .setServFee(new BigDecimal("3243254.35"))
                .setStartTime(new Date())
                .setStopTime(new Date())
                .setStartSoc(34)
                .setCurSoc(39)
                .setCreateTime(new Date());
        long startTime = System.nanoTime();
        for (int i = 0; i < 50000; i++) {

            String ret = JsonUtils.toJsonString(o);
        }
        long endTime = System.nanoTime();

        //logger.info(JsonUtils.toJsonString(o));
        logger.info("time = {}", (endTime - startTime) / 1000);
        //logger.info(ret);
    }
}
