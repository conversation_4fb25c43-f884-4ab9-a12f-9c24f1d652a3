package com.cdz360.base.utils;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class TestNumberUtils {


    @Test
    public void test_split2Long() {
        String val = "111,222 ,333, 444,555";
        List<Long> list = NumberUtils.split2Long(val);
        log.info("list = {}", list);
        Assertions.assertEquals(5, list.size());
    }

    @Test
    public void test_split2Long_fail() {
        String val = "111,222x ,333, 444,555";
        List<Long> list = NumberUtils.split2Long(val);
        log.info("list = {}", list);
        Assertions.assertEquals(0, list.size());
    }

    @Test
    public void test_split2Integer() {
        String val = "111,222 ,333, 444,555";
        List<Integer> list = NumberUtils.split2Integer(val);
        log.info("list = {}", list);
        Assertions.assertEquals(5, list.size());
    }

    @Test
    public void test_split2Integer_empty() {
        String val = " ";
        List<Integer> list = NumberUtils.split2Integer(val);
        log.info("list = {}", list);
        Assertions.assertEquals(0, list.size());
    }

    @Test
    public void test_split2Integer_fail() {
        String val = "111,222x ,333, 444,555";
        List<Integer> list = NumberUtils.split2Integer(val);
        log.info("list = {}", list);
        Assertions.assertEquals(0, list.size());
    }


    @Test
    public void test_compareLong() {
        Long x = 3L;
        Long y= 5L;
        log.info("x = {}, y = {}, ret = {}", x, y, x.compareTo(y));
        Assertions.assertEquals(x.compareTo(y), NumberUtils.compareLong(x, y));

        y = 2L;
        log.info("x = {}, y = {}, ret = {}", x, y, x.compareTo(y));
        Assertions.assertEquals(x.compareTo(y), NumberUtils.compareLong(x, y));

//        a.compareTo(b)
        List<Long> list = List.of(10L, 5L, 9L, 6L);
        List<Long> ret1 = list.stream().sorted().collect(Collectors.toList());
        log.info("ret1 = {}", ret1);
        List<Long> ret2 = list.stream().sorted((a, b) -> NumberUtils.compareLong(a, b))
                .collect(Collectors.toList());
        log.info("ret2 = {}", ret2);

        Assertions.assertEquals(0, NumberUtils.compareLong(null, null));
        Assertions.assertEquals(1, NumberUtils.compareLong(3L, null));
        Assertions.assertEquals(-1, NumberUtils.compareLong(null, 5L));
    }


    @Test
    public void test_compareInteger() {
        Integer x = 3;
        Integer y= 5;
        log.info("x = {}, y = {}, ret = {}", x, y, x.compareTo(y));
        Assertions.assertEquals(x.compareTo(y), NumberUtils.compareInteger(x, y));

        y = 2;
        log.info("x = {}, y = {}, ret = {}", x, y, x.compareTo(y));
        Assertions.assertEquals(x.compareTo(y), NumberUtils.compareInteger(x, y));

//        a.compareTo(b)
        List<Integer> list = List.of(10, 5, 9, 6);
        List<Integer> ret1 = list.stream().sorted().collect(Collectors.toList());
        log.info("ret1 = {}", ret1);
        List<Integer> ret2 = list.stream().sorted((a, b) -> NumberUtils.compareInteger(a, b))
                .collect(Collectors.toList());
        log.info("ret2 = {}", ret2);

        Assertions.assertEquals(0, NumberUtils.compareInteger(null, null));
        Assertions.assertEquals(1, NumberUtils.compareInteger(3, null));
        Assertions.assertEquals(-1, NumberUtils.compareInteger(null, 5));
    }
}
