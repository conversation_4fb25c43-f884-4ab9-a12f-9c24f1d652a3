package com.cdz360.base.utils;

import com.cdz360.base.model.base.exception.DcServiceException;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.slf4j.event.Level;

@Slf4j
public class TestLogHelper {


    @Test
    public void test_logException() {
        LogHelper.logException(log, new DcServiceException("test error log level", Level.ERROR));
        LogHelper.logException(log, new DcServiceException("test warn log level", Level.WARN));
        LogHelper.logException(log, new DcServiceException("test info log level", Level.INFO));
        LogHelper.logException(log, new DcServiceException("test debug log level", Level.DEBUG));
        LogHelper.logException(log, new DcServiceException("test trace log level", Level.TRACE));
        Level logLevel = null;
        LogHelper.logException(log, new DcServiceException("test no log level", logLevel));
    }
}
