package com.cdz360.base.utils;

import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class NumberUtils {
    private static final String SPLITOR = ",";

    public static long parseLong(String val, long defaultValue) {
        long ret = defaultValue;
        try {
            ret = Long.parseLong(val);
        } catch (Exception ignored) {

        }
        return ret;
    }

    public static long parseLong(byte[] val, long defaultValue) {
        if (val == null) {
            return defaultValue;
        } else {
            return NumberUtils.parseLong(new String(val), defaultValue);
        }
    }

    public static int parseInt(String val, int defaultValue) {
        int ret = defaultValue;
        try {
            ret = Integer.parseInt(val);
        } catch (Exception ignored) {

        }
        return ret;
    }

    public static int parseInt(byte[] val, int defaultValue) {
        if (val == null) {
            return defaultValue;
        } else {
            return NumberUtils.parseInt(new String(val), defaultValue);
        }
    }

    public static boolean equals(Integer right, Integer left) {
        if (right == null && left == null) {
            return true;
        } else if (right != null && left != null) {
            return right.intValue() == left.intValue();
        } else {
            return false;
        }
    }

    public static boolean equals(BigDecimal right, BigDecimal left) {
        if (right == null && left == null) {
            return true;
        } else if (right != null && left != null) {
            return right.equals(left);
        } else {
            return false;
        }
    }

    public static boolean equals(Long right, Long left) {
        if (right == null && left == null) {
            return true;
        } else if (right != null && left != null) {
            return right.longValue() == left.longValue();
        } else {
            return false;
        }
    }

    public static boolean isZero(Long val) {
        if (val == null) return false;
        return val == 0;
    }

    public static boolean isZero(Integer val) {
        if (val == null) return false;
        return val == 0;
    }

    public static boolean gtZero(Long val) {
        if (val == null) return false;
        return val > 0;
    }

    public static boolean gtZero(Integer val) {
        if (val == null) return false;
        return val > 0;
    }

    public static boolean gteZero(Long val) {
        if (val == null) return false;
        return val >= 0;
    }

    public static boolean gteZero(Integer val) {
        if (val == null) return false;
        return val >= 0;
    }

    /**
     * 比较a, b大小
     * @param a 入参
     * @param b 入参
     * @return
     * a &lt; b return -1
     * a == b return 0
     * a &gt; b return 1
     * a == null and b == null return 0
     * a == null return -1
     * b == null return 1
     */
    public static int compareLong(Long a, Long b) {
        if(a == null && b == null ) return 0;
        else if (a == null) return -1;
        else if (b == null) return 1;
        else {
            return a.compareTo(b);
        }
    }

    public static int compareInteger(Integer a, Integer b) {
        if(a == null && b == null ) return 0;
        else if (a == null) return -1;
        else if (b == null) return 1;
        else {
            return a.compareTo(b);
        }
    }

    /**
     * 将字符串 111,222,333,444 转换为Long型的数组
     *
     * @param val 入参
     * @return 返回long型数组
     */
    public static List<Long> split2Long(String val) {
        return NumberUtils.split2Long(val, SPLITOR);
    }

    public static List<Long> split2Long(String val, String spliter) {
        List<Long> list = List.of();
        if (StringUtils.isBlank(val)) {
            return list;
        }
        try {
            list = Arrays.stream(val.replaceAll(StringUtils.BLANK, StringUtils.EMPTY).split(spliter))
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.warn("error = {}, val = {}", e.getMessage(), val);
        }
        return list;
    }

    /**
     * 将字符串 111,222,333,444 转换为Integer型的数组
     *
     * @param val 入参
     * @return 返回Integer型数组
     */
    public static List<Integer> split2Integer(String val) {
        return NumberUtils.split2Integer(val, SPLITOR);
    }

    public static List<Integer> split2Integer(String val, String spliter) {
        List<Integer> list = List.of();
        if (StringUtils.isBlank(val)) {
            return list;
        }
        try {
            list = Arrays.stream(val.replaceAll(StringUtils.BLANK, StringUtils.EMPTY).split(spliter))
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.warn("error = {}, val = {}", e.getMessage(), val);
        }
        return list;
    }


    public static byte[] long2Bytes(long data) {
        return new byte[]{
                (byte) ((data >> 56) & 0xff),
                (byte) ((data >> 48) & 0xff),
                (byte) ((data >> 40) & 0xff),
                (byte) ((data >> 32) & 0xff),
                (byte) ((data >> 24) & 0xff),
                (byte) ((data >> 16) & 0xff),
                (byte) ((data >> 8) & 0xff),
                (byte) ((data >> 0) & 0xff),
        };
    }


    public static Integer sum(Integer va, Integer vb) {
        return NumberUtils.sum(va, vb, 0);
    }

    public static Integer sum(Integer va, Integer vb, Integer defVal) {
        if (va == null && vb == null) {
            return defVal;
        } else if (va == null) {
            return vb;
        } else if (vb == null) {
            return va;
        } else {
            return va + vb;
        }
    }

    public static Long sum(Long va, Long vb) {
        return NumberUtils.sum(va, vb, 0L);
    }

    public static Long sum(Long va, Long vb, Long defVal) {
        if (va == null && vb == null) {
            return defVal;
        } else if (va == null) {
            return vb;
        } else if (vb == null) {
            return va;
        } else {
            return va + vb;
        }
    }


}
