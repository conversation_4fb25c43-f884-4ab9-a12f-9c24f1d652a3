package com.cdz360.base.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class DecimalUtils {

    public static final int SCALE_2 = 2;
    public static final BigDecimal ZERO = BigDecimal.valueOf(0, SCALE_2);

    /**
     * 2位小数的分转化为元
     *
     * @param fen 分
     * @return 返回元
     */
    public static BigDecimal fen2Yuan(long fen) {
        return DecimalUtils.divide100(fen);
    }

    public static BigDecimal divide10(Long inVal) {
        return DecimalUtils.divide10(inVal, null);
    }

    public static BigDecimal divide10(Long inVal, Long defaultValue) {
        BigDecimal inDecimal;
        if (inVal == null && defaultValue == null) {
            return null;
        } else if (inVal == null) {
            inDecimal = new BigDecimal(defaultValue);
        } else {
            inDecimal = new BigDecimal(inVal);
        }
//        return inDecimal.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
        return inDecimal.movePointLeft(1);
    }

    public static BigDecimal divide10(Integer inVal) {
        return DecimalUtils.divide10(inVal, null);
    }

    public static BigDecimal divide10(Integer inVal, Integer defaultValue) {
        BigDecimal inDecimal;
        if (inVal == null && defaultValue == null) {
            return null;
        } else if (inVal == null) {
            inDecimal = new BigDecimal(defaultValue);
        } else {
            inDecimal = new BigDecimal(inVal);
        }
//        return inDecimal.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
        return inDecimal.movePointLeft(1);
    }

    public static BigDecimal divide100(Integer inVal) {
        return DecimalUtils.divide100(inVal, null);
    }

    public static BigDecimal divide100(Integer inVal, Integer defaultValue) {
        BigDecimal inDecimal;
        if (inVal == null && defaultValue == null) {
            return null;
        } else if (inVal == null) {
            inDecimal = new BigDecimal(defaultValue);
        } else {
            inDecimal = new BigDecimal(inVal);
        }
//        return inDecimal.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
        return inDecimal.movePointLeft(2);
    }

    public static BigDecimal divide100(Long inVal) {
        return DecimalUtils.divide100(inVal, null);
    }

    public static BigDecimal divide100(Long inVal, Long defaultValue) {
        BigDecimal inDecimal;
        if (inVal == null && defaultValue == null) {
            return null;
        } else if (inVal == null) {
            inDecimal = new BigDecimal(defaultValue);
        } else {
            inDecimal = new BigDecimal(inVal);
        }
        return inDecimal.movePointLeft(2);
    }



    public static BigDecimal divide1000(Integer inVal) {
        return DecimalUtils.divide1000(inVal, null);
    }

    public static BigDecimal divide1000(Integer inVal, Integer defaultValue) {
        BigDecimal inDecimal;
        if (inVal == null && defaultValue == null) {
            return null;
        } else if (inVal == null) {
            inDecimal = new BigDecimal(defaultValue);
        } else {
            inDecimal = new BigDecimal(inVal);
        }
//        return inDecimal.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
        return inDecimal.movePointLeft(3);
    }

    public static BigDecimal divide1000(Long inVal) {
        return DecimalUtils.divide1000(inVal, null);
    }

    public static BigDecimal divide1000(Long inVal, Long defaultValue) {
        BigDecimal inDecimal;
        if (inVal == null && defaultValue == null) {
            return null;
        } else if (inVal == null) {
            inDecimal = new BigDecimal(defaultValue);
        } else {
            inDecimal = new BigDecimal(inVal);
        }
        return inDecimal.movePointLeft(3);
    }

    public static BigDecimal divide10000(Long inVal) {
        return DecimalUtils.divide10000(inVal, null);
    }

    public static BigDecimal divide10000(Long inVal, Long defaultValue) {
        BigDecimal inDecimal;
        if (inVal == null && defaultValue == null) {
            return null;
        } else if (inVal == null) {
            inDecimal = new BigDecimal(defaultValue);
        } else {
            inDecimal = new BigDecimal(inVal);
        }
//        return inDecimal.divide(new BigDecimal(10000), 4, RoundingMode.HALF_UP);
        return inDecimal.movePointLeft(4);
    }

    public static Integer multiply10(BigDecimal inVal) {
        if (inVal == null) {
            return null;
        }
        return inVal.movePointRight(1).intValue();
    }

    public static BigDecimal multiply100(BigDecimal inVal) {
        if (inVal == null) {
            return null;
        }
        return inVal.movePointRight(2);
    }

    public static BigDecimal multiply1000(BigDecimal inVal) {
        if (inVal == null) {
            return null;
        }
        return inVal.movePointRight(3);
    }

    public static BigDecimal multiply10000(BigDecimal inVal) {
        if (inVal == null) {
            return null;
        }
        return inVal.movePointRight(4);
    }

    public static Long yuan2fen(BigDecimal inVal) {
        if (inVal == null) {
            return null;
        }
        return multiply100(inVal).longValue();
    }



    /**
     * 获取 BigDecimal 小数位数
     *
     * @param val 入参
     * @return 返回小数位数
     */
    public static int getDecimalPlacesNum(BigDecimal val) {
        String strVal = val.toPlainString();
        int idx = strVal.indexOf(".");
        return idx < 0 ? 0 : strVal.length() - idx - 1;
    }


    public static boolean isZero(BigDecimal val) {
        if (val == null) {
            return false;
        }
        return BigDecimal.ZERO.compareTo(val) == 0;
    }

    /**
     *
     * @param left left value
     * @param right right value
     * @return true if left is less than right
     */
    public static boolean lt(BigDecimal left, BigDecimal right) {
        return left.compareTo(right) < 0;
    }

    public static boolean lte(BigDecimal left, BigDecimal right) {
        return left.compareTo(right) < 1;
    }

    /**
     * 判断 val 是否 小于 0
     * @param val 参数
     * @return val 小于 0 返回 true; 否则返回 false
     */
    public static boolean ltZero(BigDecimal val) {
        return val.compareTo(BigDecimal.ZERO) < 0;
    }

    /**
     * 判断 val 是否 小于等于 0
     * @param val 参数
     * @return val 小于等于 0 返回 true; 否则返回 false
     */
    public static boolean lteZero(BigDecimal val) {
        return val.compareTo(BigDecimal.ZERO) < 1;
    }

    public static boolean gt(BigDecimal left, BigDecimal right) {
        return left.compareTo(right) > 0;
    }
    public static boolean gte(BigDecimal left, BigDecimal right) {
        return left.compareTo(right) > -1;
    }

    /**
     * 判断 val 是否 大于 0
     * @param val 参数
     * @return val 大于 0 返回 true; 否则返回 false
     */
    public static boolean gtZero(BigDecimal val) {
        return val.compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * 判断 val 是否 大于等于 0
     * @param val 参数
     * @return val 大于等于 0 返回 true; 否则返回 false
     */
    public static boolean gteZero(BigDecimal val) {
        return val.compareTo(BigDecimal.ZERO) > -1;
    }

    /**
     * 两个值是否相等
     *
     * @param val1 左值
     * @param val2 右值
     * @return 返回是否相等
     */
    public static boolean eq(BigDecimal val1, BigDecimal val2) {
        if (val1 == null || val2 == null) {
            return Boolean.FALSE;
        }

        return val1.compareTo(val2) == 0;
    }

    public static BigDecimal min(BigDecimal left, BigDecimal right) {
        if(DecimalUtils.gt(left, right)) {
            return right;
        }
        else {
            return left;
        }
    }

    public static BigDecimal max(BigDecimal left, BigDecimal right) {
        if(DecimalUtils.gt(left, right)) {
            return left;
        }
        else {
            return right;
        }
    }

    public static BigDecimal add(BigDecimal in1, BigDecimal in2) {
        BigDecimal bd1 = in1 == null ? BigDecimal.ZERO : in1;
        BigDecimal bd2 = in2 == null ? BigDecimal.ZERO : in2;
        return bd1.add(bd2);
    }

    /**
     * 四舍五入保留2位小数
     */
    public static BigDecimal roundS2(BigDecimal val){
        return val.setScale(2, RoundingMode.HALF_UP);
    }
    /**
     * 四舍五入保留3位小数
     */
    public static BigDecimal roundS3(BigDecimal val){
        return val.setScale(3, RoundingMode.HALF_UP);
    }
    /**
     * 四舍五入保留4位小数
     */
    public static BigDecimal roundS4(BigDecimal val){
        return val.setScale(4, RoundingMode.HALF_UP);
    }
}
