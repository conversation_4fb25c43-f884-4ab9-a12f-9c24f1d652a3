package com.cdz360.base.utils;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

public class DateUtils {

    public final static SimpleDateFormat fmtYyyyMmDdHhMmSs = new SimpleDateFormat(
        "yyyy-MM-dd HH:mm:ss");
    public final static SimpleDateFormat fmtYyyyMmDd = new SimpleDateFormat("yyyy-MM-dd");
    public final static DateTimeFormatter fmtYyyyMmDdLd = DateTimeFormatter.ofPattern(
        "yyyy-MM-dd");
    public final static DateTimeFormatter fmtYyyyMmDdHhMmSsLdt = DateTimeFormatter.ofPattern(
        "yyyy-MM-dd HH:mm:ss");


    public static String toYyyyMmDdHhMmSs(Date date) {
        if (date == null) {
            return StringUtils.EMPTY;
        }
        return fmtYyyyMmDdHhMmSs.format(date);
    }

    public static String toYyyyMmDd(LocalDate date) {
        if (date == null) {
            return StringUtils.EMPTY;
        }
        return fmtYyyyMmDdLd.format(date);
    }

    public static String toYyyyMmDd(LocalDateTime time) {
        if (time == null) {
            return StringUtils.EMPTY;
        }
        return fmtYyyyMmDdLd.format(time);
    }

    public static String toYyyyMmDdHhMmSs(LocalDateTime time) {
        if (time == null) {
            return StringUtils.EMPTY;
        }
        return fmtYyyyMmDdHhMmSsLdt.format(time);
    }

    public static String toStringFormat(Date date, String format) {
        SimpleDateFormat fmt = new SimpleDateFormat(format);
        return fmt.format(date);
    }

    public static String toStringFormat(LocalDate date, String format) {
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern(format);
        return fmt.format(date);
    }

    public static String toStringFormat(LocalDateTime time, String format) {
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern(format);
        return fmt.format(time);
    }

    public static Date fromYyyyMmDd(String strDate) {
        return parseDate(strDate, fmtYyyyMmDd);
    }

    public static Date fromYyyyMmDdHhMmSs(String strDate) {
        return parseDate(strDate, fmtYyyyMmDdHhMmSs);
    }

    public static Date parseDate(String str, String pattern) {
        if (StringUtils.isBlank(pattern)) {
            return null;
        }
        SimpleDateFormat fmt = new SimpleDateFormat(pattern);
        return parseDate(str, fmt);
    }

    public static Date parseDate(String str, SimpleDateFormat fmt) {
        try {
            return fmt.parse(str);
        } catch (Exception e) {
            return null;
        }
    }

    public static Date addDays(Date calIn, int days) {
        long time = calIn.getTime() + 24 * 60 * 60 * 1000 * days;
        return new Date(time);
    }

}
