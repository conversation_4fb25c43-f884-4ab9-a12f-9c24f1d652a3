package com.cdz360.base.utils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonFactory;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import java.io.IOException;
import java.lang.reflect.Type;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class JsonUtils {
//    private static final Logger logger = LoggerFactory.getLogger(JsonUtils.class);

    private static ObjectMapper deserializeMapper;

    private static ObjectMapper serializeMapper;

    private static ObjectMapper deserializeTimeMapper;

    private static ObjectMapper serializeTimeMapper;

    static {
        init();
    }

    public static <T> T fromJson(String json, Class<T> clazz) {
        ObjectMapper mapper = getDeserializeMapper();
        try {
            return mapper.readValue(json, clazz);
        } catch (IOException e) {
            log.error("error = {}, json = {}", e.getMessage(), json, e);
            throw new RuntimeException(" result can not converto to Object");
        }

    }

    public static <T> T fromJson(String json, TypeReference<T> typereference) {
        ObjectMapper mapper = getDeserializeMapper();
        try {
            return mapper.readValue(json, typereference);
        } catch (IOException e) {
            log.error("error = {}, json = {}", e.getMessage(), json, e);
            throw new RuntimeException(" result can not converto to Object");
        }
    }

    public static <T> T fromJsonTime(String json, Class<T> clazz) {
        try {
            return deserializeTimeMapper.readValue(json, clazz);
        } catch (IOException e) {
            log.error("error = {}, json = {}", e.getMessage(), json, e);
            throw new RuntimeException("result can not convert to Object");
        }
    }

    public static <T> T fromJsonTime(String json, TypeReference<T> typeReference) {
        try {
            return deserializeTimeMapper.readValue(json, typeReference);
        } catch (IOException e) {
            log.error("error = {}, json = {}", e.getMessage(), json, e);
            throw new RuntimeException("result can not convert to Object");
        }
    }

    public static <T> List<T> toJsonList(String json) {
        ObjectMapper mapper = getDeserializeMapper();
        try {
            return mapper.readValue(json, new TypeReference<ArrayList<T>>() {
                @Override
                public Type getType() {
                    return super.getType();
                }
            });
        } catch (IOException e) {
            log.error("error = {}, json = {}", e.getMessage(), json, e);
            throw new RuntimeException(" result can not converto to Object");
        }
    }

    public static <T> List<T> toJsonList(ArrayNode arrayNode) {
        ObjectMapper mapper = getDeserializeMapper();
        try {
            return mapper.treeToValue(arrayNode, new TypeReference<ArrayList<T>>() {
                @Override
                public Type getType() {
                    return super.getType();
                }
            });
        } catch (IOException e) {
            log.error("error = {}, arrayNode = {}", e.getMessage(), arrayNode, e);
            throw new RuntimeException(" result can not converto to List Object");
        }
    }

    public static <T> List<T> toJsonList(JsonNode rootNode, String arrayNodeName) {
        if (!rootNode.has(arrayNodeName)) {
            log.debug("json数组节点 {} 不存在", arrayNodeName);
            return null;
        }
        ArrayNode arrayNode = (ArrayNode) rootNode.get(arrayNodeName);
        return toJsonList(arrayNode);
    }


    public static JsonNode fromJson(String json) {
        ObjectMapper mapper = getDeserializeMapper();
        JsonFactory factory = mapper.getFactory();
        try {
            JsonParser jp = factory.createParser(json);
            JsonNode jsonNode = mapper.readTree(jp);
            return jsonNode;
        } catch (IOException e) {
            log.error("error = {}, json = {}", e.getMessage(), json, e);
            throw new RuntimeException(" result can not converto to Object");
        }

    }

    public static <T> T fromJson(JsonNode json, Class<T> clazz) {
        ObjectMapper mapper = getDeserializeMapper();
        try {
            return mapper.treeToValue(json, clazz);
        } catch (IOException e) {
            log.error("error = {}, json = {}", e.getMessage(), json, e);
            throw new RuntimeException(" result can not converto to Object");
        }
    }


    public static <T> T fromJson(JsonNode json, TypeReference<T> typeReference) {
        ObjectMapper mapper = getDeserializeMapper();
        try {
            return mapper.convertValue(json, typeReference);
        } catch (Exception e) {
            log.error("error = {}, json = {}", e.getMessage(), json, e);
            throw new RuntimeException(" result can not converto to Object");
        }
    }

    public static <T> T fromJsonTime(JsonNode json, Class<T> clazz) {
        try {
            return deserializeTimeMapper.treeToValue(json, clazz);
        } catch (IOException e) {
            log.error("error = {}, json = {}", e.getMessage(), json, e);
            throw new RuntimeException("result can not converto to Object");
        }
    }


    public static <T> T fromJsonTime(JsonNode json, TypeReference<T> typeReference) {
        try {
            return deserializeTimeMapper.convertValue(json, typeReference);
        } catch (Exception e) {
            log.error("error = {}, json = {}", e.getMessage(), json, e);
            throw new RuntimeException("result can not converto to Object");
        }
    }


    private static ObjectMapper getSerializeMapper() {
//        if (serializeMapper == null) {
//            serializeMapper = new ObjectMapper();
//
//            serializeMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
//            serializeMapper.setSerializationInclusion(JsonInclude.Include.NON_EMPTY);
//        }
        return serializeMapper;
    }

    private static ObjectMapper getDeserializeMapper() {
//        if (deserializeMapper == null) {
//            deserializeMapper = new ObjectMapper();
//            deserializeMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
//            deserializeMapper.configure(DeserializationFeature.USE_BIG_DECIMAL_FOR_FLOATS, true);
//
//        }
        return deserializeMapper;
    }

    private static void init() {
        serializeMapper = new ObjectMapper();
        serializeMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        serializeMapper.setSerializationInclusion(JsonInclude.Include.NON_EMPTY);
//        serializeMapper.registerModule(new JavaTimeModule());
//        serializeMapper.setDateFormat(new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss",
//            Locale.CHINA));   // 默认的日期时间格式

        deserializeMapper = new ObjectMapper();
        deserializeMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        deserializeMapper.configure(DeserializationFeature.USE_BIG_DECIMAL_FOR_FLOATS, true);

        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss",
            Locale.getDefault());
        DateTimeFormatter zonedDf1 = DateTimeFormatter.ofPattern(
            "yyyy-MM-dd'T'HH:mm:ss.SSSXXX"); // 带毫秒
        DateTimeFormatter zonedDf2 = DateTimeFormatter.ofPattern(
            "yyyy-MM-dd'T'HH:mm:ssXXX"); // 不带毫秒

        serializeTimeMapper = new ObjectMapper();
        serializeTimeMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        serializeTimeMapper.setSerializationInclusion(JsonInclude.Include.NON_EMPTY);
        JavaTimeModule serializeJTM = new JavaTimeModule();
        serializeJTM.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(df));
        serializeJTM.addSerializer(LocalDate.class, new JsonSerializer<LocalDate>() {
            @Override
            public void serialize(LocalDate value, JsonGenerator gen,
                SerializerProvider serializers) throws IOException {
                gen.writeString(value.toString());
            }
        });
        serializeJTM.addSerializer(ZonedDateTime.class, new JsonSerializer<ZonedDateTime>() {
            @Override
            public void serialize(ZonedDateTime value, JsonGenerator gen,
                SerializerProvider serializers) throws IOException {
                gen.writeString(value.format(zonedDf1));
            }
        });
        serializeTimeMapper.registerModule(serializeJTM);

        deserializeTimeMapper = new ObjectMapper();
        deserializeTimeMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        deserializeTimeMapper.configure(DeserializationFeature.USE_BIG_DECIMAL_FOR_FLOATS, true);
        JavaTimeModule deserializeJTM = new JavaTimeModule();
        // 注册时间类型的反序列化器
        deserializeJTM.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(df));
        deserializeJTM.addDeserializer(LocalDate.class,
            new LocalDateDeserializer(DateTimeFormatter.ISO_LOCAL_DATE));
        deserializeJTM.addDeserializer(ZonedDateTime.class, new JsonDeserializer<ZonedDateTime>() {
            @Override
            public ZonedDateTime deserialize(JsonParser p, DeserializationContext ctxt)
                throws IOException {
                String text = p.getText();
                try {
                    return ZonedDateTime.parse(text, zonedDf1); // 先尝试带毫秒的
                } catch (Exception e) {
                    return ZonedDateTime.parse(text, zonedDf2); // 再尝试不带毫秒的
                }
            }
        });
        deserializeTimeMapper.registerModule(deserializeJTM);
    }

    public static String toJsonString(Object obj) {
        try {
            return getSerializeMapper().writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            log.error(e.getMessage(), e);
        }
        return "";
    }

    public static String toJsonString(Object obj, String timeFormat,
        Locale locale) {
        try {

            ObjectMapper mapper = new ObjectMapper();
            mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
            mapper.setSerializationInclusion(JsonInclude.Include.NON_EMPTY);

            DateTimeFormatter df = DateTimeFormatter.ofPattern(timeFormat, locale);
            LocalDateTimeSerializer serializer = new LocalDateTimeSerializer(df);
            JavaTimeModule jtm = new JavaTimeModule();
            jtm.addSerializer(LocalDateTime.class, serializer);
            mapper.registerModule(jtm);

            return mapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            log.error(e.getMessage(), e);
        }
        return "";
    }

    public static String toJsonTimeString(Object obj) {
        try {
            return serializeTimeMapper.writeValueAsString(obj);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return "";
    }

}
