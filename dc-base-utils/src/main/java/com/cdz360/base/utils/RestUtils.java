package com.cdz360.base.utils;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;

import java.util.List;

public class RestUtils {

    private static final BaseResponse success = new BaseResponse();

    public static BaseResponse success() {
        return success;
    }

    public static <T> ObjectResponse<T> buildObjectResponse(T data) {
        return new ObjectResponse<>(data);
    }

    public static <T> ListResponse<T> buildListResponse(List<T> list) {
        return new ListResponse<>(list);
    }

    public static <T> ListResponse<T> buildListResponse(List<T> list, long total) {
        return new ListResponse<>(list, total);
    }

    public static BaseResponse fail(int status, String error) {
        return new BaseResponse(status, error);
    }

    public static BaseResponse serverBusy() {
        return new BaseResponse(DcConstants.KEY_RES_CODE_SERVER_ERROR, "系统繁忙,请稍后重试!");
    }

    public static ObjectResponse serverBusy4ObjectResponse() {
        return new ObjectResponse(DcConstants.KEY_RES_CODE_SERVER_ERROR, "系统繁忙,请稍后重试!");
    }

    public static ListResponse serverBusy4ListResponse() {
        return new ListResponse(DcConstants.KEY_RES_CODE_SERVER_ERROR, "系统繁忙,请稍后重试!");
    }


}
