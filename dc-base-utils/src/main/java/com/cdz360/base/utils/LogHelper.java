package com.cdz360.base.utils;

import com.cdz360.base.model.base.exception.DcException;
import org.slf4j.Logger;
import org.slf4j.event.Level;

public class LogHelper {
    public static void logException(Logger log, DcException ex) {
        if (Level.ERROR == ex.getLogLevel()) {
            log.error(ex.getMessage(), ex);
        } else if (Level.WARN == ex.getLogLevel()) {
            log.warn(ex.getMessage(), ex);
        } else if (Level.INFO == ex.getLogLevel()) {
            log.info(ex.getMessage());
        } else if (Level.DEBUG == ex.getLogLevel()) {
            log.debug(ex.getMessage());
        } else if (Level.TRACE == ex.getLogLevel()) {
            log.trace(ex.getMessage());
        } else {
            log.error(ex.getMessage(), ex);
        }
    }

    public static void logException(Logger log, String msg, DcException ex) {
        if (Level.ERROR == ex.getLogLevel()) {
            log.error("{} -- {}", msg, ex.getMessage(), ex);
        } else if (Level.WARN == ex.getLogLevel()) {
            log.warn("{} -- {}", msg, ex.getMessage(), ex);
        } else if (Level.INFO == ex.getLogLevel()) {
            log.info("{} -- {}", msg, ex.getMessage());
        } else if (Level.DEBUG == ex.getLogLevel()) {
            log.debug("{} -- {}", msg, ex.getMessage());
        } else if (Level.TRACE == ex.getLogLevel()) {
            log.trace("{} -- {}", msg, ex.getMessage());
        } else {
            log.error("{} -- {}", msg, ex.getMessage(), ex);
        }
    }

    /**
     * 打印延迟
     *
     * @param log       logger
     * @param clazzName 类名
     * @param funcName  函数名
     * @param tag       标签
     * @param startTime 时间
     */
    public static void logLatency(Logger log, String clazzName, String funcName, String tag, long startTime) {
        long curTime = System.nanoTime();
        if (curTime - startTime > 5000L * 1000000) {
            // 超过5秒打印error级别日志
            log.error("延迟过大...latency = {}, class = {}, func = {}, tag = {}",
                    (curTime - startTime) / 1000000, clazzName, funcName, tag);
        } else if (curTime - startTime > 1000L * 1000000) {
            log.warn("延迟过大...latency = {}, class = {}, func = {}, tag = {}",
                    (curTime - startTime) / 1000000, clazzName, funcName, tag);
        }
    }
}
