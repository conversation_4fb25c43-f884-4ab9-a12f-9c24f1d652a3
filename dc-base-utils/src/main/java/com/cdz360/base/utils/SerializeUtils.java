package com.cdz360.base.utils;


import com.cdz360.base.model.base.annotation.Cache;
import com.cdz360.base.model.base.exception.DcServiceException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

public class SerializeUtils {
    private static final Logger logger = LoggerFactory.getLogger(SerializeUtils.class);


    public static byte[] serialize(Object object) {
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream();
             ObjectOutputStream oos = new ObjectOutputStream(baos)) {
            // 序列化
            oos.writeObject(object);
            byte[] bytes = baos.toByteArray();
            return bytes;
        } catch (Exception e) {
            throw new DcServiceException(e.getMessage(), e);
        }
    }

    public static Object unserialize(byte[] bytes) {
        try (InputStream bais = new ByteArrayInputStream(bytes)) {
            // 反序列化
            ObjectInputStream ois = new ObjectInputStream(bais);
            return ois.readObject();
        } catch (Exception e) {
            throw new DcServiceException(e.getMessage(), e);
        }
    }

    private static Map<String, Field> findCacheFields(Field[] fields) {
        Map<String, Field> names = new HashMap<>();
        for (Field f : fields) {
            String name = "";

            boolean needCache = false;
            for (Cache cache : f.getDeclaredAnnotationsByType(Cache.class)) {
                needCache = true;
                if (StringUtils.isNotBlank(cache.name())) {
                    name = cache.name();
                }
            }
            if (needCache) {
                if (StringUtils.isBlank(name)) {
                    name = f.getName();
                }
                names.put(name, f);
            }
        }
        return names;
    }

    public static Map<String, String> objectToMap(Object obj) {
        Map<String, String> m = new HashMap<>();


        for (Field f : obj.getClass().getDeclaredFields()) {
            addField(obj, f, m);
        }

        Class<?> superClass = obj.getClass().getSuperclass();
        while (superClass != null) {
            for (Field f : superClass.getDeclaredFields()) {
                addField(obj, f, m);

            }
            superClass = superClass.getSuperclass();
        }
        return m;
    }

    private static void addField(Object obj, Field f, Map<String, String> m) {
        String name = "";

        boolean needCache = false;
        for (Cache cache : f.getDeclaredAnnotationsByType(Cache.class)) {
            needCache = true;
            if (StringUtils.isNotBlank(cache.name())) {
                name = cache.name();
            }
        }


        if (needCache) {
            if (StringUtils.isBlank(name)) {
                name = f.getName();
            }
            Object v = null;
            try {
                String methodName = "get" + f.getName().substring(0, 1).toUpperCase() + f.getName().substring(1);

                v = obj.getClass().getMethod(methodName).invoke(obj);
            } catch (Exception e) {
                logger.trace(e.getMessage(), e);
            }
            if (v == null) {
                try {
                    String methodName = "is" + f.getName().substring(0, 1).toUpperCase() + f.getName().substring(1);

                    v = obj.getClass().getMethod(methodName).invoke(obj);
                } catch (Exception e) {
                    logger.trace(e.getMessage(), e);
                }
            }
            if (v != null) {
                if (v instanceof Date) {
                    m.put(name, String.valueOf(((Date) v).getTime()));
                } else {
                    m.put(name, String.valueOf(v));
                }
            }
        }
    }

    public static <T> T mapToObject(Class<T> clazz, Map<String, String> map) {
        if (map.isEmpty()) {
            return null;
        }

        T obj = null;
        try {
            obj = clazz.getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return obj;
        }

        // redis-field-name --- object-parameter-name 间的映射
        Map<String, Field> names = SerializeUtils.findCacheFields(obj.getClass().getDeclaredFields());
        Class<?> superClass = obj.getClass().getSuperclass();
        while (superClass != null) {
            names.putAll(SerializeUtils.findCacheFields(superClass.getDeclaredFields()));
            superClass = superClass.getSuperclass();
        }

        // 列出所有可缓存的变量名


        Iterator<String> iter = map.keySet().iterator();
        while (iter.hasNext()) {
            String k = iter.next();
            String v = map.get(k);
            if (names.containsKey(k)) {
                Field field = names.get(k);
                try {
                    String methodName = "set" + field.getName().substring(0, 1).toUpperCase()
                            + field.getName().substring(1);

                    Method method = obj.getClass().getMethod(methodName, field.getType());
                    if (method != null) {
                        Object arg = null;
                        if (field.getType().equals(int.class) || field.getType().equals(Integer.class)) {
                            arg = Integer.parseInt(v);
                        } else if (field.getType().equals(long.class) || field.getType().equals(Long.class)) {
                            arg = Long.parseLong(v);
                        } else if (field.getType().equals(double.class) || field.getType().equals(Double.class)) {
                            arg = Double.parseDouble(v);
                        } else if (field.getType().equals(boolean.class) || field.getType().equals(Boolean.class)) {
                            arg = Boolean.parseBoolean(v);
                        } else if (field.getType().equals(String.class)) {
                            arg = v;
                        } else if (field.getType().isEnum()) {
                            Class clazzX = Class.forName(field.getType().getName());
                            arg = Enum.valueOf(clazzX, v);
                        } else if (field.getType().equals(Date.class)) {
                            Date d = new Date();
                            d.setTime(Long.parseLong(v));
                            arg = d;
                        } else if (field.getType().equals(BigDecimal.class)) {
                            arg = new BigDecimal(v);
                        }
                        method.invoke(obj, arg);
                    }
                } catch (Exception e) {
                    logger.info("field.name = {}, msg = {}", field.getName(), e.getMessage(), e);
                }
            }
        }

        return obj;
    }
}
