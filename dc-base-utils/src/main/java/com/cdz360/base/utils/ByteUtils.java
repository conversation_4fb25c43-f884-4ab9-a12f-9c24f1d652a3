package com.cdz360.base.utils;

import static java.lang.Float.floatToRawIntBits;

public class ByteUtils {

    public static final byte[] ZERO_2_BYTES = {(byte) 0x00, (byte) 0x00};

    public static String bytesToHex(byte[] srcBytes) {
        if (srcBytes == null) {
            return null;
        }
        char[] hexArray = "0123456789ABCDEF".toCharArray();
        char[] hexChars = new char[srcBytes.length * 2];
        for (int j = 0; j < srcBytes.length; j++) {
            int v = srcBytes[j] & 0xFF;
            hexChars[j * 2] = hexArray[v >>> 4];
            hexChars[j * 2 + 1] = hexArray[v & 0x0F];
        }
        return new String(hexChars);
    }

    /**
     * 16进制表示的字符串转换为字节数组
     *
     * @param s 16进制表示的字符串
     * @return byte[] 字节数组
     */
    public static byte[] hexToBytes(String s) {
        int len = s.length();
        byte[] b = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            // 两位一组，表示一个字节,把这样表示的16进制字符串，还原成一个字节
            b[i / 2] = (byte) ((Character.digit(s.charAt(i), 16) << 4) + Character
                .digit(s.charAt(i + 1), 16));
        }
        return b;
    }

    public static byte[] longToByteLE(long val) {
        return new byte[]{
            (byte) (val), (byte) (val >>> 8), (byte) (val >>> 16), (byte) (val >>> 24)
        };
    }

    public static byte[] longToByteBE(long val) {
        return new byte[]{
            (byte) (val >>> 24), (byte) (val >>> 16), (byte) (val >>> 8), (byte) (val)
        };
    }

    public static byte[] floatToByteBE(float val) {
        int b = floatToRawIntBits(val);
        return new byte[]{
            (byte) (b >> 24), (byte) (b >> 16), (byte) (b >> 8), (byte) b
        };
    }

    public static byte[] intToByte2BE(int val) {
        return new byte[]{
            (byte) (val >>> 8), (byte) (val)
        };
    }

    public static byte[] intToByte2LE(int val) {
        return new byte[]{
            (byte) (val), (byte) (val >>> 8)
        };
    }

    public static byte[] intToByteBE(int val) {
        byte[] b = new byte[4];
        b[0] = (byte) ((val >> 24) & 0xff);
        b[1] = (byte) ((val >> 16) & 0xff);
        b[2] = (byte) ((val >> 8) & 0xff);
        b[3] = (byte) (val & 0xff);
        return b;
    }

    public static byte[] intToByteLE(int val) {
        byte[] b = new byte[4];
        b[3] = (byte) ((val >> 24) & 0xff);
        b[2] = (byte) ((val >> 16) & 0xff);
        b[1] = (byte) ((val >> 8) & 0xff);
        b[0] = (byte) (val & 0xff);
        return b;
    }


    /**
     * byte[]转换为short（b为大端表示）
     *
     * @param b
     * @return short
     */
    public static short bytes2ShortBE(byte[] b, int offset) {
        short s = 0;
        short s0 = (short) (b[offset + 0] & 0xff);// 最低位
        short s1 = (short) (b[offset + 1] & 0xff);
        s0 <<= 8;
        s = (short) (s0 | s1);
        return s;
    }

    /**
     * byte[]转换为short（b为小端表示）
     *
     * @param b
     * @return short
     */
    public static short bytes2ShortLE(byte[] b, int offset) {
        short s = 0;
        short s0 = (short) (b[offset + 0] & 0xff);// 最低位
        short s1 = (short) (b[offset + 1] & 0xff);
        s1 <<= 8;
        s = (short) (s0 | s1);
        return s;
    }

    /**
     * LittleEnded
     */
    public static int bytes2IntLE(byte[] data, int offset, int length) {
        int result = 0;
        int start = offset + length - 1;
        int end = offset - 1;
        for (int i = start; i > end; i--) {
            result <<= 8;
            result |= (data[i] & 0xFF);
        }
        return result;
    }

    /**
     * BigEnded
     */
    public static int bytes2IntBE(byte[] data, int offset, int length) {
        int result = 0;
        int start = offset;
        int end = offset + length;
        for (int i = start; i < end; i++) {
            result <<= 8;
            result |= (data[i] & 0xFF);
        }
        return result;
    }

    /**
     * byte[] 转 long, 小端编码
     *
     * @param data   二进制数组
     * @param offset 开始字节下标
     * @param length 长度
     * @return 小端编码的 long 型数据
     */
    public static long bytes2LongLE(byte[] data, int offset, int length) {
        long result = 0L;
        int start = offset + length - 1;
        int end = offset - 1;
        for (int i = start; i > end; i--) {
            result <<= 8;
            result |= (data[i] & 0xFF);
        }
        return result;
    }

    public static long bytes2LongBE(byte[] data, int offset, int length) {
        long result = 0L;
        int start = offset;
        int end = offset + length;
        for (int i = start; i < end; i++) {
            result <<= 8;
            result |= (data[i] & 0xFF);
        }
        return result;
    }

    public static long long2LongBE(long data) {
        long result = data & 0xFF;
        int cnt = 1;
        while (cnt < 4) {
            result <<= 8;
            result |= (data >> (cnt * 8)) & 0xFF;
            cnt++;
        }
        return result;
    }


    /**
     * modbus crc16
     */
    public static int crc16(byte[] buf) {
        return crc16(buf, buf.length);
    }

    /**
     * modbus crc16
     */
    public static int crc16(byte[] buf, int len) {
        int i, j;
        int c, crc = 0xFFFF;
        for (i = 0; i < len; i++) {
            c = buf[i] & 0x00FF;
            crc ^= c;
            for (j = 0; j < 8; j++) {
                if ((crc & 0x0001) != 0) {
                    crc >>= 1;
                    crc ^= 0xA001;
                } else {
                    crc >>= 1;
                }
            }
        }
        return (crc);
    }

    public static String byteBuf2Ascii(byte[] buf, int start, int len) {
        StringBuilder sb = new StringBuilder();
        for (int i = start; i < (start + len); i++) {
            byte b = buf[i];
            if (buf[i] != (byte) 0x00) {
                sb.append((char) b);
            }
        }
        return sb.toString();
    }

    public static int byte2BcdInt(byte data) {
        int a = ((0xF0 & data) >>> 4) * 10;
        int b = 0x0F & data;
        return a + b;
    }

    // 小端 byte[]
    public static int byte2BcdIntSE(byte[] data, int offset, int length) {
        StringBuilder sb = new StringBuilder();
        int start = offset + length - 1;
        int end = offset - 1;
        for (int i = start; i > end; i--) {
            int temp = byte2BcdInt(data[i]);
            sb.append(temp >= 10 ? temp : "0" + temp);
        }
        return Integer.parseInt(sb.toString());
    }

    /**
     * 带符号位版本
     *
     * @param data
     * @return
     */
    public static int byte2BcdIntSkipSign(byte data) {
        int a = ((0x70 & data) >>> 4) * 10;
        int b = 0x0F & data;
        return a + b;
    }

    public static byte int2BcdByte(int val) {
        return (byte) (val / 10 * 16 + val % 10);
    }

    /**
     * 反转字节顺序
     */
    public static byte[] reverseBytes(byte[] bufIn) {
        return reverseBytes(bufIn, 0, bufIn.length);
    }


    /**
     * 反转字节顺序
     */
    public static byte[] reverseBytes(byte[] bufIn, int start, int size) {
        if (size < 1) {
            return null;
        } else if (start < 0) {
            return null;
        }
        byte[] result = new byte[size];
        int idx = start;
        for (int i = 0; i < size; i++) {
            result[i] = bufIn[start + size - i - 1];
        }
        return result;
    }
}
