package com.cdz360.ess.sync.publisher;

import com.cdz360.base.model.es.vo.hi.InverterRtInfo;
import com.cdz360.data.sync.service.EssEventPublisher;
import com.cdz360.ess.sync.EssSyncTestBase;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
class EssEventPublisherImplTest extends EssSyncTestBase {

    @Autowired
    private EssEventPublisher essEventPublisher;

    @Test
    void publishEssStatus() {
        String serialNo = "TEST_SERIAL_NO";
        InverterRtInfo essStatus = new InverterRtInfo();
        essEventPublisher.publishEssStatus(serialNo, essStatus);
    }

    @Test
    void publishEssData() {
        essEventPublisher.publishEssData("", null);
    }

    @Test
    void publishEssSubDeviceData() {
        essEventPublisher.publishEssSubDeviceData("", null, null);
    }
}