server:
  address: 0.0.0.0
  port: 8080
  use-forward-headers: true
  compression.enabled: true

eureka:
  instance:
    hostname: localhost
  client:
    registerWithEureka: false
    fetchRegistry: true
    healthcheck:
      enabled: true
    serviceUrl:
      defaultZone: http://aaa:<EMAIL>/eureka/


management:
  context-path: /admin
  security:
    enabled: false



spring:
  rabbitmq:
    host: **************
    port: 5672
    username: topower
    password: topower123
    virtual-host: topower
    virtualHost: topower

resourceOwnerId: 1620936650641344

amqp:
  rabbitmq:
    host: **************
    port: 5672
    username: topower
    password: topower123
    virtual-host: topower
    virtualHost: topower


logging:
  level:
    org.springframework: 'INFO'
    com.cdz360.data: 'DEBUG'
    org.springframework.amqp: 'DEBUG'
    
