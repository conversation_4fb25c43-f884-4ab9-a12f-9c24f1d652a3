package com.cdz360.ess.sync;

import com.cdz360.base.model.base.type.IotEvent;
import com.cdz360.base.model.es.dto.EssAlarmNotify;
import com.cdz360.base.model.es.dto.EssAlarms;
import com.cdz360.base.model.es.dto.EssFactoryDto;
import com.cdz360.base.model.es.vo.EmuRtData;
import com.cdz360.base.model.es.vo.hi.InverterRtData;
import com.cdz360.base.model.es.vo.hi.InverterRtInfo;
import com.cdz360.base.model.iot.vo.EssVo;
import com.cdz360.data.sync.constant.DcMqConstants;
import com.cdz360.data.sync.event.EssAlarmEvent;
import com.cdz360.data.sync.event.EssAlarmEvent2;
import com.cdz360.data.sync.event.EssInfoEvent;
import com.cdz360.data.sync.event.EssRtDataEvent;
import com.cdz360.data.sync.event.MqEventSubType;
import com.cdz360.data.sync.event.ess.EssCollectDataEvent;
import com.cdz360.data.sync.event.ess.EssComponentDataEvent;
import com.cdz360.data.sync.event.ess.EssFactoryInfoEvent;
import com.cdz360.data.sync.event.ess.EssStatusAlarmEvent;
import com.cdz360.data.sync.service.EssEventPublisher;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class EssEventPublisherImpl implements EssEventPublisher {

    @Autowired
    private AmqpTemplate rabbitTemplate;

//    @Override
//    public void publishEssAlarm(EssAlarms alarms) {
//        EssAlarmEvent2 event = new EssAlarmEvent2(IotEvent.STATE_CHANGE, alarms);
//        this.publish(event.toString());
//    }
//
//    @Override
//    public void publishEssRtData(EmuRtData rtData) {
//        EssRtDataEvent event = new EssRtDataEvent(IotEvent.RT_DATA_CHANGE, rtData);
//        this.publish(event.toString());
//    }
//
//    @Override
//    public void publishEssInfo(IotEvent eventType, EssVo ess) {
//        EssInfoEvent event = new EssInfoEvent(eventType, ess);
////        event.setData(ess);
////        String msg = event.toString();
////        log.info(" event = {}", msg);
////        rabbitTemplate.convertAndSend(DcMqConstants.MQ_EXCHANGE_NAME_IOT,
////            DcMqConstants.MQ_ROUTING_KEY_IOT, msg);
//        this.publish(event.toString());
//    }

    @Override
    public void publishEssFactoryInfo(String serialNo, EssFactoryDto data) {
        EssFactoryInfoEvent event = new EssFactoryInfoEvent(serialNo);
        event.setData(data);
        this.publish(event.toString());
    }

    @Override
    public void publishEssStatus(String serialNo, InverterRtInfo essStatus) {
        EssStatusAlarmEvent event = new EssStatusAlarmEvent(serialNo);
        event.setData(essStatus);
        this.publish(event.toString());
    }

    @Override
    public void publishEssData(String serialNo, InverterRtData essData) {
        EssCollectDataEvent event = new EssCollectDataEvent(serialNo);
        event.setData(essData);
        this.publish(event.toString());
    }

    @Override
    public void publishEssSubDeviceData(String serialNo, MqEventSubType type, String deviceData) {
        EssComponentDataEvent event = new EssComponentDataEvent(serialNo);
        event.setData(deviceData);
        event.setMqSubType(type);
        this.publish(event.toString());
    }

    private void publish(String msg) {
        log.info("ess push event = {}", msg);
        rabbitTemplate.convertAndSend(DcMqConstants.MQ_EXCHANGE_NAME_ESS,
            DcMqConstants.MQ_ROUTING_KEY_ESS, msg);
    }
}
