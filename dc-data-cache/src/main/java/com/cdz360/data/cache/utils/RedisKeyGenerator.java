package com.cdz360.data.cache.utils;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.utils.DateUtils;
import com.cdz360.base.utils.PlugNoUtils;
import com.cdz360.data.cache.CacheConstants;
import java.text.MessageFormat;
import java.time.LocalDate;

public class RedisKeyGenerator {


    public static String genTransformerRedisKey(Long tfmId) {
        return CacheConstants.REDIS_KEY_TRANSFORMER_INFO + tfmId;
    }

    public static String genEvseRedisKey(String evseNo) {
        return CacheConstants.REDIS_KEY_EVSE_INFO + evseNo;
    }


    public static String genPlugRedisKey(String plugNo) {
        return CacheConstants.REDIS_KEY_PLUG_INFO + plugNo;
    }

    public static String genPlugRedisKey(String evseNo, int idx) {
        return CacheConstants.REDIS_KEY_PLUG_INFO + PlugNoUtils.formatPlugNo(evseNo, idx);
    }

    public static String genEmuRtInfoKey(String emuDno) {
        return CacheConstants.REDIS_KEY_EMU_RT_INFO + emuDno;
    }




    public static String genEmuRtDataKey(String emuDno) {
        return CacheConstants.REDIS_KEY_EMU_RT_DATA + emuDno;
    }


    public static String genPcsRtInfoKey(String pcsDno) {
        return CacheConstants.REDIS_KEY_PCS_RT_INFO + pcsDno;
    }


    public static String genPcsRtDataKey(String pcsDno) {
        return CacheConstants.REDIS_KEY_PCS_RT_DATA + pcsDno;
    }

    public static String genBmsRtInfoKey(String bmsDno) {
        return CacheConstants.REDIS_KEY_BMS_RT_INFO + bmsDno;
    }


    public static String genBmsRtDataKey(String bmsDno) {
        return CacheConstants.REDIS_KEY_BMS_RT_DATA + bmsDno;
    }

    public static String genLiquidRtInfoKey(String liquidDno) {
        return CacheConstants.REDIS_KEY_LIQUID_RT_INFO + liquidDno;
    }

    public static String genLiquidRtDataKey(String liquidDno) {
        return CacheConstants.REDIS_KEY_LIQUID_RT_DATA + liquidDno;
    }

    public static String genDehRtInfoKey(String dehDno) {
        return CacheConstants.REDIS_KEY_DEH_RT_INFO + dehDno;
    }

    public static String genDehRtDataKey(String dehDno) {
        return CacheConstants.REDIS_KEY_DEH_RT_DATA + dehDno;
    }

    public static String genFfsRtInfoKey(String ffsDno) {
        return CacheConstants.REDIS_KEY_FFS_RT_INFO + ffsDno;
    }

    public static String genFfsRtDataKey(String ffsDno) {
        return CacheConstants.REDIS_KEY_FFS_RT_DATA + ffsDno;
    }

    public static String genUpsRtInfoKey(String upsDno) {
        return CacheConstants.REDIS_KEY_UPS_RT_INFO + upsDno;
    }

    public static String genUpsRtDataKey(String upsDno) {
        return CacheConstants.REDIS_KEY_UPS_RT_DATA + upsDno;
    }

    public static String genMeterRtInfoKey(String meterDno) {
        return CacheConstants.REDIS_KEY_METER_RT_INFO + meterDno;
    }

    public static String genMeterRtDataKey(String meterDno) {
        return CacheConstants.REDIS_KEY_METER_RT_DATA + meterDno;
    }

//    public static String genEmuRtDiDataKey(String emuDno) {
//        return CacheConstants.REDIS_KEY_DI_RT_INFO + emuDno;
//    }
    /**
     * BMS 日内曲线数据
     */
    public static String genBmsInDaySamplingDataKey(String bmsDno, LocalDate date) {
        return CacheConstants.REDIS_KEY_BMS_RT_DATA + bmsDno + "_" + DateUtils.toYyyyMmDd(date)
            .replace("-", "");
    }

    /**
     * PCS 日内曲线数据
     */
    public static String genPcsInDaySamplingDataKey(String pcsDno, LocalDate date) {
        return CacheConstants.REDIS_KEY_PCS_RT_DATA + pcsDno + "_" + DateUtils.toYyyyMmDd(date)
            .replace("-", "");
    }

    public static String genMeterInDaySamplingDataKey(String meterDno, LocalDate date) {
        return CacheConstants.REDIS_KEY_METER_RT_DATA + meterDno + "_" + DateUtils.toYyyyMmDd(date)
            .replace("-", "");
    }

    public static String genIotGwCmdsKey(String gwno) {
        return CacheConstants.REDIS_KEY_IOT_GW_CMD_SEQ + gwno;
    }

    public static String genIotGwCmdKye(String gwno, String seq) {
        return CacheConstants.REDIS_KEY_IOT_GW_CMD + gwno + ":" + seq;
    }


    public static String genSiteEvseRedisKey(String siteId) {
        return CacheConstants.REDIS_KEY_SITE_EVSE_REF + siteId;
    }

    public static String genSitePlugRedisKey(String siteId) {
        return CacheConstants.REDIS_KEY_SITE_PLUG_REF + siteId;
    }

//    public static String genPlugNo(String evseNo, int idx) {
//        if (idx < 10) {
//            return evseNo + "0" + idx;
//        } else {
//            return evseNo + idx;
//        }
//    }


    public static String genChargerOrderInfoKey(String orderNo) {
        return CacheConstants.REDIS_KEY_CHARGE_ORDER_INFO + orderNo;
    }

    public static String genChargerOrderStopCodeKey(String orderNo) {
        return CacheConstants.REDIS_KEY_CHARGE_ORDER_STOP_CODE + orderNo;
    }

    public static String genExpectLimitSocInfoKey(String orderNo) {
        return CacheConstants.REDIS_KEY_EXPECT_LIMIT_SOC + orderNo;
    }

    public static String genLimitSocEventKey(String orderNo) {
        return CacheConstants.REDIS_KEY_LIMIT_SOC_EVENT + orderNo;
    }

    public static String genChargerOrderDetailKey(String orderNo) {
        return CacheConstants.REDIS_KEY_CHARGE_ORDER_DETAIL + orderNo;
    }

    public static String genAppCfgKey() {
        return CacheConstants.REDIS_KEY_APP_CFG;
    }


    public static String genCustomerKey(AppClientType appType, long cusId) {
        return MessageFormat.format(CacheConstants.REDIS_KEY_CUSTOMER, appType.getCode(),
            String.valueOf(cusId));
    }

    /**
     * 账号信息缓存
     */
    public static String genUserInfoKey(long topCommId, long cusId) {
        return MessageFormat.format(CacheConstants.REDIS_KEY_USER_INFO,
            String.valueOf(topCommId), String.valueOf(cusId));
    }

    /**
     * 账号登录的 salt 缓存 key
     */
    public static String genLoginSaltKey(long topCommId, String username) {
        return MessageFormat.format(CacheConstants.REDIS_KEY_LOGIN_SALT,
            String.valueOf(topCommId), username);
    }


    public static String genPlugSearchKey(String plugNoSuffix) {
        return CacheConstants.REDIS_KEY_PLUG_INFO + "*:" + plugNoSuffix;
    }
}
