package com.cdz360.data.cache.model;

import com.cdz360.base.model.base.type.IotGwCmdType2;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class IotGwCmdCacheVo<T> {
    @JsonProperty(value = "n")
    private String gwno;
    @JsonProperty(value = "v")
    private Integer ver;

    @JsonProperty(value = "s")
    private String seq;

    /**
     * @deprecated 使用dno替换
     */
    @Deprecated
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String evseNo;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String dno;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer plugId;

    @JsonProperty(value = "c")
    private IotGwCmdType2 cmd;
    private T data;

    // TODO： 兼容旧版网关代码，2021.11版本后移除
    @JsonProperty(value = "seq")
    public String getSeqX() {
        return this.seq;
    }


}
