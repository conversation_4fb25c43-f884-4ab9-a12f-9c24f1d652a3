package com.cdz360.data.cache;


public interface CacheConstants {

    String REDIS_KEY_TRANSFORMER_INFO = "iot:tfm:";
    String REDIS_KEY_EVSE_INFO = "iot:evse:";
    String REDIS_KEY_PLUG_INFO = "iot:plug:";

    /**
     * EMU实时设备信息 (不含 PCS、BMS)
     */
    String REDIS_KEY_EMU_RT_INFO = "iot:emu:rt:info:";

    /**
     * EMU实时数据 (不含 PCS、BMS)
     */
    String REDIS_KEY_EMU_RT_DATA = "iot:emu:rt:data:";

    /**
     * PCS实时设备信息
     */
    String REDIS_KEY_PCS_RT_INFO = "iot:pcs:rt:info:";

    /**
     * PCS实时数据
     */
    String REDIS_KEY_PCS_RT_DATA = "iot:pcs:rt:data:";

    /**
     * BMS实时设备信息
     */
    String REDIS_KEY_BMS_RT_INFO = "iot:bms:rt:info:";

    /**
     * BMS实时数据
     */
    String REDIS_KEY_BMS_RT_DATA = "iot:bms:rt:data:";

    /**
     * 液冷 实时信息
     */
    String REDIS_KEY_LIQUID_RT_INFO = "iot:liquid:rt:info:";

    /**
     * 液冷 实时数据
     */
    String REDIS_KEY_LIQUID_RT_DATA = "iot:liquid:rt:data:";

    /**
     * 除湿器 实时信息
     */
    String REDIS_KEY_DEH_RT_INFO = "iot:deh:rt:info:";

    /**
     * 除湿器 实时数据
     */
    String REDIS_KEY_DEH_RT_DATA = "iot:deh:rt:data:";
    /**
     * 消防系统实时信息
     */
    String REDIS_KEY_FFS_RT_INFO = "iot:ffs:rt:info:";
    /**
     * 消防系统 实时数据
     */
    String REDIS_KEY_FFS_RT_DATA = "iot:ffs:rt:data:";

    /**
     * UPS实时信息
     */
    String REDIS_KEY_UPS_RT_INFO = "iot:ups:rt:info:";


    /**
     * UPS实时数据
     */
    String REDIS_KEY_UPS_RT_DATA = "iot:ups:rt:data:";

    /**
     * UPS实时信息
     */
    String REDIS_KEY_METER_RT_INFO = "iot:meter:rt:info:";


    /**
     * 电表实时数据
     */
    String REDIS_KEY_METER_RT_DATA = "iot:meter:rt:data:";


//    String REDIS_KEY_DI_RT_INFO = "iot:emu:di:rt:info:";

    /**
     * 网关下行指令
     */
    String REDIS_KEY_IOT_GW_CMD = "iot:gw:cmd:";

    /**
     * 网关下行指令的seq集合
     */
    String REDIS_KEY_IOT_GW_CMD_SEQ = "iot:gw:cmdHash:";

    /**
     * 桩和场站的绑定关系
     */
    String REDIS_KEY_SITE_EVSE_REF = "iot:site:evse:";

    /**
     * 枪和场站的绑定关系
     */
    String REDIS_KEY_SITE_PLUG_REF = "iot:site:plug:";

    /**
     * 订单基础信息
     */
    String REDIS_KEY_CHARGE_ORDER_INFO = "order:info:";

    /**
     * 停充码
     */
    String REDIS_KEY_CHARGE_ORDER_STOP_CODE = "order:stopCode:";

    /**
     * 期望的soc限制值
     */
    String REDIS_KEY_EXPECT_LIMIT_SOC = "order:expectLimitSoc:";

    /**
     * soc限制值修改事件
     */
    String REDIS_KEY_LIMIT_SOC_EVENT = "order:limitSoc:event:";

    /**
     * 订单详情信息(桩心跳)
     */
    String REDIS_KEY_CHARGE_ORDER_DETAIL = "order:detail:";

    /**
     * 客户信息缓存, 格式为 cus:{appType}:{cusId}
     */
    String REDIS_KEY_CUSTOMER = "cus:{0}:{1}";

    /**
     * 登录后账号缓存信息 v2, 格式为 {topCommId}:cus:info:{cusId}
     */
    String REDIS_KEY_USER_INFO = "{0}:cus:info:{1}";

    /**
     * 账号登录 salt 的缓存, 格式为 {topCommId}:cus:login:salt:{username}
     */
    String REDIS_KEY_LOGIN_SALT = "{0}:cus:login:salt:{1}";


    /**
     * APP配置的 redis key
     */
    String REDIS_KEY_APP_CFG = "app:cfg";


    int REDIS_EVSE_TTL_MINUTES = 365 * 24 * 60;    // 365天
    int REDIS_PLUG_TTL_MINUTES = REDIS_EVSE_TTL_MINUTES;    // 60天
    int REDIS_CHARGE_ORDER_TTL_MINUTES = 60 * 4; // 4小时
    int REDIS_CUS_INFO_TTL_DAYS = 60; // 60天
    int REDIS_LOGIN_SALT_TTL= 60;   // 登录缓存有效期 60 秒
}