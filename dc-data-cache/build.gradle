
plugins {
    id 'java'
}

jar{
    enabled = true
}


dependencies {

    implementation project(':dc-base-model')
    implementation project(':dc-base-utils')
    //implementation('org.springframework.boot:spring-boot-starter-data-redis')
    implementation("com.fasterxml.jackson.core:jackson-core:${jacksonVersion}")
    implementation("com.fasterxml.jackson.core:jackson-annotations:${jacksonVersion}")
    implementation("com.fasterxml.jackson.core:jackson-databind:${jacksonVersion}")
}