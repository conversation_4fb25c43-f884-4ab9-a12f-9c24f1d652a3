

//ext {
//    JAXB_VERSION = '2.3.0'
//}

dependencies {


    implementation('org.springframework.cloud:spring-cloud-starter-netflix-eureka-server')
//    implementation('org.springframework.cloud:spring-cloud-bus')
//    implementation('org.springframework.cloud:spring-cloud-starter-bus-amqp')

    // logstash间接依赖于jaxb, 在java10+以上的环境, 缺少jaxb-api时, logstash无法正常启动
    implementation("org.glassfish.jaxb:jaxb-runtime")

    //implementation("javax.xml.bind:jaxb-api:${JAXB_VERSION}")
    //compile("com.sun.xml.bind:jaxb-core:${JAXB_VERSION}")
    //compile("com.sun.xml.bind:jaxb-impl:${JAXB_VERSION}")

    implementation('javax.activation:activation:1.1.1')

}
