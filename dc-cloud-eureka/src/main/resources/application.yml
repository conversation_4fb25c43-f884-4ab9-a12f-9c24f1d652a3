app:
  name: dcEureka

server:
  address: 0.0.0.0
  port: 7001
  use-forward-headers: true
  compression.enabled: true

eureka:
  instance:
    hostname: localhost
  client:
    registerWithEureka: false
    fetchRegistry: false
    healthcheck:
      enabled: true
    serviceUrl:
      defaultZone: http://aaa:bbb@${eureka.instance.hostname}:${server.port}/eureka/

management:
  context-path: /admin
  security:
    enabled: false



logging:
  level:
    org.springframework: 'INFO'
    
