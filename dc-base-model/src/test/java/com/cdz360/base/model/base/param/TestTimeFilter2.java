package com.cdz360.base.model.base.param;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;


@Slf4j
public class TestTimeFilter2 {

    @Test
    public void test_thisDay() {
        TimeFilter2 result = TimeFilter2.thisDay();
        log.info("startTime= {}, endTime= {}", result.getStartTime(), result.getEndTime());
    }


    @Test
    public void test_lastDay() {
        TimeFilter2 result = TimeFilter2.lastDay();
        log.info("startTime= {}, endTime= {}", result.getStartTime(), result.getEndTime());
    }

    @Test
    public void test_lastNDay() {
        TimeFilter2 result = TimeFilter2.lastNDay(3);
        log.info("startTime= {}, endTime= {}", result.getStartTime(), result.getEndTime());
    }

    @Test
    public void test_thisMonth() {
        TimeFilter2 result = TimeFilter2.thisMonth();
        log.info("startTime= {}, endTime= {}", result.getStartTime(), result.getEndTime());
    }

    @Test
    public void test_lastMonth() {
        TimeFilter2 result = TimeFilter2.lastMonth();
        log.info("startTime= {}, endTime= {}", result.getStartTime(), result.getEndTime());
    }

    @Test
    public void test_lastNMonth() {
        TimeFilter2 result = TimeFilter2.lastNMonth(3);
        log.info("startTime= {}, endTime= {}", result.getStartTime(), result.getEndTime());
    }
}
