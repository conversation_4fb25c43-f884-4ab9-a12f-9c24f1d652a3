package com.cdz360.base.model.es.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.Setter;

@Getter
public enum PcsAlarmCode implements DcEnum, EssBaseAlarmCode {
    UNKNOWN(0, "未知错误", ""),


    OFFLINE(1, "离线", "PCS设备离线"),

    HARD_WARE_ERROR(1001, "硬件故障", "本故障指的是硬件检测到的过压、过流故障。"),

    AC_OVER_LOAD_ERROR(1002, "逆变侧过流保护故障",
        "本故障是指逆变侧A/B/C相电流至少有一相超过软件过流保护设定值。"),

    BUS_OVER_LOAD_ERROR(1003, "母线电压故障",
        "本故障是指总母线、正母线和负母线电压中至少有一个超过软件过压保护设定值。"),

    ISLAND_ERROR(1004, "孤岛运行故障",
        "本故障是指PCS运行在并网状态时电网异常断开，此时PCS运行在孤岛状态，这是不允许的，因此PCS会上报此故障并停机。"),

    OFFSET_ERROR(1005, "偏移值故障", "本故障是指PCS软件采样偏移值异常。"),

    CFG_ERROR(1006, "参数配置故障", "本故障是指PCS软件参数配置异常。"),

    SIGNAL_ERROR(1007, "过调制故障", "本故障是指PCS控制环路出现过调制异常。"),

    GRID_VOLTAGE_ERROR(1008, "电网相电压故障",
        "本故障是指电网A/B/C相至少有一相出现软 件过压、欠压等故障。"),

    LINE_VOLTAGE_ERROR(1009, "电网线电压故障",
        "本故障是指电网L12,L23,L31线间至少有一 路出现软件过压、欠压等故障。"),

    GRID_RATE_ERROR(1010, "电网频率故障", "本故障是指电网频率超过PCS允许的最低和最高设定值。"),

    ON_GRID_ERROR(1011, "电网连接故障",
        "本故障是指PCS在并网过程中出现错误导致无法成功并网的故障。"),

    LOW_TEMP_ERROR(1012, "低温故障", "本故障是指PCS采集的温度超过其所允许的最低温度值。"),

    HIGH_TEMP_ERROR(1013, "高温故障", "本故障是指PCS采集的温度超过其所允许的最高温度值。"),

    ISO_ERROR(1014, "绝缘故障", "本故障是指PCS绝缘检测异常。"),

    RCD_ERROR(1015, "RCD故障", "本故障是指PCS残余电流检测异常。"),

    RES_ERROR(1016, "RES故障", "本故障是指PCS漏电流异常。"),

    AFCI_ERROR(1017, "AFCI故障", "本故障是指PCS出现电弧故障断路故障。"),

    AC_RELAY_ERROR(1018, "交流继电器故障", "本故障是指PCS开机自检过程中交流继电器吸合或断开异常。"),

    DC_POSITIVE_RELAY_DISCONNECT(1019, "直流主正继电器断开", "直流主正继电器断开"),

    DC_NEGATIVE_RELAY_DISCONNECT(1020, "直流主负继电器断开", "直流主负继电器断开"),

    AC_RELAY_DISCONNECT(1021, "交流继电器断开", "交流继电器断开"),

    DC_VOLTAGE_ERROR(1022, "直流过压", "直流过压"),

    GRID_PEAK_ERROR(1023, "电网幅值异常", "电网幅值异常"),

    GRID_PHASE_ERROR(1024, "电网相序异常", "电网相序异常"),

    IGBT_TEMP_ERROR(1025, "IGBT 过温", "IGBT 过温"),

    PCS_CURRENT_OVERLOAD(1026, "逆变电流过流", "逆变电流过流"),

    DC_SOFT_START_ERROR(1027, "直流软启动故障", "直流软启动故障"),

    DC_MAIN_CONTACTOR_ERROR(1028, "直流主接触器故障", "直流主接触器故障"),

    FAN_ERROR(1029, "风机故障", "风机故障"),

    MAIN_CONTACTOR_ERROR(1030, "主接触器故障", "主接触器故障"),

    IN_SWITCH_DISCONNECT(1031, "输入开关运行断开", "输入开关运行断开"),

    PCS_TEMP_HIGH_ERROR(1032, "机内过温", "机内过温"),

    SOFT_START_ERROR(1033, "软启动故障", "软启动故障"),

    COMMUNICATION_ERROR(1034, "通信故障", "通信故障"),

    SPD_ERROR(1035, "防雷器故障", "防雷器故障"),

    EMERGENCY_STOP(1036, "急停故障", "急停故障"),

    BMS_SYS_ERROR(1037, "BMS 系统故障", "BMS 系统故障"),

    BMS_COMM_ERROR(1038, "BMS 通信故障", "BMS 通信故障"),

    FNL_COMM_ERROR(1039, "防逆流通信故障", "防逆流通信故障"),

    REMOTE_COMM_ERROR(1040, "远程通信故障", "远程通信故障"),


    DOOR_STATUS_WARNING(1041, "门禁告警", "门禁告警"),

    PHASE_LOCK_ERROR(1042, "锁相异常", "锁相异常"),

    FNL_STOP(1043, "防逆流关机", "防逆流关机"),

    FAN_TEMP_HIGH_WARNING(1044, "散热器过温告警", "散热器过温告警"),

    PCS_HW_CURRENT_OVERLOAD(1045, "逆变硬件过流", "逆变硬件过流"),

    DRIVER_ERROR(1046, "驱动故障", "驱动故障"),

    OUTPUT_VOLTAGE_ERROR(1047, "输出电压异常", "输出电压异常"),

    BATTERY_VOLTAGE_OVERLOAD(1048, "电池过压", "电池过压"),

    BATTERY_LOW_LOAD_VOLTAGE_ERROR(1049, "电池轻载低压", "电池轻载低压"),

    DC_OVERLOAD(1050, "直流过流", "直流过流"),
    OUTPUT_VOLTAGE_OFF_GRID_ERROR(1051, "输出电压不符合离网条件", "输出电压不符合离网条件"),
    OVERLOAD_PROTECTION(1052, "过载保护", "过载保护"),
    SHORTED_PROTECTION(1053, "短路保护", "短路保护"),
    JOIN_COMM_ERROR(1054, "并机通信线异常保护", "并机通信线异常保护"),
    DC_FUSE_DISCONNECT(1055, "直流保险丝断开", "直流保险丝断开"),
    BATTERY_HIGH_LOAD_VOLTAGE_ERROR(1056, "电池重载低压", "电池重载低压"),
    BATTERY_LOW_VOLTAGE_WARNING(1057, "电池低压告警", "电池低压告警"),
    OUT_EMERGENCY_STOP_ERROR(1058, "外部急停故障", "外部急停故障"),
    BATTERY_CHARGE_VOLTAGE_ERROR(1059, "电池电压不符合充电条件", "电池电压不符合充电条件"),
    OVERLOAD_WARNING(1060, "过载告警", "过载告警"),
    OUT_CONTACTOR_ERROR(1061, "外部接触器故障", "外部接触器故障"),
    GRID_VOLTAGE_HIGH_ERROR(1062, "电网过压", "电网过压"),
    GRID_VOLTAGE_LOW_ERROR(1063, "电网欠压", "电网欠压"),
    GRID_RATE_HIGH_ERROR(1064, "电网过频异常", "电网过频异常"),
    GRID_RATE_LOW_ERROR(1065, "电网欠频异常", "电网欠频异常"),
    SMOKE_ERROR(1066, "烟雾告警", "烟雾告警"),
    ;

    @JsonValue
    final int code;

    final String msg;

    final String desc;
    @Setter
    private Integer level;

    PcsAlarmCode(int code, String msg, String desc) {
        this.code = code;
        this.msg = msg;
        this.desc = desc;
    }


    @JsonCreator
    public static PcsAlarmCode valueOf(Object codeIn) {
        if (codeIn == null) {
            return PcsAlarmCode.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof PcsAlarmCode) {
            return (PcsAlarmCode) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (PcsAlarmCode type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return PcsAlarmCode.UNKNOWN;
    }


}
