package com.cdz360.base.model.es.type;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Schema(description = "告警目标设备类型(子设备类型)")
@Getter
public enum WarnSubDeviceType {
    EMS(3, "EMS"),
    PCS(4, "PCS"),
    METER(5, "电表"),
    FIRE_FIGHTING(6, "消防装置"),
    UPS(7, "UPS"),
    AIR_SYS(8, "空调除湿"),
    BATTERY(9, "电池"),
    GRID(10, "电网"),
    PV(11, "光伏"),
    DSP(12, "DSP"),
    INVERTER(13, "逆变器"),
    FAN(14, "风扇"),
    INVERTER_RELAYS(15, "逆变继电器"),
    OIL_ENGINE(16, "油机"),
    LEAD_ACID_BATTERIES(17, "铅蓄电池"),
    RADIATOR(18, "散热器"),
    DC_BUS(19, "直流母线"),
    SURGE_PROTECTORS(20, "防雷器"),
    DC_CONVERTERS(21, "直流变换器"),
    BMS(30, "BMS"),
    BATTERY_STACK(31, "电池堆"),
    BATTERY_CLUSTER(32, "电池簇"),
    LIQUID(50, "液冷");

    private final String desc;
    private final long code;

    WarnSubDeviceType(long code, String desc) {
        this.code = code;
        this.desc = desc;
    }

//    电网电压异常
//    电网频率异常
//    电网电压反序
//    电网电压缺相
//    输出电压异常
//    输出频率异常
//    零线异常
//    环境温度过高
//    散热器温度过高
//    绝缘故障
//    漏电保护故障
//    辅助电源故障
//    风扇故障
//    机型容量故障
//    防雷器异常
//    孤岛保护
//    电池1未接
//    电池1过压
//    电池1欠压
//    电池1放电终止
//    电池1反接
//    电池2未接
//    电池2过压
//    电池2欠压
//    电池2放电终止
//    电池2反接
//    光伏1未接入
//    光伏1过压
//    光伏1均流异常
//    光伏2未接入
//    光伏2过压
//    光伏2均流异常
//    直流母线过压
//    直流母线欠压
//    直流母线电压不平衡
//    光伏1功率管故障
//    光伏2功率管故障
//    电池1功率管故障
//    电池2功率管故障
//    逆变器功率管故障
//    系统输出过载
//    逆变器过载
//    逆变器过载超时
//    电池1过载超时
//    电池2过载超时
//    逆变器软启动失败
//    电池1软启动失败
//    电池2软启动失败
//    DSP1参数设置故障
//    DSP2参数设置故障
//    DSP版本兼容故障
//    CPLD版本兼容故障
//    CPLD通讯故障
//    DSP通讯故障
//    输出电压直流量超限
//    输出电流直流量超限
//    继电器自检不通过
//    逆变器异常
//    接地不良
//    光伏1软起动失败
//    光伏2软起动失败
//    平衡电路过载超时
//    光伏1过载超时
//    光伏2过载超时
//    PCB过温
//    直流变换器过温
//    母线慢过压
//    离网输出电压异常
//    硬件母线过压
//    硬件过流
//    直流变换器过压
//    直流变换器硬件过压
//    直流变换器过流
//    直流变换器硬件过流
//    直流变换器谐振腔过流
//    光伏1反接
//    光伏2反接
//    电池1功率不足
//    电池2功率不足
//    电池1禁止充电
//    电池1禁止放电
//    电池2禁止充电
//    电池2禁止放电
//    电池1充满
//    电池1放电终止
//    电池2充满
//    电池2放电终止
//    负载功率过载
//    漏电自检异常
//    逆变过温告警
//    逆变器过温
//    直流变换器过温告警
//    并机通信告警
//    系统降额运行
//    逆变继电器开路
//    逆变继电器短路
//    光伏接入方式错误告警
//    并机模块缺失
//    并机模块机号重复
//    并机模块参数冲突
//    预留告警4
//    预留告警5
//    逆变器封脉冲
//    光伏3未接入
//    光伏3过压
//    光伏3均流异常
//    光伏4未接入
//    光伏4过压
//    光伏4均流异常
//    光伏3功率管故障
//    光伏4功率管故障
//    光伏3软起动失败
//    光伏4软起动失败
//    光伏3过载超时
//    光伏4过载超时
//    光伏3反接
//    光伏4反接
//    油机电压异常
//    油机频率异常
//    油机电压反序
//    油机电压缺相
//    铅蓄电池温度异常
//    电池接入方式错误
}
