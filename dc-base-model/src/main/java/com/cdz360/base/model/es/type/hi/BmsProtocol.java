package com.cdz360.base.model.es.type.hi;

import com.cdz360.base.model.base.type.DcEnum;
import com.cdz360.base.model.charge.type.OrderStartType;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.util.List;
import lombok.Getter;

@Getter
public enum BmsProtocol implements DcEnum {

    UNKNOWN(0, "无"),
    PYLON(1, "派能"),
    DYNESS(2, "大秦"),
    GREENWAY(3, "博力威"),
    FAR_EAST(4, "远东"),
    SOLUNA(5, "赛南"),
    HYPER_STRONG(6, "海博思创"),
    AVIC_TECH(7, "中航太克"),
    WATTSONIC(8, "旭浦"),
    LITHIUM_VALLEY(9, "锂谷"),
    HUASU(10, "华宿"),
    SHUANGDENG(11, "双登"),
    SUNWODA(12, "欣旺达"),
    ;

    @JsonValue
    private final int code;
    private final String desc;

    BmsProtocol(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static BmsProtocol valueOf(Object codeIn) {
        if (codeIn == null) {
            return BmsProtocol.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof OrderStartType) {
            return (BmsProtocol) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (BmsProtocol status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return BmsProtocol.UNKNOWN;
    }

    /**
     * 不同的BMS协议对应不同的BMS故障、BMS告警和BMS保护
     *
     * @param protocol
     * @return
     */
    public static int getProtocolStepValue(BmsProtocol protocol) {
        List<BmsProtocol> types1 = List.of(PYLON, DYNESS, GREENWAY, FAR_EAST, HYPER_STRONG,
            WATTSONIC, LITHIUM_VALLEY);
        int step = 0;
        if (protocol == null || types1.contains(protocol)) {
            step = 0;
        } else if (SOLUNA.equals(protocol)) {
            step = 100;
        } else if (HUASU.equals(protocol)) {
            step = 200;
        }
        return step;
    }

}
