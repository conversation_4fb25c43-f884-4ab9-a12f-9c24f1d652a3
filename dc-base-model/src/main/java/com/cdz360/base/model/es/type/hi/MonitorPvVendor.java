package com.cdz360.base.model.es.type.hi;

import com.cdz360.base.model.base.type.DcEnum;
import com.cdz360.base.model.charge.type.OrderStartType;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Getter
@Schema(description = "PV厂商")
public enum MonitorPvVendor implements DcEnum {

    UNKNOWN(0, "未知"),
    ACTION_POWER(1, "爱科赛博"),
    ;

    @JsonValue
    private final int code;
    private final String desc;

    MonitorPvVendor(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static MonitorPvVendor valueOf(Object codeIn) {
        if (codeIn == null) {
            return MonitorPvVendor.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof OrderStartType) {
            return (MonitorPvVendor) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (MonitorPvVendor status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return MonitorPvVendor.UNKNOWN;
    }

}