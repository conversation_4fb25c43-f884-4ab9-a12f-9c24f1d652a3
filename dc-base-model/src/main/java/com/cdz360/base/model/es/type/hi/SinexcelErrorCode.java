package com.cdz360.base.model.es.type.hi;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum SinexcelErrorCode implements DcEnum {

    /**
     * 以下为逆变器和整流器相关告警 0 ~ 40000
     */
    UNKNOWN(0, "未知"),
    GRID_VOLTAGE_ABNORMAL(0x1048, "电网电压异常"),
    GRID_FREQUENCY_ABNORMAL(0x1049, "电网频率异常"),
    GRID_VOLTAGE_ANTITONE(0x104A, "电网电压反序"),
    GRID_VOLTAGE_OUT_OF_PHASE(0x104B, "电网电压缺相"),
    OUTPUT_VOLTAGE_ABNORMAL(0x104C, "输出电压异常"),
    OUTPUT_FREQUENCY_ABNORMAL(0x104D, "输出频率异常"),
    ZERO_LINE_ABNORMAL(0x104E, "零线异常"),
    ENV_OVER_TEMPERATURE(0x104F, "环境温度过高"),
    RADIATOR_OVER_TEMPERATURE(0x1050, "散热器温度过高"),
    INSULATION_FAULT(0x1051, "绝缘故障"),
    LEAKAGE_PROTECTION_FAULT(0x1052, "漏电保护故障"),
    AUXILIARY_POWER_FAULT(0x1053, "辅助电源故障"),
    FAN_ERROR(0x1054, "风扇故障"),
    MODEL_CAPACITY_FAULT(0x1055, "机型容量故障"),
    SURGE_ARRESTER_FAULT(0x1056, "防雷器异常"),
    ISLANDING_PROTECTION(0x1057, "孤岛保护"),
    BATTERY_NOT_CONNECTED_1(0x1058, "电池1未接"),
    BATTERY_OVERVOLTAGES_1(0x1059, "电池1过压"),
    BATTERY_UNDERVOLTAGE_1(0x105A, "电池1欠压"),
    BATTERY_DISCHARGE_TERMINATES_1(0x105B, "电池1放电终止"),
    BATTERY_REVERSED_1(0x105C, "电池1反接"),
    BATTERY_NOT_CONNECTED_2(0x105D, "电池2未接"),
    BATTERY_OVERVOLTAGES_2(0x105E, "电池2过压"),
    BATTERY_UNDERVOLTAGE_2(0x105F, "电池2欠压"),
    BATTERY_DISCHARGE_TERMINATES_2(0x1060, "电池2放电终止"),
    BATTERY_REVERSED_2(0x1061, "电池2反接"),
    PV_NOT_CONNECTED_1(0x1062, "光伏1未接入"),
    PV_OVERVOLTAGES_1(0x1063, "光伏1过压"),
    PV_CURRENT_EQUALIZATION_ANOMALY_1(0x1064, "光伏1均流异常"),
    PV_NOT_CONNECTED_2(0x1065, "光伏2未接入"),
    PV_OVERVOLTAGES_2(0x1066, "光伏2过压"),
    PV_CURRENT_EQUALIZATION_ANOMALY_2(0x1067, "光伏2均流异常"),
    DC_BUS_OVERVOLTAGE(0x1068, "直流母线过压"),
    DC_BUS_UNDERVOLTAGE(0x1069, "直流母线欠压"),
    DC_BUS_VOLTAGE_IMBALANCE(0x106A, "直流母线电压不平衡"),
    PV_POWER_TUBE_IS_FAULTY_1(0x106B, "光伏1功率管故障"),
    PV_POWER_TUBE_IS_FAULTY_2(0x106C, "光伏2功率管故障"),
    BATTERY_POWER_TUBE_IS_FAULTY_1(0x106D, "电池1功率管故障"),
    BATTERY_POWER_TUBE_IS_FAULTY_2(0x106E, "电池2功率管故障"),
    INVERTER_POWER_TUBE_IS_FAULTY(0x106F, "逆变器功率管故障"),
    SYSTEM_OUTPUT_OVERLOAD(0x1070, "系统输出过载"),
    INVERTER_OVERLOAD(0x1071, "逆变器过载"),
    INVERTER_OVERLOAD_TIMEOUT(0x1072, "逆变器过载超时"),
    BATTERY_OVERLOAD_TIMEOUT_1(0x1073, "电池1过载超时"),
    BATTERY_OVERLOAD_TIMEOUT_2(0x1074, "电池2过载超时"),
    INVERTER_SOFT_STARTUP_FAIL(0x1075, "逆变器软启动失败"),
    BATTERY_SOFT_STARTUP_FAIL_1(0x1076, "电池1软启动失败"),
    BATTERY_SOFT_STARTUP_FAIL_2(0x1077, "电池2软启动失败"),
    DSP1_PARAM_SETTINGS_FAULTY(0x1078, "DSP1参数设置故障"),
    DSP2_PARAM_SETTINGS_FAULTY(0x1079, "DSP2参数设置故障"),
    DSP_VERSION_INCOMPATIBLE_FAULTY(0x107A, "DSP版本兼容故障"),
    CPLD_VERSION_INCOMPATIBLE_FAULTY(0x107B, "CPLD版本兼容故障"),
    CPLD_COMMUNICATION_FAULTY(0x107C, "CPLD通讯故障"),
    DSP_COMMUNICATION_FAULTY(0x107D, "DSP通讯故障"),
    OUTPUT_VOLTAGE_DIRECT_CURRENT_EXCEEDS_THE_LIMIT(0x107E, "输出电压直流量超限"),
    DIRECT_OUTPUT_CURRENT_EXCEEDS_THE_LIMIT(0x107F, "输出电流直流量超限"),
    RELAY_SELF_TEST_FAILED(0x1080, "继电器自检不通过"),
    INVERTER_EXCEPTION(0x1081, "逆变器异常"),
    IMPERFECT_EARTH(0x1082, "接地不良"),
    PV_SOFT_START_FAILS_1(0x1083, "光伏1软起动失败"),
    PV_SOFT_START_FAILS_2(0x1084, "光伏2软起动失败"),
    BALANCE_CIRCUIT_OVERLOAD_TIMEOUT(0x1085, "平衡电路过载超时"),
    PV_OVERLOAD_TIMEOUT_1(0x1086, "光伏1过载超时"),
    PV_OVERLOAD_TIMEOUT_2(0x1087, "光伏2过载超时"),
    PCB_OVERTEMPERATURE(0x1088, "PCB过温"),
    DC_CONVERTER_OVERTEMPERATURE(0x1089, "直流变换器过温"),
    BUS_SLOW_OVERVOLTAGE(0x108A, "母线慢过压"),
    OFF_NETWORK_OUTPUT_VOLTAGE_ABNORMAL(0x108B, "离网输出电压异常"),
    HARDWARE_BUS_OVERVOLTAGE(0x108C, "硬件母线过压"),
    HARDWARE_OVERCURRENT(0x108D, "硬件过流"),
    DC_CONVERTER_OVERVOLTAGE(0x108E, "直流变换器过压"),
    DC_CONVERTER_HARDWARE_OVERVOLTAGE(0x108F, "直流变换器硬件过压"),
    DC_CONVERTER_OVERCURRENT(0x1090, "直流变换器过流"),
    DC_CONVERTER_HARDWARE_OVERCURRENT(0x1091, "直流变换器硬件过流"),
    DC_CONVERTER_CAVITY_OVERCURRENT(0x1092, "直流变换器谐振腔过流"),
    PV_REVERSE_CONNECTION_1(0x1093, "光伏1反接"),
    PV_REVERSE_CONNECTION_2(0x1094, "光伏2反接"),
    BATTERY_LOW_1(0x1095, "电池1功率不足"),
    BATTERY_LOW_2(0x1096, "电池2功率不足"),
    BATTERY_NOT_CHARGE_1(0x1097, "电池1禁止充电"),
    BATTERY_NOT_DISCHARGE_1(0x1098, "电池1禁止放电"),
    BATTERY_NOT_CHARGE_2(0x1099, "电池2禁止充电"),
    BATTERY_NOT_DISCHARGE_2(0x109A, "电池2禁止放电"),
    BATTERY_FULL_1(0x109B, "电池1充满"),
    BATTERY_DISCHARGE_TERMINATION_1(0x109C, "电池1放电终止"),
    BATTERY_FULL_2(0x109D, "电池2充满"),
    BATTERY_DISCHARGE_TERMINATION_2(0x109E, "电池2放电终止"),
    LOAD_POWER_OVERLOAD(0x109F, "负载功率过载"),
    LEAKAGE_SELF_TEST_ABNORMAL(0x10A0, "漏电自检异常"),
    INVERTER_OVERTEMPERATURE_ALARM_GENERATED(0x10A1, "逆变过温告警"),
    INVERTER_OVERHEATED(0x10A2, "逆变器过温"),
    DC_CONVERTER_OVERTEMPERATURE_ALARM(0x10A3, "直流变换器过温告警"),
    PARALLEL_COMMUNICATION_ALARM(0x10A4, "并机通信告警"),
    SYSTEM_RUNS_DERATED(0x10A5, "系统降额运行"),
    OPEN_INVERTER_RELAY(0x10A6, "逆变继电器开路"),
    INVERTER_RELAY_SHORT_CIRCUIT(0x10A7, "逆变继电器短路"),
    PV_ACCESS_MODE_INCORRECT(0x10A8, "光伏接入方式错误告警"),
    PARALLEL_MODULE_MISSING(0x10A9, "并机模块缺失"),
    PARALLEL_MODULE_NUMBER_REPEATED(0x10AA, "并机模块机号重复"),
    PARAM_OF_PARALLEL_MODULES_CONFLICT(0x10AB, "并机模块参数冲突"),
    PV_DC_ARC_FAULT(0x10AC, "光伏直流电弧故障"),
    METER_ACCESS_ANOMALY(0x10AD, "电表接入异常"),
    INVERTER_SEAL_PULSE(0x10AE, "逆变器封脉冲"),
    PV_NOT_CONNECTED_3(0x10AF, "光伏3未接入"),
    PV_OVERVOLTAGES_3(0x10B0, "光伏3过压"),
    PV_CURRENT_EQUALIZATION_ANOMALY_3(0x10B1, "光伏3均流异常"),
    PV_NOT_CONNECTED_4(0x10B2, "光伏4未接入"),
    PV_OVERVOLTAGES_4(0x10B3, "光伏4过压"),
    PV_CURRENT_EQUALIZATION_ANOMALY_4(0x10B4, "光伏4均流异常"),
    PV_POWER_TUBE_IS_FAULTY_3(0x10B5, "光伏3功率管故障"),
    PV_POWER_TUBE_IS_FAULTY_4(0x10B6, "光伏4功率管故障"),
    PV_SOFT_START_FAILS_3(0x10B7, "光伏3软起动失败"),
    PV_SOFT_START_FAILS_4(0x10B8, "光伏4软起动失败"),
    PV_OVERLOAD_TIMEOUT_3(0x10B9, "光伏3过载超时"),
    PV_OVERLOAD_TIMEOUT_4(0x10BA, "光伏4过载超时"),
    PV_REVERSE_CONNECTION_3(0x10BB, "光伏3反接"),
    PV_REVERSE_CONNECTION_4(0x10BC, "光伏4反接"),
    OIL_ENGINE_VOLTAGE_ABNORMAL(0x10BD, "油机电压异常"),
    OIL_ENGINE_FREQUENCY_ABNORMAL(0x10BE, "油机频率异常"),
    OIL_ENGINE_VOLTAGE_REVERSE(0x10BF, "油机电压反序"),
    OIL_ENGINE_VOLTAGE_PHASE_FAILURE(0x10C0, "油机电压缺相"),
    LEAD_ACCUMULATOR_TEMPERATURE_ABNORMAL(0x10C1, "铅蓄电池温度异常"),
    BATTERY_CONNECTION_MODE_INCORRECT(0x10C2, "电池接入方式错误"),
    RESERVED_ALARM_5(0x10C3, "预留告警5"),
    BATTERY_1_UP_TO_POWER_RESERVE_SOC(0x10C4, "电池1达到备电SoC"),
    BATTERY_2_UP_TO_POWER_RESERVE_SOC(0x10C5, "电池2达到备电SoC"),
    GRID_OVERLOAD(0x10C6, "电网过载"),
    OIL_ENGINE_OVERLOAD(0x10C7, "油机过载"),
    BUSBAR_SOFT_START_FAILURE(0x10C8, "母线软起失败"),
    GRID_RELAY_OPEN_CIRCUIT(0x10C9, "电网继电器开路"),
    GRID_RELAY_SHORT_CIRCUIT(0x10CA, "电网继电器短路"),

    /**
     * 以下为电表相关告警 40000 ~ 59999
     * METER_AMMETER_ERROR(40000, "电表故障"),
     */

    /**
     * 以下为BMS相关告警 60000 ~ 79999
     * BMS_ERROR(60000, "电表故障"),
     */

    /**
     * 以下为监控相关告警 80000 ~ 99999
     */
    DCDC_COMMUNICATION_FAILS(80000, "DCDC通讯失败"),
    DCAC_COMMUNICATION_FAILS(80001, "DCAC通讯失败"),
    SLAVE_COMMUNICATION_FAILS(80002, "从机通讯失败"),
    MASTER_SLAVE_BOOTLOADER_VERSION_INCONSISTENCY(80003, "主从机Bootloader版本不一致"),
    MASTER_SLAVE_APP_VERSION_INCONSISTENCY(80004, "主从机APP版本不一致"),
    ;

    @JsonValue
    private final int code;

    private final String desc;

    SinexcelErrorCode(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static SinexcelErrorCode valueOf(Object codeIn) {
        if (codeIn == null) {
            return SinexcelErrorCode.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof SinexcelErrorCode) {
            return (SinexcelErrorCode) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (SinexcelErrorCode type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return SinexcelErrorCode.UNKNOWN;
    }


}
