package com.cdz360.base.model.es.type.hi;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum UpgradeStatusCode {

    UNKNOWN(0x999, "未知"),
    CONNECT_SUCCESS(0x00, "连接成功"),
    FLASH_ERASE_SUCCESS(0x01, "FLASH擦除成功"),
    FLASH_ERASE_FAILURE(0x02, "FLASH擦除失败"),
    FLASH_WRITE_SUCCESS(0x03, "写入FLASH成功"),
    FLASH_WRITE_FAILURE(0x04, "写入FLASH失败"),
    UPGRADE_SUCCESS(0x05, "升级成功"),
    UPGRADE_FAILURE(0x06, "升级失败"),
    DATA_LENGTH_ERROR(0x07, "数据长度错误"),
    CHECKSUM_ERROR(0x08, "校验和错误"),
    SEQUENCE_ERROR(0x09, "顺序错误"),
    BLOCK_BYTE_COUNT_ERROR(0x0A, "Block字节数错误"),
    BLOCK_OFFSET_ADDRESS_ERROR(0x0B, "Block偏移地址错误"),
    FILE_TYPE_ERROR(0x0C, "文件类型错误"),
    FILE_LENGTH_NOT_ALIGNED(0x0D, "文件长度未对齐"),
    ;

    @JsonValue
    private final int code;
    private final String desc;

    UpgradeStatusCode(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static UpgradeStatusCode valueOf(Object codeIn) {
        if (codeIn == null) {
            return UpgradeStatusCode.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof UpgradeStatusCode) {
            return (UpgradeStatusCode) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (UpgradeStatusCode type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return UpgradeStatusCode.UNKNOWN;
    }


}
