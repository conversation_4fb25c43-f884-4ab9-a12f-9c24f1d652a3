package com.cdz360.base.model.iot.type;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 海外版使用，ODM供应商类型
 */
public enum ODMType {

    HUICHONG("A", "慧充"),
    YIPU("B", "驿普"),
    YONGLIAN("C", "永联"),
    YONGLIAN_GUOGAIOU("CX", "永联国改欧"),
    UNKNOWN("Z", "未知或剔除的"),
    RUNCHENGDA("D", "润诚达"),
    LINGCHONG("E", "领充"),
    ZHONGPUSEN("F", "众普森"),
    HENGYAO("G", "亨耀"),
    ;

    @JsonValue
    @Getter
    private final String code;
    @Getter
    private final String desc;

    ODMType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static ODMType valueOf(Object codeIn) {
        if (codeIn == null) {
            return ODMType.UNKNOWN;
        }
        String code = "";
        if (codeIn instanceof ODMType) {
            return (ODMType) codeIn;
        } else if (codeIn instanceof String) {
            code = String.valueOf(codeIn);
        }
        for (ODMType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return ODMType.UNKNOWN;
    }

}
