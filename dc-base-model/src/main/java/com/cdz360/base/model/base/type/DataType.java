package com.cdz360.base.model.base.type;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum DataType implements DcEnum {
    OTHER(0, "其它"),
    CURRENT(1, "电流"),
    VOLTAGE(2, "电压"),
    POWER(3, "功率"),
    KWH(4, "电量"),
    TEMPERATURE(5, "温度"),
    HUMIDITY(6, "湿度"),
    PRESSURE(7, "压力"),
    SPEED(8, "速度"),
    FLOW(9, "流量"),
    ;

    @JsonValue
    final int code;

    final String desc;

    DataType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static DataType valueOf(Object codeIn) {
        if (codeIn == null) {
            return null;
        }
        int code = 0;
        if (codeIn instanceof DataType) {
            return (DataType) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (DataType type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }

}
