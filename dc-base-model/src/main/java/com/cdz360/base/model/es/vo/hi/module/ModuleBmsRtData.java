package com.cdz360.base.model.es.vo.hi.module;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@JsonInclude(Include.NON_NULL)
public class ModuleBmsRtData {

    @Schema(description = "电池柜数据")
    @JsonInclude(Include.NON_EMPTY)
    @JsonProperty("bcl")
    @JsonAlias({"batteryCupboardList", "bcl"})
    private List<ModuleBmsRtDataTemplate> batteryCupboardList;

}
