package com.cdz360.base.model.es.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.Setter;

@Getter
public enum FfsAlarmCode implements DcEnum, EssBaseAlarmCode {
    UNKNOWN(0, "未知错误", ""),

    OFFLINE(1, "离线", "消防设备离线"),

    FAULT(2, "故障", "消防设备故障"),

    WORK(3, "喷淋", "消防喷淋");

    @JsonValue
    final int code;

    final String msg;

    final String desc;
    @Setter
    private Integer level;

    FfsAlarmCode(int code, String msg, String desc) {
        this.code = code;
        this.msg = msg;
        this.desc = desc;
    }


    @JsonCreator
    public static FfsAlarmCode valueOf(Object codeIn) {
        if (codeIn == null) {
            return FfsAlarmCode.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof FfsAlarmCode) {
            return (FfsAlarmCode) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (FfsAlarmCode type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return FfsAlarmCode.UNKNOWN;
    }
}
