package com.cdz360.base.model.es.dto;

import com.cdz360.base.model.es.vo.EssInOutStrategyItem;
import com.cdz360.base.model.es.vo.EssPriceItem;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "更新ESS配置数据项")
@Data
@Accessors(chain = true)
public class UpdateEssCfgDto {

    @Schema(description = "EMS配置参数传递")
    @JsonInclude(Include.NON_NULL)
    private EmsConfigParamDto emsConfigParam;

    @Schema(description = "PCS配置参数传递")
    @JsonInclude(Include.NON_NULL)
    private PcsConfigParamDto pcsConfigParam;

    @Schema(description = "控制策略参数(有攻防逆流与无功优化)")
    @JsonInclude(Include.NON_NULL)
    private ControlStrategyParamDto controlStrategyParam;

    @Schema(description = "生效时间(仅作用与时段电价)")
    @JsonInclude(Include.NON_NULL)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private LocalDateTime activeDateTime;

    @Schema(description = "充电时段电价")
    @JsonInclude(Include.NON_NULL)
    private List<EssPriceItem> chargePriceItems;

    @Schema(description = "放电时段电价")
    @JsonInclude(Include.NON_NULL)
    private List<EssPriceItem> dischargePriceItems;

    @Schema(description = "充放电时段")
    @JsonInclude(Include.NON_NULL)
    private List<EssInOutStrategyItem> inOutItems;
}
