package com.cdz360.base.model.base.vo;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "范围")
public class RangeVo {


    @Schema(description = "最小值")
    private BigDecimal min;

    @Schema(description = "最大值")
    private BigDecimal max;
}
