package com.cdz360.base.model.iot.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum PowerSupply implements DcEnum {

    UNKNOWN(0, "未知"),
    MAIN_POWER(1, "主电"),
    BACKUP_POWER(2, "备电"),
    BOTH(3, "主备电"),
    ;

    @JsonValue
    private final int code;
    private final String desc;

    PowerSupply(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static PowerSupply valueOf(Object codeIn) {
        if (codeIn == null) {
            return PowerSupply.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof PowerSupply) {
            return (PowerSupply) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (PowerSupply type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return PowerSupply.UNKNOWN;
    }
}
