package com.cdz360.base.model.iot.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * @Classname EssEquipType
 * @Description
 * @Date 10/12/2021 9:20 AM
 * @Created by Rafael
 */
@Getter
public enum EssEquipType implements DcEnum {

    UNKNOWN(0, 0, "未知"),
    EMS(2000, 30, "EMS"),
    EMU(2001, 30, "EMU"),
    ESS(2002, 30, "储能柜"),
    MGC(2003, 30, "微网控制器"),
    PCS(2100, 30, "PCS"),
    STS(2200, 30, "STS"),
    CENTRAL_CONTROL(2300, 30, "集控"),

    BMS(2400, 1, "BMS"),

    HYBRID_INVERTER(2500, 30, "户储"),

    BATTERY_STACK(3000, 15 * 60, "电池堆"),
    BATTERY_PACK(3100, 15 * 60, "电池簇"),
    PV_INV(4000, 30, "光伏逆变器"),
    PV_JUNCTION_BOX(4100, 30, "光伏汇流箱"),

    EVSE(5001, 30, "充电桩"),  // 通用充电桩类型
    DC_EVSE(5000, 30, "直流充电桩"),
    AC_EVSE(5100, 30, "交流充电桩"),
    METER(6001, 30, "电表"),  // 通用电表类型
    GRID_GATEWAY_METER(6000, 30, "电网关口电表"),
    ESS_GATEWAY_METER(6100, 30, "储能并网点电表"),
    PV_INV_GATEWAY_METER(6200, 30, "光伏并网点电表"),
    EVSE_GATEWAY_METER(6300, 30, "充电桩计量表"),
    WIND_ENERGY_GATEWAY_METER(6400, 30, "风能并网点电表"),
    LOAD_GATEWAY_METER(6500, 30, "负载用电电表"),
    ESS_INSIDE_METER(6600, 30, "储能内部用电电表"),
    HIGH_VOLTAGE_SIDE_METER(6700, 30, "高压侧用电电表"),
    ACDC(7000, 30, "ACDC"),
    DCDC(7100, 30, "DCDC"),
    AIR_CONDITION(9000, 30, "空调"),
    FIRE_FIGHTING(9100, 30, "消防"),
    UPS(9200, 30, "UPS"),
    DIESEL_ENGINE(9300, 30, "柴油机"),
    DEHUMIDIFIER(9400, 30, "除湿器"),
    LIQUID_COOLING(9500, 30, "液冷"),
    ;


    @JsonValue
    private final int code;
    // 运行数据查询的时间间隔,单位‘秒’,小于1表示不执行
    private final int rtTime;
    private final String desc;

    EssEquipType(int code, int rtTime, String desc) {
        this.code = code;
        this.rtTime = rtTime;
        this.desc = desc;
    }

    @JsonCreator
    public static EssEquipType valueOf(Object codeIn) {
        if (codeIn == null) {
            return EssEquipType.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof EssEquipType) {
            return (EssEquipType) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (EssEquipType type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return EssEquipType.UNKNOWN;
    }
}