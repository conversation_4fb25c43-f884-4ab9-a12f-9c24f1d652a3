package com.cdz360.base.model.meter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 电表实时数据
 */
@Data
public class MeterAbcData {

    /**
     * 线电压
     * V1是AB,V2是BC,V3是CA，没有total
     */
    @JsonProperty("lv")
    private MeterAbcItem lineVoltage;

    /**
     * <p>ABC相电压, 已乘变比, 单位 V</p>
     */
    private MeterAbcItem voltage;

    /**
     * <p>ABC相电流, 已乘变比, 单位 A</p>
     */
    private MeterAbcItem current;

    /**
     * <p>有功总功率(总,A,B,C), (已乘变比), 单位 kW</p>
     */
    private MeterAbcItem activePower;

    /**
     * <p>无功功率(总,A,B,C), (已乘变比), 单位 kVar</p>
     */
    private MeterAbcItem reactivePower;

    /**
     * <p>视在功率(总,A,B,C), (已乘变比), 单位 kVA</p>
     */
    private MeterAbcItem apparentPower;

    /**
     * <p>频率</p>
     */
    private BigDecimal rate;

    /**
     * <p>功率因数(总,A,B,C)</p>
     */
    private MeterAbcItem pf;

    /**
     * <p>当前有功需量，单位 kW</p>
     */
    @JsonProperty("ad")
    private BigDecimal activeDemand;

    /**
     * <p>最大有功需量，单位 kW</p>
     */
    @JsonProperty("adm")
    private BigDecimal activeDemandMax;

    @JsonProperty("admt")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(title = "最大有功需量发生时间", description = "最大有功需量发生时间")
    private LocalDateTime activeDemandTime;
}
