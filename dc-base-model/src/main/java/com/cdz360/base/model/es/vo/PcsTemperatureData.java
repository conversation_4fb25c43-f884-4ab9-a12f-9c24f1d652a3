package com.cdz360.base.model.es.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class PcsTemperatureData {

    @Schema(title = "PCS内部环境温度")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal pcsTemp;

    @Schema(title = "散热器温度")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal fanTemp;

    @Schema(title = "模块1温度")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal modularTemp1;


    @Schema(title = "模块2温度")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal modularTemp2;


    @Schema(title = "模块3温度")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal modularTemp3;


    @Schema(title = "模块4温度")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal modularTemp4;




}
