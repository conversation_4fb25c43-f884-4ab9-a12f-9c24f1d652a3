package com.cdz360.base.model.charge.vo;

import com.cdz360.base.model.base.type.ChargeOrderStatus;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.base.type.PaymentStatus;
import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.base.model.charge.dto.BatteryDto;
import com.cdz360.base.model.charge.dto.BatteryDynamicDto;
import com.cdz360.base.model.charge.dto.BmsDto;
import com.cdz360.base.model.charge.dto.BmsDynamicDto;
import com.cdz360.base.model.charge.type.HlhtType;
import com.cdz360.base.model.charge.type.OrderPayStatus;
import com.cdz360.base.model.charge.type.OrderStartType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description = "充电订单信息")
public class ChargeOrderVo implements Serializable {
    private static final long serialVersionUID = -5663749435823131392L;


    /**
     * 订单号
     */
    @Schema(description = "订单号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String orderNo;

    @Schema(description = "合作方(互联互通)编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String partnerNo;

    @Schema(description = "合作方(互联互通)订单号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String partnerOrderNo;

    @Schema(description = "openId")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String openId;

    @Schema(description = "即充即退的支付单号(微信支付/支付宝)")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String prepay3rdTradeNo;

    @Schema(description = "订单启动方式")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private OrderStartType startType;

    /**
     * 订单充电状态
     */
    @Schema(description = "订单充电状态")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private ChargeOrderStatus status;

    /**
     * 桩编号
     */
    @Schema(description = "桩编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String evseNo;
    /**
     * 枪头序号
     */
    @Schema(description = "枪头序号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private int plugIdx;

    @Schema(description = "电流类型")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private SupplyType supplyType;

    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private long cusId;

    /**
     * 客户手机号
     */
    @Schema(description = "客户手机号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String cusPhone;

    /**
     * 客户的归属商户ID
     */
    @Schema(description = "客户的归属商户ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long cusCommId;

    /**
     * 客户的归属商户名称
     */
    @Schema(description = "客户的归属商户名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String cusCommName;

    @Schema(description = "客户所属的企业名称")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long corpId;

    /**
     * 场站ID
     */
    @Schema(description = "场站ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteId;

    /**
     * 场站的归属商户ID
     */
    @Schema(description = "场站的归属商户ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long siteCommId;

    @Schema(description = "城市编码")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String cityCode;

    /**
     * 逻辑卡号
     */
    @Schema(description = "逻辑卡号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String cardNo;

    /**
     * 物理卡号
     */
    @Schema(description = "物理卡号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String cardChipNo;

    @Schema(description = "车牌号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String carNo;

    @Schema(description = "车辆自编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String carNum;
    /**
     * 车架号
     */
    @Schema(description = "车架号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String vin;

    @Schema(description = "车辆线路编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String lineNum;

    /**
     * 付款账户ID
     */
    @Schema(description = "付款账户ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private long payAccountId = 0L;

    /**
     * 付款账户类型
     */
    @Schema(description = "付款账户类型")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private PayAccountType payAccountType;

    /**
     * 支付状态
     */
    @Schema(description = "支付状态")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Deprecated // 使用 payStatus 替换
    private PaymentStatus paymentStatus;

    /**
     * 支付状态
     */
    @Schema(description = "支付状态")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private OrderPayStatus payStatus;

    /**
     * 互联互通类型
     */
    @Schema(description = "互联互通类型")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private HlhtType hlhtType;

    /**
     * 消费电量
     */
    @Schema(description = "消费电量, 单位'kwh'")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal kwh;
    /**
     * 电费金额
     */
    @Schema(description = "电费金额, 单位'元'")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal elecFee;

    /**
     * 服务费金额
     */
    @Schema(description = "服务费金额, 单位'元'")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal servFee;

    @Schema(description = "超停费用, 单位'元'")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal overtimeParkFee;

    /**
     * 充电开始时间
     */
    @Schema(description = "充电开始时间(桩端)")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date startTime;

    /**
     * 充电结束时间
     */
    @Schema(description = "充电结束时间(桩端)")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date stopTime;

    @Schema(description = "订单支付时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date paymentTime;

    @Schema(description = "充电完成原因")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer completeCode;

    /**
     * com.cdz360.base.model.charge.type.OrderStopCode
     */
    @Schema(description = "停充原因. 0表示正常停充; 其他都表示异常停充", example = "0")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer stopCode;

    /**
     * 开始soc
     */
    @Schema(description = "开始SOC")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer startSoc;
    /**
     * 当前soc
     */
    @Schema(description = "当前SOC")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer curSoc;

    @Schema(description = "限制SOC")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer limitSoc;

    @Schema(description = "期望限制SOC")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer expectLimitSoc;

    @Schema(description = "已充时长, 单位'分钟'", example = "15")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer duration;

    @Schema(description = "预计剩余充电时间, 单位'分钟'", example = "23")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer remainingTime;


    @Schema(description = "充电开始前电表读数, 单位 KWH", example = "123.45")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal startMeter;

    @Schema(description = "充电完成后电表读数, 单位 KWH", example = "456.78")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal stopMeter;

    @Schema(description = "直流输出电压, 单位'伏'", example = "123.45")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal dcVoltageO;

    @Schema(description = "直流输出电流, 单位'安'", example = "123.45")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal dcCurrentO;

    @Schema(description = "直流A相输入电压, 单位'伏'", example = "123.45")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal dcVoltageA;

    @Schema(description = "直流B相输入电压, 单位'伏'", example = "123.45")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal dcVoltageB;

    @Schema(description = "直流C相输入电压, 单位'伏'", example = "123.45")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal dcVoltageC;

    @Schema(description = "交流A相电压, 单位'伏'", example = "123.45")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal acVoltageA;

    @Schema(description = "交流A相电流, 单位'安'", example = "123.45")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal acCurrentA;

    @Schema(description = "交流B相电压, 单位'伏'", example = "123.45")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal acVoltageB;

    @Schema(description = "交流B相电流, 单位'安'", example = "123.45")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal acCurrentB;


    @Schema(description = "交流C相电压, 单位'伏'", example = "123.45")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal acVoltageC;

    @Schema(description = "交流C相电流, 单位'安'", example = "123.45")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal acCurrentC;

    @Schema(description = "电池温度, 单位'摄氏度'", example = "35")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer batteryTemp;

    @Schema(description = "桩温度, 单位'摄氏度'", example = "45")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer evseTemp;

    @Schema(description = "枪温度, 单位'摄氏度'", example = "55")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer plugTemp;

    /**
     * 订单(云端)创建时间
     */
    @Schema(description = "订单(云端)创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date createTime;

    @Schema(description = "BMS静态数据")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BmsDto bms;

    @Schema(description = "BMS动态数据")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BmsDynamicDto bmsDynamic;

    @Schema(description = "电池静态数据")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BatteryDto battery;

    @Schema(description = "电池动态数据")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BatteryDynamicDto batteryDynamic;

    /**
     * 费用明细. 仅在正常充电完成时才有.
     */
    @Schema(title = "费用明细", description = "仅在正常充电完成时才有")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<ChargeOrderFeeItem> feeDetail;

    @Schema(description = "订单(云端)更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date updateTime;

    @Schema(description = "订单(云端)结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date cloudStopTime;

    @Schema(description = "分配功率", example= "123")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer assignPower;
}
