package com.cdz360.base.model.es.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "报警参数信息")
public class BmsAlarmVo {

    @Schema(description = "单体电压高，单位：mV")
    @JsonProperty("cvh")
    private BmsAlarmItemVo cellVoltageHigh;

    @Schema(description = "单体电压低，单位：mV")
    @JsonProperty("cvl")
    private BmsAlarmItemVo cellVoltageLow;

    @Schema(description = "单体压差大，单位：mV")
    @JsonProperty("pdl")
    private BmsAlarmItemVo pressureDifferenceLarge;

    @Schema(description = "总压高，单位：V")
    @JsonProperty("tph")
    private BmsAlarmItemVo totalPressureHigh;

    @Schema(description = "总压低，单位：V")
    @JsonProperty("tpl")
    private BmsAlarmItemVo totalPressureLow;

    @Schema(description = "放电电流大，单位：A")
    @JsonProperty("dch")
    private BmsAlarmItemVo dischargeCurrentHigh;

    @Schema(description = "充电电流大，单位：A")
    @JsonProperty("cch")
    private BmsAlarmItemVo chargeCurrentHigh;

    @Schema(description = "绝缘低，单位：KΩ")
    @JsonProperty("il")
    private BmsAlarmItemVo insulationLow;

    @Schema(description = "充电温度高，单位：℃")
    @JsonProperty("cth")
    private BmsAlarmItemVo chargeTempHigh;

    @Schema(description = "充电温度低，单位：℃")
    @JsonProperty("ctl")
    private BmsAlarmItemVo chargeTempLow;

    @Schema(description = "放电温度高，单位：℃")
    @JsonProperty("dth")
    private BmsAlarmItemVo dischargeTempHigh;

    @Schema(description = "放电温度低，单位：℃")
    @JsonProperty("dtl")
    private BmsAlarmItemVo dischargeTempLow;

    @Schema(description = "温差大，单位：℃")
    @JsonProperty("tdl")
    private BmsAlarmItemVo tempDifferenceLarge;

    @Schema(description = "SOC低，单位：%")
    @JsonProperty("sl")
    private BmsAlarmItemVo socLow;

    @Schema(description = "SOC高，单位：%")
    @JsonProperty("sh")
    private BmsAlarmItemVo socHigh;

    @Schema(description = "簇间压差大，单位：V")
    @JsonProperty("cpdl")
    private BmsAlarmItemVo clusterPressureDifferenceLarge;

}
