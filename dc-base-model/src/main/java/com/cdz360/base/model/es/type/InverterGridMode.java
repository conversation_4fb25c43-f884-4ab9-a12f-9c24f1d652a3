package com.cdz360.base.model.es.type;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.experimental.Accessors;

@Schema(description = "逆变器模式")
@Accessors(chain = true)
public enum InverterGridMode {

    OFF_GRID(1, "离网"),
    PARALLEL_GRID(2, "并网"),
    INIT(3, "初始化");

    @JsonValue
    private final int code;
    private final String desc;

    InverterGridMode(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static InverterGridMode valueOf(Object codeIn) {
        if (codeIn == null) {
            return null;
        }
        int code = 0;
        if (codeIn instanceof InverterGridMode) {
            return (InverterGridMode) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (InverterGridMode type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }

}
