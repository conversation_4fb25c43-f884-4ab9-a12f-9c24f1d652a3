package com.cdz360.base.model.base.annotation;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.PARAMETER;


@Retention(RetentionPolicy.RUNTIME)
@Target(value = {FIELD, PARAMETER})
public @interface Cache {
    String name() default "";

}