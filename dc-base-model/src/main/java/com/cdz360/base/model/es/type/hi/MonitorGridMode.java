package com.cdz360.base.model.es.type.hi;

import com.cdz360.base.model.base.type.DcEnum;
import com.cdz360.base.model.charge.type.OrderStartType;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Getter
@Schema(description = "并离网选择")
public enum MonitorGridMode implements DcEnum {

    UNKNOWN(0, "未知"),
    auto_power_on(1, "自动开机模式"),
    Manual_off_grid(2, "手动离网模式"),
    Manual_on_grid(3, "手动并网模式"),
    ;

    @JsonValue
    private final int code;
    private final String desc;

    MonitorGridMode(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static MonitorGridMode valueOf(Object codeIn) {
        if (codeIn == null) {
            return MonitorGridMode.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof OrderStartType) {
            return (MonitorGridMode) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (MonitorGridMode status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return MonitorGridMode.UNKNOWN;
    }

}