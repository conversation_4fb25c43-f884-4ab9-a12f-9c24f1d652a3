package com.cdz360.base.model.charge.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@Schema(description = "BMS静态信息")
public class BmsDto {

    @Schema(description = "国标协议版本. 0,未知; 1,2011版本; 2,2015版本", example = "1")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer gbVer;

    @Schema(description = "BMS通信协议版本号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String protocol;

    @Schema(description = "BMS软件版本号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String swVer;

    @Schema(description = "BMS辅助电压. 12V / 24V", example = "12")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer voltage;

    @Schema(description = "整车最高允许充电总电压. 单位: V", example = "11.22")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal maxVoltage;

    @Schema(description = "整车最高允许充电总电流. 单位: A", example = "11.22")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal maxCurrent;

    @Schema(description = "整车当前电池电压. 单位: V", example = "11.22")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal curVoltage;

    @Schema(description = "最高允许温度. 单位: ℃", example = "37")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer maxTemp;
}
