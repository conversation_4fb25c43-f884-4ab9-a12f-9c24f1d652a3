package com.cdz360.base.model.geo.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;


@Data
@Accessors(chain = true)
@Schema(description = "地理位置信息")
public class GeoInfo implements Serializable {
    private static final long serialVersionUID = 1899469152216431353L;

    @Schema(description = "省份编码")
    private String provinceCode;

    @Schema(description = "省份名称")
    private String provinceName;

    @Schema(description = "城市编码")
    private String cityCode;

    @Schema(description = "城市名称")
    private String cityName;

    @Schema(description = "区县编码")
    private String districtCode;

    @Schema(description = "区县名称")
    private String districtName;


    /**
     * 站点地址
     **/
    @Schema(description = "地址")
    private String address;

    /**
     * 经度
     **/
    @Schema(description = "经度")
    private BigDecimal lng;

    /**
     * 纬度
     **/
    @Schema(description = "纬度")
    private BigDecimal lat;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(title = "时区", description = "如: +8")
    private String timeZone;
}
