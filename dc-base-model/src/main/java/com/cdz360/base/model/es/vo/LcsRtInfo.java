package com.cdz360.base.model.es.vo;

import com.cdz360.base.model.es.type.LcsStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

@Deprecated
@Data
@Accessors(chain = true)
@ToString(callSuper = true)
@JsonInclude(Include.NON_NULL)
@Schema(description = "液冷实时信息")
public class LcsRtInfo {

    private LcsStatus lcsStatus;

    @Schema(description = "TMS_K1接触器状态; 0：open。1：close")
    private Integer tmsK1RelayState;

    @Schema(description = "TMS_K2接触器状态; 0：open。1：close")
    private Integer tmsK2RelayState;

    @Schema(description = "PTC状态")
    private Integer tmsPtcState;

    @Schema(description = "水泵状态")
    private Integer tmsPumpState;

    @Schema(description = "液冷机组故障")
    private String tmsFaultCode;

    @Schema(description = "液冷机组故障等级")
    private String tmsFaultCodeGrade;

    @Schema(description = "1:发生MSD故障报警。0：没有报警")
    private Integer msdStatus;

    @Schema(description = "1:TMS通讯超时。0：无超时")
    private Integer tmsCommunicationStatus;

}
