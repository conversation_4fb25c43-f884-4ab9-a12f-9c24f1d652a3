package com.cdz360.base.model.es.dto;

import com.cdz360.base.model.es.type.EssCfgStrategy;
import com.cdz360.base.model.es.vo.EssInOutStrategyItem;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "充放电时段设置")
@Data
@Accessors(chain = true)
@JsonInclude(Include.NON_NULL)
public class InOutTimeRangeDto {

    @Schema(description = "设备编号", hidden = true)
    @JsonInclude(Include.NON_EMPTY)
    private String dno;

    @Schema(description = "策略类型: SELF_USE(自发自用); PEAK_VALLEY_ARBITRAGE(峰谷套利); "
        + "PEAK_SHARE(削峰填谷); TIMING_CHARGING_DISCHARGING(定时充放电); DISASTER_SPARE(灾备)")
    @JsonInclude(Include.NON_NULL)
    private EssCfgStrategy strategy;

    @Schema(description = "其他时段工作模式")
    private EssCfgStrategy otherStrategy;

    @Schema(description = "重复周期: 当前协议支持周天~周六，使用位来存在对应信息")
    private Integer repeatCycle;

    @Schema(description = "生效开始时间(Unix时间戳)")
    private Long effectiveStartTime;

    @Schema(description = "生效结束时间(Unix时间戳)")
    private Long effectiveEndTime;

    @Schema(description = "最大功率值是否支持时段设置")
    @JsonInclude(Include.NON_NULL)
    private Boolean supportDivision;

    @Schema(description = "充放电时段")
    @JsonInclude(Include.NON_NULL)
    private List<EssInOutStrategyItem> inOutItems;

    @Schema(description = "是否支持多参数下发")
    @JsonInclude(Include.NON_NULL)
    private Boolean multiParamDelivery;

    public float convertOtherStrategy() {
        return EssCfgStrategy.SELF_USE.equals(otherStrategy) ? 0 : 1;
    }
}
