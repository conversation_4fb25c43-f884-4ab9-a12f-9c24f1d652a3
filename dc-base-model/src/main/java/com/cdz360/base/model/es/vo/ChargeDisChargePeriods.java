package com.cdz360.base.model.es.vo;

import com.cdz360.base.model.es.type.ChargeFlowType;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Schema(description = "定时充放电时间段")
@Data
@Accessors(chain = true)
@JsonInclude(Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
public class ChargeDisChargePeriods {

    @Schema(title = "开始时间", description = "[0,2359]")
    @JsonProperty("sct")
    private Integer startTime;
    @Schema(title = "结束时间", description = "[0,2359]")
    @JsonProperty("ect")
    private Integer endTime;
    @Schema(description = "工作模式")
    @JsonProperty("type")
    private ChargeFlowType type;
}
