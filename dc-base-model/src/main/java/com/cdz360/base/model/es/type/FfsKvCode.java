package com.cdz360.base.model.es.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 消防字段编码
 */
@Getter
public enum FfsKvCode implements DcEnum {
    UNKNOWN(150000, "未知"),

    /**
     * 静态信息
     */
//    HW_SN(150000, "硬件序号"),
//    HW_MODEL(150000, "硬件型号"),
//    SW_VER(150000, "软件版本"),

    /**
     * 设备状态信息
     */
    FFS_STATUS(150101, "整机运行状态"),
    SELF_CHECK(150110, "自检"),
    MAIN_SUPPLY(150115, "主电"),
    BACKUP_SUPPLY(150116, "备电"),
    SPILLING_PREPARE(150120, "启动控制"),
    SPILLING_STARTING(150121, "启动喷洒"),
    SPILLING(150122, "喷洒"),

    /**
     * 传感器数据
     */
//    TEMPERATURE_DEH(150200, "控制器内部温度"),


    /**
     * 设定参数
     */
//    CFG_SERIAL_RATE(150301, "通讯速率"),


    /**
     * 告警、故障
     */
    FFS_ALARM(150401, "报警"),
    FFS_WARN(150402, "告警"),
    FFS_ERROR(150403, "错误"),
    FFS_FAULT(150404, "故障"),
 
    ;

    @JsonValue
    final int code;


    final String desc;


    FfsKvCode(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    @JsonCreator
    public static FfsKvCode valueOf(Object codeIn) {
        if (codeIn == null) {
            return FfsKvCode.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof FfsKvCode) {
            return (FfsKvCode) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (FfsKvCode type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return FfsKvCode.UNKNOWN;
    }
}
