package com.cdz360.base.model.base.type;

import lombok.Getter;

/**
 * 扣款账户类型
 */
@Getter
public enum PayAccountType implements DcEnum {

    UNKNOWN(0, "未知"),
    /**
     * 个人现金账户
     */
    PERSONAL(1, "个人账户"),

    /**
     * 企业授信账户
     */
    CREDIT(2, "企业授信账户"),

    /**
     * 商户会员账户
     */
    COMMERCIAL(3, "会员账户"),

    /**
     * 即充即退
     */
    PREPAY(4, "即充即退"),

    CORP(5, "企业账户"),

    E_CNY(6, "数字人民币"),

    WX_CREDIT(7, "微信信用充"),

    ALIPAY_CREDIT(8, "支付宝信用充"),

    CREDIT_CARD(9, "信用卡账户"),

    OTHER(999, "其他");


    private final int code;
    private final String desc;

    PayAccountType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PayAccountType valueOf(Object codeIn) {
        if (codeIn == null) {
            return PayAccountType.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof PayAccountType) {
            return (PayAccountType) codeIn;
        } else if (codeIn instanceof Integer) {
            code = (Integer) codeIn;
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            String str = (String) codeIn;
            if ("UNKNOWN".equalsIgnoreCase(str)) {
                return PayAccountType.UNKNOWN;
            } else if ("PERSONAL".equalsIgnoreCase(str)) {
                return PayAccountType.PERSONAL;
            } else if ("CREDIT".equalsIgnoreCase(str)) {
                return PayAccountType.CREDIT;
            } else if ("COMMERCIAL".equalsIgnoreCase(str)) {
                return PayAccountType.COMMERCIAL;
            } else if ("PREPAY".equalsIgnoreCase(str)) {
                return PayAccountType.PREPAY;
            } else if ("CORP".equalsIgnoreCase(str)) {
                return PayAccountType.CORP;
            } else if ("E_CNY".equalsIgnoreCase(str)) {
                return PayAccountType.E_CNY;
            } else if ("WX_CREDIT".equalsIgnoreCase(str)) {
                return PayAccountType.WX_CREDIT;
            } else if ("ALIPAY_CREDIT".equalsIgnoreCase(str)) {
                return PayAccountType.ALIPAY_CREDIT;
            } else if ("OTHER".equalsIgnoreCase(str)) {
                return PayAccountType.OTHER;
            } else {
                code = Integer.parseInt((String) codeIn);
            }
        }
        for (PayAccountType status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return PayAccountType.UNKNOWN;
    }

}
