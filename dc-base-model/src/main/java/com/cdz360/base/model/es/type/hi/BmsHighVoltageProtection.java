package com.cdz360.base.model.es.type.hi;

import com.cdz360.base.model.base.type.DcEnum;
import com.cdz360.base.model.charge.type.OrderStartType;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * BMS高压保护
 */
@Getter
public enum BmsHighVoltageProtection implements DcEnum {

    UNKNOWN(0, "未知"),
    BATTERY_LOW_VOLTAGE_PROTECTION(1, "电池单体低压保护"),
    BATTERY_HIGH_VOLTAGE_PROTECTION(2, "电池单体高压保护"),
    BATTERY_PACK_LOW_DISCHARGE_PROTECTION(3, "电池组放电低压保护"),
    BATTERY_PACK_HIGH_CHARGE_PROTECTION(4, "电池组充电高压保护"),
    CHARGE_LOW_TEMPERATURE_PROTECTION(5, "充电低温保护"),
    CHARGE_HIGH_TEMPERATURE_PROTECTION(6, "充电高温保护"),
    DISCHARGE_LOW_TEMPERATURE_PROTECTION(7, "放电低温保护"),
    DISCHARGE_HIGH_TEMPERATURE_PROTECTION(8, "放电高温保护"),
    BATTERY_PACK_CHARGE_OVERCURRENT_PROTECTION(9, "电池组充电过流保护"),
    BATTERY_PACK_DISCHARGE_OVERCURRENT_PROTECTION(10, "电池组放电过流保护"),
    BATTERY_MODULE_UNDERVOLTAGE_PROTECTION(11, "电池模块欠压保护"),
    BATTERY_MODULE_OVERVOLTAGE_PROTECTION(12, "电池模块过压保护"),
    SECONDARY_BATTERY_UNDERVOLTAGE_PROTECTION(13, "电池单体二级欠压保护"),


    CHARGE_OVERVOLTAGE_2(101, "充电过压 2"),
    DISCHARGE_UNDERVOLTAGE_2(102, "放电欠压 2"),
    CELL_HIGH_TEMPERATURE_2(103, "电芯高温 2"),
    CELL_LOW_TEMPERATURE_2(104, "电芯低温 2"),
    CHARGE_OVERCURRENT_2(105, "充电过流 2"),
    DISCHARGE_OVERCURRENT_2(106, "放电过流 2"),
    PRECHARGE_FAILURE(107, "预充失效"),
    DC_ERROR(108, "DC 故障"),
    BATTERY_TRIP(109, "电池脱扣"),
    BATTERY_LOCK(110, "电池锁死"),
    DISCHARGE_LOOP_FAILURE(111, "放电回路失效"),
    CHARGE_LOOP_FAILURE(112, "充电回路失效"),
    COMMUNICATION_FAILURE_2(113, "通讯失效 2"),
    CELL_LOW_TEMPERATURE_3(114, "电芯低温 3"),
    DISCHARGE_UNDERVOLTAGE_3(115, "放电欠压 3"),
    CHARGE_OVERVOLTAGE_3(116, "充电过压 3"),
    CHARGE_OVERTEMPERATURE_2(117, "充电过温 2"),
    CHARGE_LOW_TEMPERATURE_2(118, "充电低温 2"),
    DISCHARGE_OVERTEMPERATURE_2(119, "放电过温 2"),
    DISCHARGE_LOW_TEMPERATURE_2(120, "放电低温 2"),
    CELL_TEMPERATURE_DIFFERENCE_2(121, "单体温差大 2"),
    CELL_OVERVOLTAGE_2(122, "单体过压 2"),
    CELL_UNDERVOLTAGE_2(123, "单体欠压 2"),
    CELL_PRESSURE_DIFFERENCE_LARGE_2(124, "单体压差大 2"),


    HIGH_TEMPERATURE_PROTECTION(201, "温度高保护"),
    LOW_TEMPERATURE_PROTECTION(202, "温度低保护"),
    LARGE_TEMPERATURE_DIFFERENCE_PROTECTION(203, "温差大保护"),
    TOTAL_PRESSURE_HIGH_PROTECTION(204, "总压高保护"),
    TOTAL_PRESSURE_LOW_PROTECTION(205, "总压低保护"),
    HIGH_CELL_VOLTAGE_PROTECTION(206, "单体电压高保护"),
    LOW_CELL_VOLTAGE_PROTECTION(207, "单体电压低保护"),
    CELL_PRESSURE_DIFFERENCE_LARGE_PROTECTION(208, "单体压差大保护"),
    HIGH_CHARGING_CURRENT_PROTECTION(209, "充电电流大保护"),
    HIGH_DISCHARGING_CURRENT_PROTECTION(210, "放电电流大保护"),
    LOW_SOC_PROTECTION(212, "SOC低保护"),
    LOW_INSULATION_RESISTANCE_PROTECTION(213, "绝缘阻抗低保护"),
    ;

    @JsonValue
    private final int code;
    private final String desc;

    BmsHighVoltageProtection(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static BmsHighVoltageProtection valueOf(Object codeIn) {
        if (codeIn == null) {
            return BmsHighVoltageProtection.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof OrderStartType) {
            return (BmsHighVoltageProtection) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (BmsHighVoltageProtection status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return BmsHighVoltageProtection.UNKNOWN;
    }

}