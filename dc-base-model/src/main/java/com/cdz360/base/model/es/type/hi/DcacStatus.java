package com.cdz360.base.model.es.type.hi;

import com.cdz360.base.model.charge.type.OrderStartType;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * DCAC状态
 */
@Getter
public enum DcacStatus {

    UNKNOWN(999, "未知"),
    POWER_OFF(0, "关机"),
    POWER_ON(1, "开机"),
    ;

    @JsonValue
    private final int code;
    private final String desc;

    DcacStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static DcacStatus valueOf(Object codeIn) {
        if (codeIn == null) {
            return DcacStatus.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof OrderStartType) {
            return (DcacStatus) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (DcacStatus status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return DcacStatus.UNKNOWN;
    }

}
