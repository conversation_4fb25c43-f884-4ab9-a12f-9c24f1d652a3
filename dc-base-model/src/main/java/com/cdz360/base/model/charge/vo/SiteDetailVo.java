package com.cdz360.base.model.charge.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Schema(description = "场站详情信息")
public class SiteDetailVo extends SiteVo {
    private static final long serialVersionUID = 5480065760092165960L;


}
