package com.cdz360.base.model.es.type;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "ESS设备配置类型")
public enum EssConfigType {

    EMS_PARAM("EMS参数"),
    PCS_PARAM("PCS参数"),
    ACTIVE_REACTIVE_OPTIMIZE("有功无功调优参数"),
    CHARGING_STRATEGY("充电策略"),
    DISCHARGING_STRATEGY("放电策略"),
    TIME_SHARING_ELECTRICITY_PRICE("分时电价");

    private final String desc;

    EssConfigType(String desc) {
        this.desc = desc;
    }
}
