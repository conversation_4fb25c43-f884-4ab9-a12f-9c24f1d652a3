package com.cdz360.base.model.charge.vo;

import com.cdz360.base.model.base.type.ChargePriceCategory;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


@Data
@Accessors(chain = true)
@Schema(description = "充电订单分时费用明细")
public class ChargeOrderFeeItem implements Serializable {

    private static final long serialVersionUID = -4284889285526227632L;
    /**
     * 0, 未知; 1, 尖; 2, 峰; 3, 平; 4, 谷
     */
    @Schema(title = "尖峰平谷标签", example = "2", description = "0, 未知; 1, 尖; 2, 峰; 3, 平; 4, 谷")
    private ChargePriceCategory category;


    /**
     * 分段开始时间
     */
    @Schema(description = "分段开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date startTime;

    /**
     * 分段结束时间
     */
    @Schema(description = "分段结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date stopTime;

    /**
     * 消费电量
     */
    @Schema(description = "消费电量")
    private BigDecimal kwh;

    /**
     * 电费单价
     */
    @Schema(description = "电费单价")
    private BigDecimal elecPrice;
    /**
     * 电费金额
     */
    @Schema(description = "电费金额")
    private BigDecimal elecFee;

    /**
     * 服务费单价
     */
    @Schema(description = "服务费单价")
    private BigDecimal servPrice;
    /**
     * 服务费金额
     */
    @Schema(description = "服务费金额")
    private BigDecimal servFee;

}
