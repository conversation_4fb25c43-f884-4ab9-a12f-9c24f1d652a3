package com.cdz360.base.model.es.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.cdz360.base.model.iot.type.EquipStatus;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 充/放电模式
 */
public enum ChargeFlowType implements DcEnum {

    UNKNOWN(0, "未知"),

    CHARGE(1, "充电"),

    DISCHARGE(2, "放电"),

    IDLE(3, "待机");

    @Getter
    @JsonValue
    final int code;

    @Getter
    final String desc;

    ChargeFlowType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    @JsonCreator
    public static ChargeFlowType valueOf(Object codeIn) {
        if (codeIn == null) {
            return ChargeFlowType.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof ChargeFlowType) {
            return (ChargeFlowType) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (ChargeFlowType type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return ChargeFlowType.UNKNOWN;
    }
}
