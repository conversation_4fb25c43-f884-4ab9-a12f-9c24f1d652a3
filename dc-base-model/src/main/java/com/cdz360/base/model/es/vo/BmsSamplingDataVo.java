package com.cdz360.base.model.es.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "BMS采集数据")
@Data
@Accessors(chain = true)
public class BmsSamplingDataVo {

    @Schema(description = "时间(格式化的时间)", example = "yyyy-MM-dd")
    private String time;

    @Schema(description = "设备编号")
    private String dno;

    @Schema(description = "设备名称")
    private String name;

    @Schema(description = "单体最高温度")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal cellTempMax;

    @Schema(description = "单体最低温度")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal cellTempMin;

}
