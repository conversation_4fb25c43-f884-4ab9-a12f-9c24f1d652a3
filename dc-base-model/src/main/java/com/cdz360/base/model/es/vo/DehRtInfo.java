package com.cdz360.base.model.es.vo;

import com.cdz360.base.model.es.type.DehAlarmCode;
import com.cdz360.base.model.iot.type.EquipStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 除湿器 dehumidifier 实际设备信息
 */
@Data
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class DehRtInfo extends EssBaseRtInfo<EquipStatus, DehAlarmCode> {

//    @Deprecated
//    @Schema(title = "工作模式", description = "true降温,false升温,null未知.默认降温")
//    private Boolean cooling;
//
//    @Deprecated
//    @Schema(title = "温度设定", description = "0～100℃")
//    private Integer expectTemp;
//
//    @Deprecated
//    @Schema(title = "温度回差", description = "0～40℃")
//    private Integer tempBacklash;
//
//    @Deprecated
//    @Schema(title = "湿度设定", description = "0～100RH%")
//    private Integer expectHumidity;
//
//    @Deprecated
//    @Schema(title = "湿度回差", description = "0～40RH%")
//    private Integer humidityBacklash;
//
//    @Deprecated
//    @Schema(title = "通讯机号", description = "1～247")
//    private Integer commNo;
//
//    @Deprecated
//    @Schema(title = "通讯波特率", description = "单位：bps")
//    private Integer commBaud;
//    /**
//     * @deprecated 使用 signalValues 替换
//     */
//    @Deprecated
//    @Schema(title = "控制器内部温度", description = "℃")
//    private BigDecimal innerTemp;
//    /**
//     * @deprecated 使用 signalValues 替换
//     */
//    @Deprecated
//    @Schema(title = "温度控制状态", description = "true开启,false关闭,null未知")
//    private Boolean tempCtlOpen;

    @Schema(title = "湿度控制方式", description = "true自动,false手动,null未知.默认自动")
    private Boolean autoHumidity;
//    /**
//     * @deprecated 使用 signalValues 替换
//     */
//    @Deprecated
//    @Schema(title = "除湿状态", description = "true运行,false关闭,null未知")
//    private Boolean humidityOpen;
//    /**
//     * @deprecated 使用 signalValues 替换
//     */
//    @Deprecated
//    @Schema(title = "当前温度", description = "℃")
//    private BigDecimal currTemp;
//
//    /**
//     * @deprecated 使用 signalValues 替换
//     */
//    @Deprecated
//    @Schema(title = "当前湿度", description = "RH%")
//    private BigDecimal currHumidity;
//
//    @Schema(title = "文本值", description = "文本信息")
//    @JsonInclude(Include.NON_NULL)
//    private List<StringVal> textValues;
//
//    @Schema(title = "各种设定值", description = "设定值")
//    private List<RegisterRwValue> cfgValues;
//
//    @Schema(title = "各种信号量", description = "信号量")
//    private List<SignalVal> signalValues;

    @Schema(title = "告警状态", description = "true有告警,false无告警,null未知")
    private Boolean alarmStatus;

//    @Schema(title = "故障列表")
//    @JsonInclude(Include.NON_NULL)
//    private List<DehAlarmCode> errorList;
}
