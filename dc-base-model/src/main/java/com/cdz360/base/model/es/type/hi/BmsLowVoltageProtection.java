package com.cdz360.base.model.es.type.hi;

import com.cdz360.base.model.base.type.DcEnum;
import com.cdz360.base.model.charge.type.OrderStartType;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * BMS低压保护
 */
@Getter
public enum BmsLowVoltageProtection implements DcEnum {

    UNKNOWN(0, "未知"),
    //    reserve(1, "保留"),
    overVOLTAGE_PROTECTION(2, "过压保护"),
    UNDERVOLTAGE_PROTECTION(3, "欠压保护"),
    Overtemperature_PROTECTION(4, "过温保护"),
    undertemperature_PROTECTION(5, "欠压保护"),
    //    reserve(6, "保留"),
    //    reserve(7, "保留"),
    DISCHARGE_OVERCURRENT_PROTECTION(8, "放电过流保护"),
    CHARGE_OVERCURRENT_PROTECTION(9, "放电过流保护"),
    //    reserve(10, "保留"),
    //    reserve(11, "保留"),
    system_fault(12, "系统故障"),
    ;

    @JsonValue
    private final int code;
    private final String desc;

    BmsLowVoltageProtection(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static BmsLowVoltageProtection valueOf(Object codeIn) {
        if (codeIn == null) {
            return BmsLowVoltageProtection.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof OrderStartType) {
            return (BmsLowVoltageProtection) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (BmsLowVoltageProtection status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return BmsLowVoltageProtection.UNKNOWN;
    }

}