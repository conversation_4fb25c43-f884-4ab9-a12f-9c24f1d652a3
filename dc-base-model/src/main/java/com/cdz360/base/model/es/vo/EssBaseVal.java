package com.cdz360.base.model.es.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class EssBaseVal<T> {

    @Schema(title = "编码", description = "XxxKvCode.code")
    private int code;

    @Schema(title = "寄存器地址", description = "寄存器地址")
    private Integer addr;

    @Schema(title = "寄存器名称", description = "寄存器名称")
    @JsonInclude(Include.NON_EMPTY)
    private String name;

    private T v;

    @JsonInclude(Include.NON_EMPTY)
    private String desc;
}
