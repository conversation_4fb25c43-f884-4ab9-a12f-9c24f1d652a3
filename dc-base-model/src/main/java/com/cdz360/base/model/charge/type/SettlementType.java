package com.cdz360.base.model.charge.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 订单结算类型(细分)
 */
@Getter
public enum SettlementType implements DcEnum {
    UNKNOWN(0, "未知"),

    // 个人账户、企业集团(授信)账户、权益账户余额扣款
    BALANCE(1, "预付费结算"),

    // 即充即退, 支付宝芝麻信用
    GUARANTEE(2, "担保消费结算"),

    // 记账（赊账），在一定周期内统一结算 (挂账/后付费)
    POSTPAID(3, "后付费线下结算"),

    // 互联互通接口控制充电后进行的结算
    PARTNER(4, "外部平台结算"),

    // 数字货币结算
    POSTPAID_E_CNY(5, "后付费线上结算"),

    ;


    @JsonValue
    private final int code;
    private final String desc;

    SettlementType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static SettlementType valueOf(Object codeIn) {
        if (codeIn == null) {
            return SettlementType.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof SettlementType) {
            return (SettlementType) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (SettlementType status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return SettlementType.UNKNOWN;
    }
}