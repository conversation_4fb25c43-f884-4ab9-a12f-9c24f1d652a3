package com.cdz360.base.model.es.dto;

import com.cdz360.base.model.es.type.hi.EssDtuCommunicationWay;
import com.cdz360.base.model.es.type.hi.EssDtuType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "户储DTU通讯设备")
@Data
@Accessors(chain = true)
public class EssDtuDto {

    // 序列号
    @Schema(description = "序列号", example = "E47F23500003")
    private String serialNo;

    // 设备类型
    @Schema(description = "设备类型")
    private EssDtuType essDtuType;

    // 通讯方式
    @Schema(description = "通讯方式")
    private EssDtuCommunicationWay communicationWay;

    // 设备名称
    @Schema(description = "设备名称")
    private String deviceName;

    // 设备型号
    @Schema(description = "设备型号")
    private String deviceModel;

    // 硬件版本
    @Schema(description = "硬件版本")
    private String hardwareVer;

    // 软件版本
    @Schema(description = "软件版本")
    private String softwareVer;

    // ICCID
    @Schema(description = "ICCID")
    private String iccid;

    // ip地址
    @Schema(description = "ip地址")
    private String ip;

    // 当前不需要提供挂载ess信息
}
