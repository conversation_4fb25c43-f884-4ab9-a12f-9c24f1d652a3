package com.cdz360.base.model.es.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 液冷字段编码
 */
@Getter
public enum LiquidKvCode implements DcEnum {
    UNKNOWN(160000, "未知"),

    /**
     * 静态信息
     */
//    HW_SN(160001, "硬件序号"),
//    HW_MODEL(160005, "硬件型号"),
//    SW_VER(160010, "软件版本"),

    /**
     * 设备状态信息
     */
    LIQUID_STATUS(160101, "设备状态"),
    TMS_K1_RELAY_STATUS(160130, "TMS_K1接触器状态"),
    TMS_K2_RELAY_STATUS(160131, "TMS_K2接触器状态"),
    PTC_STATUS(160132, "PTC状态"),
    WATER_PUMP_STATUS(160133, "水泵状态"),
    MSD_STATUS(160134, "MSD状态"),
    TMS_COMM_STATUS(160135, "TMS通讯状态"),

    /**
     * 传感器数据
     */
    WATER_IN_TEMP(160200, "回液温度"),
    WATER_OUT_TEMP(160201, "出液温度"),
    WATER_IN_PRESSURE(160210, "回液压力"),
    WATER_OUT_PRESSURE(160211, "出液压力"),
    TMS_TEMP(160222, "TMS环境温度"),

    /**
     * 设定参数
     */
//    CFG_STOP_RELAY(160301, "断继电器命令"),

    /**
     * 告警、故障
     */
    TMS_FAULT_CODE(160411, "液冷机组故障"),
    TMS_FAULT_GRADE(160415, "液冷机组故障等级"),

    ;

    @JsonValue
    final int code;


    final String desc;


    LiquidKvCode(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    @JsonCreator
    public static LiquidKvCode valueOf(Object codeIn) {
        if (codeIn == null) {
            return LiquidKvCode.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof LiquidKvCode) {
            return (LiquidKvCode) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (LiquidKvCode type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return LiquidKvCode.UNKNOWN;
    }
}
