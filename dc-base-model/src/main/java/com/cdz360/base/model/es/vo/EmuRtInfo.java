package com.cdz360.base.model.es.vo;

import com.cdz360.base.model.es.type.EmuAlarmCode;
import com.cdz360.base.model.iot.type.EquipStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(title = "EMU设备信息")
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class EmuRtInfo extends EssBaseRtInfo<EquipStatus, EmuAlarmCode> {

    @Schema(title = "PCS设备号列表")
    @JsonInclude(Include.NON_NULL)
    private List<String> pcsDnos;

    @Schema(title = "BMS设备号列表")
    @JsonInclude(Include.NON_NULL)
    private List<String> bmsDnos;

    @Schema(title = "液冷设备号列表")
    @JsonInclude(Include.NON_NULL)
    private List<String> liquidDnos;

    @Schema(title = "除湿器设备号列表")
    @JsonInclude(Include.NON_NULL)
    private List<String> dehDnos;

    @Schema(title = "UPS设备号列表")
    @JsonInclude(Include.NON_NULL)
    private List<String> upsDnos;

    @Schema(title = "消防系统设备号列表")
    @JsonInclude(Include.NON_NULL)
    private List<String> ffsDnos;

    @Schema(title = "电表设备号列表")
    @JsonInclude(Include.NON_NULL)
    private List<String> meterDnos;

    @Schema(title = "今日计费数据")
    @JsonInclude(Include.NON_NULL)
    private EmuDailyFeeFull todayFee;


    @Schema(title = "DI数据")
    @JsonInclude(Include.NON_NULL)
    private List<DiVal> diVals;

//    @Schema(title = "EMU的故障告警")
//    @JsonInclude(Include.NON_NULL)
//    private List<EmuAlarmCode> errorList;
}
