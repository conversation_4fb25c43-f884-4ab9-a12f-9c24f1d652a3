package com.cdz360.base.model.es.type.hi;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum HiKvCode implements DcEnum {
    UNKNOWN(200000, "未知"),

    /**
     * 厂家信息（200001~204999）
     */
    HW_SN(200001, "硬件序号"),
    HW_MODEL(200005, "硬件型号"),
    HW_VENDOR(200010, "硬件厂家信息"),
    SW_VER(200015, "软件版本"),
    SCREEN_SW_VER(200020, "屏幕软件版本"),
    PROTOCOL_VER(200021, "协议版本号"),
    DSP1_SW_VER(200022, "DSP1软件版本号"),
    DSP2_SW_VER(200023, "DSP2软件版本号"),
    ARM_SW_VER(200024, "ARM软件版本号"),
    CPLD_VER(200025, "CPLD版本号"),
    AFCI_SW_VER(200026, "AFCI软件版本号"),
    MACHINE_TYPE(200027, "机器类型"),
    MACHINE_CAPACITY(200028, "机器容量"),
    //    DERATING_CAPACITY(200029, "降额容量"),
    ARM_BOOTLOADER_VER(200030, "ARM BootLoader版本号"),
    ARM_CHIP_TYPE(200031, "ARM芯片型号"),

    /**
     * 告警（205000~209999）
     */
    GRID_VOLTAGE_ABNORMAL(205000, "电网电压异常"),
    GRID_FREQUENCY_ABNORMAL(205001, "电网频率异常"),
    GRID_VOLTAGE_ANTITONE(205002, "电网电压反序"),
    GRID_VOLTAGE_OUT_OF_PHASE(205003, "电网电压缺相"),
    OUTPUT_VOLTAGE_ABNORMAL(205004, "输出电压异常"),
    OUTPUT_FREQUENCY_ABNORMAL(205005, "输出频率异常"),
    ZERO_LINE_ABNORMAL(205006, "零线异常"),
    ENV_OVER_TEMPERATURE(205007, "环境温度过高"),
    RADIATOR_OVER_TEMPERATURE(205008, "散热器温度过高"),
    INSULATION_FAULT(205009, "绝缘故障"),
    LEAKAGE_PROTECTION_FAULT(205010, "漏电保护故障"),
    AUXILIARY_POWER_FAULT(205011, "辅助电源故障"),
    FAN_ERROR(205012, "风扇故障"),
    MODEL_CAPACITY_FAULT(205013, "机型容量故障"),
    SURGE_ARRESTER_FAULT(205014, "防雷器异常"),
    ISLANDING_PROTECTION_FAULT(205015, "孤岛保护"),
    BATTERY_NOT_CONNECTED_1(205016, "电池1未接"),
    BATTERY_OVERVOLTAGES_1(205017, "电池1过压"),
    BATTERY_UNDERVOLTAGE_1(205018, "电池1欠压"),
    BATTERY_DISCHARGE_TERMINATES_1(205019, "电池1放电终止"),
    BATTERY_REVERSED_1(205020, "电池1反接"),
    BATTERY_NOT_CONNECTED_2(205021, "电池2未接"),
    BATTERY_OVERVOLTAGES_2(205022, "电池2过压"),
    BATTERY_UNDERVOLTAGE_2(205023, "电池2欠压"),
    BATTERY_DISCHARGE_TERMINATES_2(205024, "电池2放电终止"),
    BATTERY_REVERSED_2(205025, "电池2反接"),
    PV_NOT_CONNECTED_1(205026, "光伏1未接入"),
    PV_OVERVOLTAGES_1(205027, "光伏1过压"),
    PV_CURRENT_EQUALIZATION_ANOMALY_1(205028, "光伏1均流异常"),
    PV_NOT_CONNECTED_2(205029, "光伏2未接入"),
    PV_OVERVOLTAGES_2(205030, "光伏2过压"),
    PV_CURRENT_EQUALIZATION_ANOMALY_2(205031, "光伏2均流异常"),
    DC_BUS_OVERVOLTAGE(205032, "直流母线过压"),
    DC_BUS_UNDERVOLTAGE(205033, "直流母线欠压"),
    DC_BUS_VOLTAGE_IMBALANCE(205034, "直流母线电压不平衡"),
    PV_POWER_TUBE_IS_FAULTY_1(205035, "光伏1功率管故障"),
    PV_POWER_TUBE_IS_FAULTY_2(205036, "光伏2功率管故障"),
    BATTERY_POWER_TUBE_IS_FAULTY_1(205037, "电池1功率管故障"),
    BATTERY_POWER_TUBE_IS_FAULTY_2(205038, "电池2功率管故障"),
    INVERTER_POWER_TUBE_IS_FAULTY(205039, "逆变器功率管故障"),
    SYSTEM_OUTPUT_OVERLOAD(205040, "系统输出过载"),
    INVERTER_OVERLOAD(205041, "逆变器过载"),
    INVERTER_OVERLOAD_TIMEOUT(205042, "逆变器过载超时"),
    BATTERY_OVERLOAD_TIMEOUT_1(205043, "电池1过载超时"),
    BATTERY_OVERLOAD_TIMEOUT_2(205044, "电池2过载超时"),
    INVERTER_SOFT_STARTUP_FAIL(205045, "逆变器软启动失败"),
    BATTERY_SOFT_STARTUP_FAIL_1(205046, "电池1软启动失败"),
    BATTERY_SOFT_STARTUP_FAIL_2(205047, "电池2软启动失败"),
    DSP1_PARAM_SETTINGS_FAULTY(205048, "DSP1参数设置故障"),
    DSP2_PARAM_SETTINGS_FAULTY(205049, "DSP2参数设置故障"),
    DSP_VERSION_INCOMPATIBLE_FAULTY(205050, "DSP版本兼容故障"),
    CPLD_VERSION_INCOMPATIBLE_FAULTY(205051, "CPLD版本兼容故障"),
    CPLD_COMMUNICATION_FAULTY(205052, "CPLD通讯故障"),
    DSP_COMMUNICATION_FAULTY(205053, "DSP通讯故障"),
    OUTPUT_VOLTAGE_DIRECT_CURRENT_EXCEEDS_THE_LIMIT(205054, "输出电压直流量超限"),
    DIRECT_OUTPUT_CURRENT_EXCEEDS_THE_LIMIT(205055, "输出电流直流量超限"),
    RELAY_SELF_TEST_FAILED(205056, "继电器自检不通过"),
    INVERTER_EXCEPTION(205057, "逆变器异常"),
    IMPERFECT_EARTH(205058, "接地不良"),
    PV_SOFT_START_FAILS_1(205059, "光伏1软起动失败"),
    PV_SOFT_START_FAILS_2(205060, "光伏2软起动失败"),
    BALANCE_CIRCUIT_OVERLOAD_TIMEOUT(205061, "平衡电路过载超时"),
    PV_OVERLOAD_TIMEOUT_1(205062, "光伏1过载超时"),
    PV_OVERLOAD_TIMEOUT_2(205063, "光伏2过载超时"),
    PCB_OVERTEMPERATURE(205064, "PCB过温"),
    DC_CONVERTER_OVERTEMPERATURE(205065, "直流变换器过温"),
    BUS_SLOW_OVERVOLTAGE(205066, "母线慢过压"),
    OFF_NETWORK_OUTPUT_VOLTAGE_ABNORMAL(205067, "离网输出电压异常"),
    HARDWARE_BUS_OVERVOLTAGE(205068, "硬件母线过压"),
    HARDWARE_OVERCURRENT(205069, "硬件过流"),
    DC_CONVERTER_OVERVOLTAGE(205070, "直流变换器过压"),
    DC_CONVERTER_HARDWARE_OVERVOLTAGE(205071, "直流变换器硬件过压"),
    DC_CONVERTER_OVERCURRENT(205072, "直流变换器过流"),
    DC_CONVERTER_HARDWARE_OVERCURRENT(205073, "直流变换器硬件过流"),
    DC_CONVERTER_CAVITY_OVERCURRENT(205074, "直流变换器谐振腔过流"),
    PV_REVERSE_CONNECTION_1(205075, "光伏1反接"),
    PV_REVERSE_CONNECTION_2(205076, "光伏2反接"),
    BATTERY_LOW_1(205077, "电池1功率不足"),
    BATTERY_LOW_2(205078, "电池2功率不足"),
    LITHIUM_BATTERY_NOT_CHARGE_1(205079, "锂电池1禁止充电"),
    LITHIUM_BATTERY_NOT_DISCHARGE_1(205080, "锂电池1禁止放电"),
    LITHIUM_BATTERY_NOT_CHARGE_2(205081, "锂电池2禁止充电"),
    LITHIUM_BATTERY_NOT_DISCHARGE_2(205082, "锂电池2禁止放电"),
    LITHIUM_BATTERY_FULL_1(205083, "锂电池1充满"),
    LITHIUM_BATTERY_DISCHARGE_TERMINATION_1(205084, "锂电池1放电终止"),
    LITHIUM_BATTERY_FULL_2(205085, "锂电池2充满"),
    LITHIUM_BATTERY_DISCHARGE_TERMINATION_2(205086, "锂电池2放电终止"),
    LOAD_POWER_OVERLOAD(205087, "负载功率过载"),
    LEAKAGE_SELF_TEST_ABNORMAL(205088, "漏电自检异常"),
    INVERTER_OVERTEMPERATURE_ALARM_GENERATED(205089, "逆变过温告警"),
    INVERTER_OVERHEATED(205090, "逆变器过温"),
    DC_CONVERTER_OVERTEMPERATURE_ALARM(205091, "直流变换器过温告警"),
    PARALLEL_COMMUNICATION_ALARM(205092, "并机通信告警"),
    SYSTEM_RUNS_DERATED(205093, "系统降额运行"),
    OPEN_INVERTER_RELAY(205094, "逆变继电器开路"),
    INVERTER_RELAY_SHORT_CIRCUIT(205095, "逆变继电器短路"),
    PV_ACCESS_MODE_INCORRECT(205096, "光伏接入方式错误告警"),
    PARALLEL_MODULE_MISSING(205097, "并机模块缺失"),
    PARALLEL_MODULE_NUMBER_REPEATED(205098, "并机模块机号重复"),
    PARAM_OF_PARALLEL_MODULES_CONFLICT(205099, "并机模块参数冲突"),
    RESERVED_ALARM_4(205100, "预留告警4"),
    METER_REVERSED(205101, "电表反接"),
    INVERTER_SEAL_PULSE(205102, "逆变器封脉冲"),
    PV_NOT_CONNECTED_3(205103, "光伏3未接入"),
    PV_OVERVOLTAGES_3(205104, "光伏3过压"),
    PV_CURRENT_EQUALIZATION_ANOMALY_3(205105, "光伏3均流异常"),
    PV_NOT_CONNECTED_4(205106, "光伏4未接入"),
    PV_OVERVOLTAGES_4(205107, "光伏4过压"),
    PV_CURRENT_EQUALIZATION_ANOMALY_4(205108, "光伏4均流异常"),
    PV_POWER_TUBE_IS_FAULTY_3(205109, "光伏3功率管故障"),
    PV_POWER_TUBE_IS_FAULTY_4(205110, "光伏4功率管故障"),
    PV_SOFT_START_FAILS_3(205111, "光伏3软起动失败"),
    PV_SOFT_START_FAILS_4(205112, "光伏4软起动失败"),
    PV_OVERLOAD_TIMEOUT_3(205113, "光伏3过载超时"),
    PV_OVERLOAD_TIMEOUT_4(205114, "光伏4过载超时"),
    PV_REVERSE_CONNECTION_3(205115, "光伏3反接"),
    PV_REVERSE_CONNECTION_4(205116, "光伏4反接"),
    OIL_ENGINE_VOLTAGE_ABNORMAL(205117, "油机电压异常"),
    OIL_ENGINE_FREQUENCY_ABNORMAL(205118, "油机频率异常"),
    OIL_ENGINE_VOLTAGE_REVERSE(205119, "油机电压反序"),
    OIL_ENGINE_VOLTAGE_PHASE_FAILURE(205120, "油机电压缺相"),
    LEAD_ACCUMULATOR_TEMPERATURE_ABNORMAL(205121, "铅蓄电池温度异常"),
    BATTERY_CONNECTION_MODE_INCORRECT(205122, "电池接入方式错误"),
    RESERVED_ALARM_5(205123, "预留告警5"),
    BATTERY_1_STANDBY_POWER_DISABLE(205124, "电池1备电禁放"),
    BATTERY_2_STANDBY_POWER_DISABLE(205125, "电池2备电禁放"),
    NETWORK_CURRENT_ABNORMAL(205126, "电网电流异常"),

    /**
     * 逆变（210000~214999）
     */
    INIT_FLAG(210000, "初始化标志"),
    OP_STATUS(210001, "逆变器运行状态"),
    DCAC_STATUS(210002, "DCAC状态"),

    INVERTER_VOLTAGE_A(210301, "逆变A相电压"),
    INVERTER_VOLTAGE_B(210302, "逆变B相电压"),
    INVERTER_VOLTAGE_C(210303, "逆变C相电压"),
    INVERTER_VOLTAGE_LINE_AB(210304, "逆变AB线电压"),
    INVERTER_VOLTAGE_LINE_BC(210305, "逆变BC线电压"),
    INVERTER_VOLTAGE_LINE_CA(210306, "逆变CA线电压"),
    INVERTER_THDU_A(210307, "逆变A相THDU"),
    INVERTER_THDU_B(210308, "逆变B相THDU"),
    INVERTER_THDU_C(210309, "逆变C相THDU"),
    INVERTER_VOLTAGE_FREQUENCY(210310, "逆变电压频率"),
    INVERTER_CURRENT_EFFECTIVE_A(210311, "逆变A相电流有效值"),
    INVERTER_CURRENT_EFFECTIVE_B(210312, "逆变B相电流有效值"),
    INVERTER_CURRENT_EFFECTIVE_C(210313, "逆变C相电流有效值"),
    INVERTER_CURRENT_EFFECTIVE_N(210314, "逆变N相电流有效值"),
    INVERTER_CURRENT_THD_A(210315, "逆变A相电流THD"),
    INVERTER_CURRENT_THD_B(210316, "逆变B相电流THD"),
    INVERTER_CURRENT_THD_C(210317, "逆变C相电流THD"),
    INVERTER_CURRENT_PEAK_RATIO_A(210318, "逆变A相电流峰值比"),
    INVERTER_CURRENT_PEAK_RATIO_B(210319, "逆变B相电流峰值比"),
    INVERTER_CURRENT_PEAK_RATIO_C(210320, "逆变C相电流峰值比"),
    INVERTER_APPARENT_POWER_A(210321, "逆变A相视在功率"),
    INVERTER_APPARENT_POWER_B(210322, "逆变B相视在功率"),
    INVERTER_APPARENT_POWER_C(210323, "逆变C相视在功率"),
    INVERTER_ACTIVE_POWER_A(210324, "逆变A相有功功率"),
    INVERTER_ACTIVE_POWER_B(210325, "逆变B相有功功率"),
    INVERTER_ACTIVE_POWER_C(210326, "逆变C相有功功率"),
    INVERTER_REACTIVE_POWER_A(210327, "逆变A相无功功率"),
    INVERTER_REACTIVE_POWER_B(210328, "逆变B相无功功率"),
    INVERTER_REACTIVE_POWER_C(210329, "逆变C相无功功率"),
    INVERTER_FUNDAMENTAL_POWER_FACTOR_A(210330, "逆变A相基波功率因数"),
    INVERTER_FUNDAMENTAL_POWER_FACTOR_B(210331, "逆变B相基波功率因数"),
    INVERTER_FUNDAMENTAL_POWER_FACTOR_C(210332, "逆变C相基波功率因数"),
    INVERTER_POWER_FACTOR_A(210333, "逆变A相功率因数"),
    INVERTER_POWER_FACTOR_B(210334, "逆变B相功率因数"),
    INVERTER_POWER_FACTOR_C(210335, "逆变C相功率因数"),
    INVERTER_RATE_A(210336, "逆变A相负载率"),
    INVERTER_RATE_B(210337, "逆变B相负载率"),
    INVERTER_RATE_C(210338, "逆变C相负载率"),

    /**
     * 光伏（215000~219999）
     */
    DCDC_STATUS(215000, "DCDC状态"),
    PV_OP_STATUS_1(215001, "光伏1运行状态"),
    PV_OP_STATUS_2(215002, "光伏2运行状态"),
    PV_OP_STATUS_3(215003, "光伏3运行状态"),
    PV_OP_STATUS_4(215004, "光伏4运行状态"),
    KWH_ALL(215005, "总发电量"),
    KWH_TODAY(215006, "当天发电量"),

    PV_VOLTAGE_1(215300, "光伏1电压"),
    PV_CURRENT_1(215301, "光伏1电流"),
    PV_POWER_1(215302, "光伏1功率"),
    PV_LOAD_RATE_1(215303, "光伏1负载率"),
    PV_VOLTAGE_2(215304, "光伏2电压"),
    PV_CURRENT_2(215305, "光伏2电流"),
    PV_POWER_2(215306, "光伏2功率"),
    PV_LOAD_RATE_2(215307, "光伏2负载率"),
    PV_VOLTAGE_3(215308, "光伏3电压"),
    PV_CURRENT_3(215309, "光伏3电流"),
    PV_POWER_3(215310, "光伏3功率"),
    PV_LOAD_RATE_3(215311, "光伏3负载率"),
    PV_VOLTAGE_4(215312, "光伏4电压"),
    PV_CURRENT_4(215313, "光伏4电流"),
    PV_POWER_4(215314, "光伏4功率"),
    PV_LOAD_RATE_4(215315, "光伏4负载率"),

    /**
     * 电池（220000~224999）
     */
    BAT_CHARGE_ALL(220001, "总电池充电量"),
    BAT_DISCHARGE_ALL(220002, "总电池放电量"),
    BAT_CHARGE_TODAY(220003, "当天电池充电量"),
    BAT_DISCHARGE_TODAY(220004, "当天电池放电量"),
    BAT_OP_STATUS_1(220005, "电池1运行状态"),
    BAT_OP_STATUS_2(220006, "电池2运行状态"),

    BAT_VOLTAGE_1(220300, "电池1电压"),
    BAT_CURRENT_1(220301, "电池1电流"),
    BAT_POWER_1(220302, "电池1功率"),
    BAT_SOC_1(220303, "电池1满容量百分比"),
    BAT_FULL_LOAD_SUPPORT_TIME_1(220304, "电池1满载支撑时间"),
    BAT_VOLTAGE_2(220305, "电池2电压"),
    BAT_CURRENT_2(220306, "电池2电流"),
    BAT_POWER_2(220307, "电池2功率"),
    BAT_SOC_2(220308, "电池2满容量百分比"),
    BAT_FULL_LOAD_SUPPORT_TIME_2(220309, "电池2满载支撑时间"),
    BAT_LOAD_RATE_1(220314, "电池1负载率"),
    BAT_LOAD_RATE_2(220315, "电池2负载率"),

    BAT_BOX_1_BMS_OP_STATUS(220400, "电池柜1BMS运行状态"),
    BAT_BOX_1_BASIC_STATUS(220401, "电池柜1基本状态"),
    BAT_BOX_1_CYCLE(220402, "电池柜1循环周期"),
    BAT_BOX_1_FAULT_HIGH_VOLTAGE(220403, "电池柜1故障高压"),
    BAT_BOX_1_ALARM_HIGH_VOLTAGE(220404, "电池柜1告警高压"),
    BAT_BOX_1_PROTECTIVE_HIGH_VOLTAGE(220405, "电池柜1保护高压"),
    BAT_BOX_1_FAULT_LOW_VOLTAGE(220406, "电池柜1故障低压"),
    BAT_BOX_1_ALARM_LOW_VOLTAGE(220407, "电池柜1告警低压"),
    BAT_BOX_1_PROTECTIVE_LOW_VOLTAGE(220408, "电池柜1保护低压"),
    BAT_BOX_2_BMS_OP_STATUS(220409, "电池柜2BMS运行状态"),
    BAT_BOX_2_BASIC_STATUS(220410, "电池柜2基本状态"),
    BAT_BOX_2_CYCLE(220411, "电池柜2循环周期"),
    BAT_BOX_2_FAULT_HIGH_VOLTAGE(220412, "电池柜2故障高压"),
    BAT_BOX_2_ALARM_HIGH_VOLTAGE(220413, "电池柜2告警高压"),
    BAT_BOX_2_PROTECTIVE_HIGH_VOLTAGE(220414, "电池柜2保护高压"),
    BAT_BOX_2_FAULT_LOW_VOLTAGE(220415, "电池柜2故障低压"),
    BAT_BOX_2_ALARM_LOW_VOLTAGE(220416, "电池柜2告警低压"),
    BAT_BOX_2_PROTECTIVE_LOW_VOLTAGE(220417, "电池柜2保护低压"),
    BAT_BOX_1_TOTAL_CHARGE_ENERGY(220418, "电池柜1充电总电能"),
    BAT_BOX_1_TOTAL_DISCHARGE_ENERGY(220419, "电池柜1放电总电能"),
    BAT_BOX_1_TOTAL_BATTERY_VOLTAGE(220420, "电池柜1电池组总电压"),
    BAT_BOX_1_TOTAL_BATTERY_CURRENT(220421, "电池柜1电池组总电流"),
    BAT_BOX_1_TEMPERATURE(220422, "电池柜1温度"),
    BAT_BOX_1_SOC(220423, "电池柜1SOC"),
    BAT_BOX_1_SOH(220424, "电池柜1SOH"),
    BAT_BOX_1_MAX_BAT_VOLTAGE(220425, "电池柜1最高单体电池电压"),
    BAT_BOX_1_MIN_BAT_VOLTAGE(220426, "电池柜1最低单体电池电压"),
    BAT_BOX_1_MAX_BAT_VOLTAGE_NUM(220427, "电池柜1最高单体电池电压编号"),
    BAT_BOX_1_MIN_BAT_VOLTAGE_NUM(220428, "电池柜1最低单体电池电压编号"),
    BAT_BOX_1_MAX_BAT_TEMPERATURE(220429, "电池柜1最高单体电池温度"),
    BAT_BOX_1_MIN_BAT_TEMPERATURE(220430, "电池柜1最低单体电池温度"),
    BAT_BOX_1_MAX_BAT_TEMPERATURE_NUM(220431, "电池柜1最高单体电池温度编号"),
    BAT_BOX_1_MIN_BAT_TEMPERATURE_NUM(220432, "电池柜1最低单体电池温度编号"),
    BAT_BOX_1_CHARGING_CURRENT_LIMITING(220433, "电池柜1充电电流限幅"),
    BAT_BOX_1_DISCHARGING_CURRENT_LIMITING(220434, "电池柜1放电电流限幅"),
    BAT_BOX_1_CHARGING_VOLTAGE_LIMITING(220435, "电池柜1充电电压限幅"),
    BAT_BOX_1_DISCHARGING_VOLTAGE_LIMITING(220436, "电池柜1放电电压限幅"),
    BAT_BOX_1_CELL_VOLTAGE_1(220437, "电池柜1单体电池电压1"),
    BAT_BOX_1_CELL_VOLTAGE_2(220438, "电池柜1单体电池电压2"),
    BAT_BOX_1_CELL_VOLTAGE_3(220439, "电池柜1单体电池电压3"),
    BAT_BOX_1_CELL_VOLTAGE_4(220440, "电池柜1单体电池电压4"),
    BAT_BOX_1_CELL_VOLTAGE_5(220441, "电池柜1单体电池电压5"),
    BAT_BOX_1_CELL_VOLTAGE_6(220442, "电池柜1单体电池电压6"),
    BAT_BOX_1_CELL_VOLTAGE_7(220443, "电池柜1单体电池电压7"),
    BAT_BOX_1_CELL_VOLTAGE_8(220444, "电池柜1单体电池电压8"),
    BAT_BOX_1_CELL_VOLTAGE_9(220445, "电池柜1单体电池电压9"),
    BAT_BOX_1_CELL_VOLTAGE_10(220446, "电池柜1单体电池电压10"),
    BAT_BOX_1_CELL_VOLTAGE_11(220447, "电池柜1单体电池电压11"),
    BAT_BOX_1_CELL_VOLTAGE_12(220448, "电池柜1单体电池电压12"),
    BAT_BOX_1_CELL_VOLTAGE_13(220449, "电池柜1单体电池电压13"),
    BAT_BOX_1_CELL_VOLTAGE_14(220450, "电池柜1单体电池电压14"),
    BAT_BOX_1_CELL_VOLTAGE_15(220451, "电池柜1单体电池电压15"),
    BAT_BOX_1_CELL_VOLTAGE_16(220452, "电池柜1单体电池电压16"),
    BAT_BOX_1_CELL_TEMPERATURE_1(220453, "电池柜1单体电池温度1"),
    BAT_BOX_1_CELL_TEMPERATURE_2(220454, "电池柜1单体电池温度2"),
    BAT_BOX_1_CELL_TEMPERATURE_3(220455, "电池柜1单体电池温度3"),
    BAT_BOX_1_CELL_TEMPERATURE_4(220456, "电池柜1单体电池温度4"),
    BAT_BOX_1_MOS_TEMPERATURE(220457, "电池柜1MOS温度"),
    BAT_BOX_1_ENV_TEMPERATURE(220458, "电池柜1环境温度"),
    BAT_BOX_2_TOTAL_CHARGE_ENERGY(220459, "电池柜2充电总电能"),
    BAT_BOX_2_TOTAL_DISCHARGE_ENERGY(220460, "电池柜2放电总电能"),
    BAT_BOX_2_TOTAL_BATTERY_VOLTAGE(220461, "电池柜2电池组总电压"),
    BAT_BOX_2_TOTAL_BATTERY_CURRENT(220462, "电池柜2电池组总电流"),
    BAT_BOX_2_TEMPERATURE(220463, "电池柜2温度"),
    BAT_BOX_2_SOC(220464, "电池柜2SOC"),
    BAT_BOX_2_SOH(220465, "电池柜2SOH"),
    BAT_BOX_2_MAX_BAT_VOLTAGE(220466, "电池柜2最高单体电池电压"),
    BAT_BOX_2_MIN_BAT_VOLTAGE(220467, "电池柜2最低单体电池电压"),
    BAT_BOX_2_MAX_BAT_VOLTAGE_NUM(220468, "电池柜2最高单体电池电压编号"),
    BAT_BOX_2_MIN_BAT_VOLTAGE_NUM(220469, "电池柜2最低单体电池电压编号"),
    BAT_BOX_2_MAX_BAT_TEMPERATURE(220470, "电池柜2最高单体电池温度"),
    BAT_BOX_2_MIN_BAT_TEMPERATURE(220471, "电池柜2最低单体电池温度"),
    BAT_BOX_2_MAX_BAT_TEMPERATURE_NUM(220472, "电池柜2最高单体电池温度编号"),
    BAT_BOX_2_MIN_BAT_TEMPERATURE_NUM(220473, "电池柜2最低单体电池温度编号"),
    BAT_BOX_2_CHARGING_CURRENT_LIMITING(220474, "电池柜2充电电流限幅"),
    BAT_BOX_2_DISCHARGING_CURRENT_LIMITING(220475, "电池柜2放电电流限幅"),
    BAT_BOX_2_CHARGING_VOLTAGE_LIMITING(220476, "电池柜2充电电压限幅"),
    BAT_BOX_2_DISCHARGING_VOLTAGE_LIMITING(220477, "电池柜2放电电压限幅"),
    BAT_BOX_2_CELL_VOLTAGE_1(220478, "电池柜2单体电池电压1"),
    BAT_BOX_2_CELL_VOLTAGE_2(220479, "电池柜2单体电池电压2"),
    BAT_BOX_2_CELL_VOLTAGE_3(220480, "电池柜2单体电池电压3"),
    BAT_BOX_2_CELL_VOLTAGE_4(220481, "电池柜2单体电池电压4"),
    BAT_BOX_2_CELL_VOLTAGE_5(220482, "电池柜2单体电池电压5"),
    BAT_BOX_2_CELL_VOLTAGE_6(220483, "电池柜2单体电池电压6"),
    BAT_BOX_2_CELL_VOLTAGE_7(220484, "电池柜2单体电池电压7"),
    BAT_BOX_2_CELL_VOLTAGE_8(220485, "电池柜2单体电池电压8"),
    BAT_BOX_2_CELL_VOLTAGE_9(220486, "电池柜2单体电池电压9"),
    BAT_BOX_2_CELL_VOLTAGE_10(220487, "电池柜2单体电池电压10"),
    BAT_BOX_2_CELL_VOLTAGE_11(220488, "电池柜2单体电池电压11"),
    BAT_BOX_2_CELL_VOLTAGE_12(220489, "电池柜2单体电池电压12"),
    BAT_BOX_2_CELL_VOLTAGE_13(220490, "电池柜2单体电池电压13"),
    BAT_BOX_2_CELL_VOLTAGE_14(220491, "电池柜2单体电池电压14"),
    BAT_BOX_2_CELL_VOLTAGE_15(220492, "电池柜2单体电池电压15"),
    BAT_BOX_2_CELL_VOLTAGE_16(220493, "电池柜2单体电池电压16"),
    BAT_BOX_2_CELL_TEMPERATURE_1(220494, "电池柜2单体电池温度1"),
    BAT_BOX_2_CELL_TEMPERATURE_2(220495, "电池柜2单体电池温度2"),
    BAT_BOX_2_CELL_TEMPERATURE_3(220496, "电池柜2单体电池温度3"),
    BAT_BOX_2_CELL_TEMPERATURE_4(220497, "电池柜2单体电池温度4"),
    BAT_BOX_2_MOS_TEMPERATURE(220498, "电池柜2MOS温度"),
    BAT_BOX_2_ENV_TEMPERATURE(220499, "电池柜2环境温度"),

    BMS_1_PROTOCOL(220600, "BMS1协议"),
    BMS_1_SW_VER(220601, "BMS1软件版本"),
    BMS_2_PROTOCOL(220602, "BMS2协议"),
    BMS_2_SW_VER(220603, "BMS2软件版本"),
    BMS_1_FIRST_ACTIVATION_TIME(220604, "BMS1首次激活时间"),
    BMS_1_SN_1(220605, "BMS1序列号1"),
    BMS_1_SN_2(220606, "BMS1序列号2"),
    BMS_1_SN_3(220607, "BMS1序列号3"),
    BMS_1_SN_4(220608, "BMS1序列号4"),
    BMS_1_HW_VER_1(220609, "BMS1硬件版本1"),
    BMS_1_HW_VER_2(220610, "BMS1硬件版本2"),
    BMS_1_SW_VER_1(220611, "BMS1软件版本1"),
    BMS_1_SW_VER_2(220612, "BMS1软件版本2"),
    BMS_1_SW_VER_3(220613, "BMS1软件版本3"),
    BMS_1_SW_VER_4(220614, "BMS1软件版本4"),
    BMS_1_SW_VER_5(220615, "BMS1软件版本5"),
    BMS_1_SW_VER_6(220616, "BMS1软件版本6"),
    //    BMS_RESERVE_1(220617, "预留"),
    //    BMS_RESERVE_2(220618, "预留"),
    //    BMS_RESERVE_3(220619, "预留"),
    BMS_2_FIRST_ACTIVATION_TIME(220620, "BMS2首次激活时间"),
    BMS_2_SN_1(220621, "BMS2序列号1"),
    BMS_2_SN_2(220622, "BMS2序列号2"),
    BMS_2_SN_3(220623, "BMS2序列号3"),
    BMS_2_SN_4(220624, "BMS2序列号4"),
    BMS_2_HW_VER_1(220625, "BMS2硬件版本1"),
    BMS_2_HW_VER_2(220626, "BMS2硬件版本2"),
    BMS_2_SW_VER_1(220627, "BMS2软件版本1"),
    BMS_2_SW_VER_2(220628, "BMS2软件版本2"),
    BMS_2_SW_VER_3(220629, "BMS2软件版本3"),
    BMS_2_SW_VER_4(220630, "BMS2软件版本4"),
    BMS_2_SW_VER_5(220631, "BMS2软件版本5"),
    BMS_2_SW_VER_6(220632, "BMS2软件版本6"),

    /**
     * 电网（225000~229999）
     */
    DRED_STATUS(225000, "DRED状态"),
    PURCHASE_ELEC_ALL(225001, "总购电量"),
    FEED_ELEC_ALL(225002, "总馈电量"),
    PURCHASE_ELEC_TODAY(225003, "当天购电量"),
    FEED_ELEC_TODAY(225004, "当天馈电量"),

    GRID_VOLTAGE_A(225300, "电网电压A相"),
    GRID_VOLTAGE_B(225301, "电网电压B相"),
    GRID_VOLTAGE_C(225302, "电网电压C相"),
    GRID_VOLTAGE_AB(225303, "电网AB线电压"),
    GRID_VOLTAGE_BC(225304, "电网AB线电压"),
    GRID_VOLTAGE_CA(225305, "电网CA线电压"),
    GRID_THDU_A(225306, "电网A相THDU"),
    GRID_THDU_B(225307, "电网B相THDU"),
    GRID_THDU_C(225308, "电网C相THDU"),
    GRID_VOLTAGE_FREQUENCY(225309, "电网电压频率"),
    GRID_CURRENT_EFFECTIVE_A(225310, "电网A相电流有效值"),
    GRID_CURRENT_EFFECTIVE_B(225311, "电网B相电流有效值"),
    GRID_CURRENT_EFFECTIVE_C(225312, "电网C相电流有效值"),
    GRID_CURRENT_EFFECTIVE_N(225313, "电网N相电流有效值"),
    GRID_CURRENT_THD_A(225314, "电网A相电流THD"),
    GRID_CURRENT_THD_B(225315, "电网B相电流THD"),
    GRID_CURRENT_THD_C(225316, "电网C相电流THD"),
    GRID_CURRENT_PEAK_RATIO_A(225317, "电网A相电流峰值比"),
    GRID_CURRENT_PEAK_RATIO_B(225318, "电网B相电流峰值比"),
    GRID_CURRENT_PEAK_RATIO_C(225319, "电网C相电流峰值比"),
    GRID_APPARENT_POWER_A(225320, "电网A相视在功率"),
    GRID_APPARENT_POWER_B(225321, "电网B相视在功率"),
    GRID_APPARENT_POWER_C(225322, "电网C相视在功率"),
    GRID_ACTIVE_POWER_A(225323, "电网A相有功功率"),
    GRID_ACTIVE_POWER_B(225324, "电网B相有功功率"),
    GRID_ACTIVE_POWER_C(225325, "电网C相有功功率"),
    GRID_REACTIVE_POWER_A(225326, "电网A相无功功率"),
    GRID_REACTIVE_POWER_B(225327, "电网B相无功功率"),
    GRID_REACTIVE_POWER_C(225328, "电网C相无功功率"),
    GRID_FUNDAMENTAL_POWER_FACTOR_A(225329, "电网A相基波功率因数"),
    GRID_FUNDAMENTAL_POWER_FACTOR_B(225330, "电网B相基波功率因数"),
    GRID_FUNDAMENTAL_POWER_FACTOR_C(225331, "电网C相基波功率因数"),
    GRID_POWER_FACTOR_A(225332, "电网A相功率因数"),
    GRID_POWER_FACTOR_B(225333, "电网B相功率因数"),
    GRID_POWER_FACTOR_C(225334, "电网C相功率因数"),

    /**
     * 负载（230000~234999）
     */
    LOAD_KWH_ALL(230000, "总负载用电量"),
    LOAD_KWH_TODAY(230001, "当天负载用电量"),
    IMPORTANT_LOAD_TOTAL_POWER(230006, "重要负载总功率"),
    COMMON_LOAD_TOTAL_POWER(230007, "一般负载总功率"),

    LOAD_VOLTAGE_A(230300, "负载A相电压"),
    LOAD_VOLTAGE_B(230301, "负载B相电压"),
    LOAD_VOLTAGE_C(230302, "负载C相电压"),
    LOAD_VOLTAGE_AB(230303, "负载AB线电压"),
    LOAD_VOLTAGE_BC(230304, "负载BC线电压"),
    LOAD_VOLTAGE_CA(230305, "负载CA线电压"),
    LOAD_THDU_A(230306, "负载A相THDU"),
    LOAD_THDU_B(230307, "负载B相THDU"),
    LOAD_THDU_C(230308, "负载C相THDU"),
    LOAD_VOLTAGE_FREQUENCY(230309, "负载电压频率"),
    LOAD_CURRENT_EFFECTIVE_A(230310, "负载A相电流有效值"),
    LOAD_CURRENT_EFFECTIVE_B(230311, "负载B相电流有效值"),
    LOAD_CURRENT_EFFECTIVE_C(230312, "负载C相电流有效值"),
    LOAD_CURRENT_EFFECTIVE_N(230313, "负载N相电流有效值"),
    LOAD_CURRENT_THD_A(230314, "负载A相电流THD"),
    LOAD_CURRENT_THD_B(230315, "负载B相电流THD"),
    LOAD_CURRENT_THD_C(230316, "负载C相电流THD"),
    LOAD_CURRENT_PEAK_RATIO_A(230317, "负载A相电流峰值比"),
    LOAD_CURRENT_PEAK_RATIO_B(230318, "负载B相电流峰值比"),
    LOAD_CURRENT_PEAK_RATIO_C(230319, "负载C相电流峰值比"),
    LOAD_APPARENT_POWER_A(230320, "负载A相视在功率"),
    LOAD_APPARENT_POWER_B(230321, "负载B相视在功率"),
    LOAD_APPARENT_POWER_C(230322, "负载C相视在功率"),
    LOAD_ACTIVE_POWER_A(230323, "负载A相有功功率"),
    LOAD_ACTIVE_POWER_B(230324, "负载B相有功功率"),
    LOAD_ACTIVE_POWER_C(230325, "负载C相有功功率"),
    LOAD_REACTIVE_POWER_A(230326, "负载A相无功功率"),
    LOAD_REACTIVE_POWER_B(230327, "负载B相无功功率"),
    LOAD_REACTIVE_POWER_C(230328, "负载C相无功功率"),
    LOAD_FUNDAMENTAL_POWER_FACTOR_A(230329, "负载A相基波功率因数"),
    LOAD_FUNDAMENTAL_POWER_FACTOR_B(230330, "负载B相基波功率因数"),
    LOAD_FUNDAMENTAL_POWER_FACTOR_C(230331, "负载C相基波功率因数"),
    LOAD_POWER_FACTOR_A(230332, "负载A相功率因数"),
    LOAD_POWER_FACTOR_B(230333, "负载B相功率因数"),
    LOAD_POWER_FACTOR_C(230334, "负载C相功率因数"),
    LOAD_RATE_A(230335, "负载A相负载率"),
    LOAD_RATE_B(230336, "负载B相负载率"),
    LOAD_RATE_C(230337, "负载C相负载率"),

    /**
     * 电表（235000~239999）
     */
    METER_OP_STATUS(235000, "电表运行状态"),
    //    meter_type(235001, "电表型号"),
    METER_SW_VER(235002, "电表软件版本"),

    METER_VOLTAGE_A(235300, "电表A相电压"),
    METER_VOLTAGE_B(235301, "电表B相电压"),
    METER_VOLTAGE_C(235302, "电表C相电压"),
    METER_CURRENT_A(235303, "电表A相电流"),
    METER_CURRENT_B(235304, "电表B相电流"),
    METER_CURRENT_C(235305, "电表C相电流"),
    METER_APPARENT_POWER_A(235306, "电表A相视在功率"),
    METER_APPARENT_POWER_B(235307, "电表B相视在功率"),
    METER_APPARENT_POWER_C(235308, "电表C相视在功率"),
    METER_ACTIVE_POWER_A(235309, "电表A相有功功率"),
    METER_ACTIVE_POWER_B(235310, "电表B相有功功率"),
    METER_ACTIVE_POWER_C(235311, "电表C相有功功率"),
    METER_REACTIVE_POWER_A(235312, "电表A相无功功率"),
    METER_REACTIVE_POWER_B(235313, "电表B相无功功率"),
    METER_REACTIVE_POWER_C(235314, "电表C相无功功率"),
    METER_POWER_FACTOR_A(235315, "电表A相功率因数"),
    METER_POWER_FACTOR_B(235316, "电表B相功率因数"),
    METER_POWER_FACTOR_C(235317, "电表C相功率因数"),
    TOTAL_ACTIVE_POWER(235318, "三相有功总功率"),
    POSITIVE_ACTIVE_ENERGY(235319, "正向有功电能"),
    NEGATIVE_ACTIVE_ENERGY(235320, "反向有功电能"),
    POSITIVE_REACTIVE_ENERGY(235321, "正向无功电能"),
    NEGATIVE_REACTIVE_ENERGY(235322, "反向无功电能"),
    //    METER_RESERVE(235323, "预留"),
    TOTAL_FUNDAMENTAL_ACTIVE_POWER(235324, "基波总有功功率"),
    TOTAL_FUNDAMENTAL_REACTIVE_POWER(235325, "基波总无功功率"),
    FUNDAMENTAL_ACTIVE_POWER_A(235326, "A相基波有功功率"),
    FUNDAMENTAL_ACTIVE_POWER_B(235327, "B相基波有功功率"),
    FUNDAMENTAL_ACTIVE_POWER_C(235328, "C相基波有功功率"),
    FUNDAMENTAL_REACTIVE_POWER_A(235329, "A相基波无功功率"),
    FUNDAMENTAL_REACTIVE_POWER_B(235330, "B相基波无功功率"),
    FUNDAMENTAL_REACTIVE_POWER_C(235331, "C相基波无功功率"),

    /**
     * 油机（240000~244999）
     */
//    Oil_engine(240000, "状态"),

    OIL_ENGINE_VOLTAGE_A(240300, "油机A相电压"),
    OIL_ENGINE_VOLTAGE_B(240301, "油机B相电压"),
    OIL_ENGINE_VOLTAGE_C(240302, "油机C相电压"),
    OIL_ENGINE_VOLTAGE_AB(240303, "油机AB线电压"),
    OIL_ENGINE_VOLTAGE_BC(240304, "油机BC线电压"),
    OIL_ENGINE_VOLTAGE_CA(240305, "油机CA线电压"),
    OIL_ENGINE_THDU_A(240306, "油机A相THDU"),
    OIL_ENGINE_THDU_B(240307, "油机B相THDU"),
    OIL_ENGINE_THDU_C(240308, "油机C相THDU"),
    OIL_ENGINE_VOLTAGE_FREQUENCY(240309, "油机电压频率"),
    OIL_ENGINE_CURRENT_EFFECTIVE_A(240310, "油机A相电流有效值"),
    OIL_ENGINE_CURRENT_EFFECTIVE_B(240311, "油机B相电流有效值"),
    OIL_ENGINE_CURRENT_EFFECTIVE_C(240312, "油机C相电流有效值"),
    OIL_ENGINE_CURRENT_EFFECTIVE_N(240313, "油机N相电流有效值"),
    OIL_ENGINE_CURRENT_THD_A(240314, "油机A相电流THD"),
    OIL_ENGINE_CURRENT_THD_B(240315, "油机B相电流THD"),
    OIL_ENGINE_CURRENT_THD_C(240316, "油机C相电流THD"),
    OIL_ENGINE_CURRENT_PEAK_RATIO_A(240317, "油机A相电流峰值比"),
    OIL_ENGINE_CURRENT_PEAK_RATIO_B(240318, "油机B相电流峰值比"),
    OIL_ENGINE_CURRENT_PEAK_RATIO_C(240319, "油机C相电流峰值比"),
    OIL_ENGINE_APPARENT_POWER_A(240320, "油机A相视在功率"),
    OIL_ENGINE_APPARENT_POWER_B(240321, "油机B相视在功率"),
    OIL_ENGINE_APPARENT_POWER_C(240322, "油机C相视在功率"),
    OIL_ENGINE_ACTIVE_POWER_A(240323, "油机A相有功功率"),
    OIL_ENGINE_ACTIVE_POWER_B(240324, "油机B相有功功率"),
    OIL_ENGINE_ACTIVE_POWER_C(240325, "油机C相有功功率"),
    OIL_ENGINE_REACTIVE_POWER_A(240326, "油机A相无功功率"),
    OIL_ENGINE_REACTIVE_POWER_B(240327, "油机B相无功功率"),
    OIL_ENGINE_REACTIVE_POWER_C(240328, "油机C相无功功率"),
    OIL_ENGINE_FUNDAMENTAL_POWER_FACTOR_A(240329, "油机A相基波功率因数"),
    OIL_ENGINE_FUNDAMENTAL_POWER_FACTOR_B(240330, "油机B相基波功率因数"),
    OIL_ENGINE_FUNDAMENTAL_POWER_FACTOR_C(240331, "油机C相基波功率因数"),
    OIL_ENGINE_POWER_FACTOR_A(240332, "油机A相功率因数"),
    OIL_ENGINE_POWER_FACTOR_B(240333, "油机B相功率因数"),
    OIL_ENGINE_POWER_FACTOR_C(240334, "油机C相功率因数"),
    OIL_ENGINE_RATE_A(240335, "油机A相负载率"),
    OIL_ENGINE_RATE_B(240336, "油机B相负载率"),
    OIL_ENGINE_RATE_C(240337, "油机C相负载率"),

    /**
     * 设置（245000~284999）
     */
    DRED_INTERFACE(245000, "DRED接口"),
    REVERSE_FLOW_PREVENTION(245001, "防逆流使能"),
    REVERSE_FLOW_PREVENTION_POWER(245002, "防逆流功率"),
    PHASE_LINE_SYSTEM(245003, "三相三线/三相四线制"),
    INPUT_VOLTAGE_LEVEL(245004, "输入电压等级"),
    INPUT_FREQUENCY_LEVEL(245005, "输入频率等级"),
    WORK_MODE(245006, "工作模式"),
    OFF_GRID_SWITCHING_MODE(245007, "离并网切换方式"),
    GRID_CONNECTED_IMBALANCE_COMPENSATION_ENABLED(245008, "并网不平衡补偿使能"),
    TEMPERATURE_DERATING(245009, "温度降额"),
    HIGH_VOLTAGE_CROSSING(245010, "高电压穿越"),
    LOW_VOLTAGE_CROSSING(245011, "低电压穿越"),
    FAN_GEAR(245012, "风扇档位"),
    FIXED_POWER_FACTOR(245013, "固定功率因数"),
    SELF_AGING_TYPE(245014, "自老化类型"),
    COMPENSATION_RATE(245015, "补偿率"),
    TEST_MODE(245016, "测试模式"),
    SET_CT_RATIO(245017, "设定CT变比"),
    DEBUG_VARIABLE_ADDRESS_1(245018, "调试变量地址1"),
    DEBUG_VARIABLE_ADDRESS_2(245019, "调试变量地址2"),
    DEBUG_VARIABLE_ADDRESS_3(245020, "调试变量地址3"),
    DEBUG_VARIABLE_ADDRESS_4(245021, "调试变量地址4"),
    DEBUG_VARIABLE_ADDRESS_5(245022, "调试变量地址5"),
    DEBUG_VARIABLE_ADDRESS_6(245023, "调试变量地址6"),
    BAT_ACCESS_MODE(245024, "电池接入方式"),
    MATER_ACCESS_ENABLE(245025, "电表接入使能"),
    BAT_1_ENABLE(245026, "电池1启用"),
    BAT_2_ENABLE(245027, "电池2启用"),
    PV_1_ENABLE(245028, "光伏1启用"),
    PV_2_ENABLE(245029, "光伏2启用"),
    BAT_TYPE(245030, "电池类型"),
    BAT_1_CAPACITY(245031, "电池1容量"),
    BAT_1_MAX_CHARGE_CURRENT(245032, "电池1最大充电电流"),
    BAT_1_MAX_DISCHARGE_CURRENT(245033, "电池1最大放电电流"),
    BAT_1_RATED_VOLTAGE(245034, "电池1额定电压"),
    BAT_1_MIN_SOC(245035, "电池1最小SOC"),
    BAT_1_OVERVOLTAGE_SET_VALUE(245036, "电池1过压设定值"),
    ACTIVE_CHARGE_DISCHARGE_SWITCH(245037, "主动充/放电"),
    ACTIVE_CHARGE_DISCHARGE_POWER(245038, "主动充放电功率"),
    SW_OSCILLOSCOPE_ENABLE(245039, "软件示波器使能"),
    YEAR(245040, "年"),
    MONTH(245041, "月"),
    DAY(245042, "日"),
    HOUR(245043, "时"),
    MINUTE(245044, "分"),
    SECOND(245045, "秒"),
    ISLANDING_PROTECTION(245046, "孤岛保护"),
    PV_ACCESS_MODE(245047, "光伏接入方式"),
    OUTPUT_VOLTAGE_ADJUSTMENT_FACTOR(245048, "输出电压调整系数"),
    BAT_1_UNDERVOLTAGE_SET_VALUE(245049, "电池1欠压设定值"),
    INVERTER_POWER_LIMIT(245050, "逆变器功率限制"),
    OVERFREQUENCY_LOAD_REDUCTION_UNDERFREQUENCY_LOAD_STANDARD(245051, "过频降载欠频加载标准"),
    OVERFREQUENCY_LOAD_DROP_STARTING_POINT(245052, "过频降载起点"),
    OVERFREQUENCY_LOAD_DROP_SLOPE(245053, "过频降载斜率"),
    OVERFREQUENCY_LOAD_DROP_RECOVERY_POINT(245054, "过频降载恢复点"),
    OVERFREQUENCY_LOAD_DROP_RECOVERY_TIME(245055, "过频降载恢复时间"),
    UNDERFREQUENCY_LOAD_STARTING_POINT(245056, "欠频加载起点"),
    UNDERFREQUENCY_LOAD_DROP_SLOPE(245057, "欠频加载斜率"),
    UNDERFREQUENCY_LOAD_DROP_RECOVERY_POINT(245058, "欠频加载恢复点"),
    UNDERFREQUENCY_LOAD_DROP_RECOVERY_TIME(245059, "欠频加载恢复时间"),
    OVERVOLTAGE_TRIGGERS_FIRST_ORDER_VALUE(245060, "过压触发一阶值(额定电压百分比)"),
    OVERVOLTAGE_TRIGGERS_FIRST_ORDER_TRIP_TIME(245061, "过压触发一阶跳脱时间"),
    OVERVOLTAGE_TRIGGERS_SECOND_ORDER_VALUE(245062, "过压触发二阶值(额定电压百分比)"),
    OVERVOLTAGE_TRIGGERS_SECOND_ORDER_TRIP_TIME(245063, "过压触发二阶跳脱时间"),
    OVERVOLTAGE_TRIGGERS_THIRD_ORDER_VALUE(245064, "过压触发三阶值(额定电压百分比)"),
    OVERVOLTAGE_TRIGGERS_THIRD_ORDER_TRIP_TIME(245065, "过压触发三阶跳脱时间"),
    MINUTE_10_OVERVOLTAGE_PROTECTION_VALUE(245066, "10分钟过压保护值(额定电压百分比)"),
    UNDERVOLTAGE_TRIGGERS_FIRST_ORDER_VALUE(245067, "欠压触发一阶值(额定电压百分比)"),
    UNDERVOLTAGE_TRIGGERS_FIRST_ORDER_TRIP_TIME(245068, "欠压触发一阶跳脱时间"),
    UNDERVOLTAGE_TRIGGERS_SECOND_ORDER_VALUE(245069, "欠压触发二阶值(额定电压百分比)"),
    UNDERVOLTAGE_TRIGGERS_SECOND_ORDER_TRIP_TIME(245070, "欠压触发二阶跳脱时间"),
    UNDERVOLTAGE_TRIGGERS_THIRD_ORDER_VALUE(245071, "欠压触发三阶值(额定电压百分比)"),
    UNDERVOLTAGE_TRIGGERS_THIRD_ORDER_TRIP_TIME(245072, "欠压触发三阶跳脱时间"),
    OVERFREQUENCY_TRIGGERS_FIRST_ORDER_VALUE(245073, "过频触发一阶值(额定频率百分比)"),
    OVERFREQUENCY_TRIGGERS_FIRST_ORDER_TRIP_TIME(245074, "过频触发一阶跳脱时间"),
    OVERFREQUENCY_TRIGGERS_SECOND_ORDER_VALUE(245075, "过频触发二阶值(额定频率百分比)"),
    OVERFREQUENCY_TRIGGERS_SECOND_ORDER_TRIP_TIME(245076, "过频触发二阶跳脱时间"),
    UNDERFREQUENCY_TRIGGERS_FIRST_ORDER_VALUE(245077, "欠频触发一阶值(额定频率百分比)"),
    UNDERFREQUENCY_TRIGGERS_FIRST_ORDER_TRIP_TIME(245078, "欠频触发一阶跳脱时间"),
    UNDERFREQUENCY_TRIGGERS_SECOND_ORDER_VALUE(245079, "欠频触发二阶值(额定频率百分比)"),
    UNDERFREQUENCY_TRIGGERS_SECOND_ORDER_TRIP_TIME(245080, "欠频触发二阶跳脱时间"),
    UPPER_LIMIT_CONNECTION_VOLTAGE(245081, "连接电压上限"),
    LOWER_LIMIT_CONNECTION_VOLTAGE(245082, "连接电压下限"),
    UPPER_LIMIT_CONNECTION_FREQUENCY(245083, "连接频率上限"),
    LOWER_LIMIT_CONNECTION_FREQUENCY(245084, "连接频率下限"),
    CONNECTION_LATENCY(245085, "连接等待时间"),
    COSᵩ_P_CURVE(245086, "Cosᵩ(P)曲线"),
    EQUIPMENT_MODE_TEST_INSTRUCTION(245087, "装备模式测试指令"),
    BAT_2_CAPACITY(245088, "电池2容量"),
    BAT_2_MAX_CHARGE_CURRENT(245089, "电池2最大充电电流"),
    BAT_2_MAX_DISCHARGE_CURRENT(245090, "电池2最大放电电流"),
    BAT_2_RATED_VOLTAGE(245091, "电池2额定电压"),
    BAT_2_MIN_SOC(245092, "电池2最小SOC"),
    BAT_2_OVERVOLTAGE_SET_VALUE(245093, "电池2过压设定值"),
    BAT_2_UNDERVOLTAGE_SET_VALUE(245094, "电池2欠压设定值"),
    SINGLE_OR_PARALLEL(245095, "单机或并机"),
    SYSTEM_MODULES_NUMBER(245096, "系统模块数量"),
    PARALLEL_MODULE_NUMBER(245097, "并机模块机号"),
    CERTIFICATION_COUNTRY(245098, "认证国家"),
    QU_CURVE_ENABLE(245099, "QU曲线"),
    PU_CURVE_ENABLE(245100, "PU曲线"),
    FIXED_REACTIVE_POWER(245101, "固定无功功率"),
    SPECIFIC_FUNCTION(245102, "特殊功能"),
    ACTIVE_CHANGE_SLOPE(245103, "有功变化斜率"),
    RECONNECTION_TIME(245104, "并网重连时间"),
    LEAD_ACCUMULATOR_EQUALIZING_VOLTAGE(245105, "铅蓄电池均充电压"),
    LEAD_ACCUMULATOR_FLOATING_CHARGING_VOLTAGE(245106, "铅蓄电池浮充电压"),
    LEAD_ACCUMULATOR_END_OFF_VOLTAGE(245107, "铅蓄电池放电截止电压"),
    LEAD_ACCUMULATOR_EQUALIZING_CHARGE_ENABLED(245108, "铅蓄电池均充使能"),
    BATTERY_CHARGING_TEMPERATURE_COMPENSATION_ENABLED(245109, "电池充电温度补偿使能"),
    EQUALIZATION_TEMPERATURE_COMPENSATION_COEFFICIENT(245110, "均充温度补偿系数"),
    FLOATING_TEMPERATURE_COMPENSATION_COEFFICIENT(245111, "浮充温度补偿系数"),
    COUNTER_CURRENT_MODE(245112, "防逆流模式"),
    PV_3_ENABLE(245113, "光伏3启用"),
    PV_4_ENABLE(245114, "光伏4启用"),
    METER_ACCESS_MODE(245115, "电表接入方式"),
    POWER_FACTOR_CONTROLS_STARTING_VOLTAGE(245116, "功率因素控制起点电压"),
    POWER_FACTOR_CONTROLS_ENDING_VOLTAGE(245117, "功率因素控制终点电压"),
    POWER_FACTOR_CONTROLS_STARTING_POWER(245118, "功率因素控制起点功率"),
    POWER_FACTOR_CONTROLS_ENDING_POWER(245119, "功率因素控制终点功率"),
    POWER_FACTOR_CONTROLS_STARTING_PF(245120, "功率因素控制起点PF"),
    POWER_FACTOR_CONTROLS_ENDING_PF(245121, "功率因素控制终点PF"),
    REACTIVE_CURRENT_RESPONSE_TIME(245122, "无功电流响应时间"),
    OVERFREQUENCY_LOAD_DROP(245123, "过频降载"),
    UNDERFREQUENCY_LOADING(245124, "欠频加载"),
    PUQU_CURVE_VOLTAGE_FILTER_ASSIGNMENT(245125, "PUQU曲线电压滤波赋值"),
    CURVILINEAR_MODEL(245126, "曲线模式"),
    OVERFREQUENCY_INITIAL_DERATING_COEFFICIENT(245127, "过频初始降额系数"),
    UNDERFREQUENCY_INITIAL_DERATING_COEFFICIENT(245128, "过频初始降额系数"),
    PROTECTS_RECONNECTION_RATE(245129, "保护重连速率"),
    ACTIVE_CURRENT_RESPONSE_TIME(245130, "有功电流响应时间"),
    AC_COUPLING_MODE(245131, "AcCoupling模式"),
    GRID_CONNECTED_CERTIFICATION_COMPENSATION_RATE(245132, "并网认证补偿率"),
    PU_COORDINATE_POINT_1_VOLTAGE(245133, "PU坐标点1电压"),
    PU_COORDINATE_POINT_1_ACTIVE_POWER(245134, "PU坐标点1有功功率"),
    PU_COORDINATE_POINT_2_VOLTAGE(245135, "PU坐标点2电压"),
    PU_COORDINATE_POINT_2_ACTIVE_POWER(245136, "PU坐标点2有功功率"),
    PU_COORDINATE_POINT_3_VOLTAGE(245137, "PU坐标点3电压"),
    PU_COORDINATE_POINT_3_ACTIVE_POWER(245138, "PU坐标点3有功功率"),
    PU_COORDINATE_POINT_4_VOLTAGE(245139, "PU坐标点4电压"),
    PU_COORDINATE_POINT_4_ACTIVE_POWER(245140, "PU坐标点4有功功率"),
    QU_COORDINATE_POINT_1_VOLTAGE(245141, "QU坐标点1电压"),
    QU_COORDINATE_POINT_1_REACTIVE_POWER(245142, "QU坐标点1无功功率"),
    QU_COORDINATE_POINT_2_VOLTAGE(245143, "QU坐标点2电压"),
    QU_COORDINATE_POINT_2_REACTIVE_POWER(245144, "QU坐标点2无功功率"),
    QU_COORDINATE_POINT_3_VOLTAGE(245145, "QU坐标点3电压"),
    QU_COORDINATE_POINT_3_REACTIVE_POWER(245146, "QU坐标点3无功功率"),
    QU_COORDINATE_POINT_4_VOLTAGE(245147, "QU坐标点4电压"),
    QU_COORDINATE_POINT_4_REACTIVE_POWER(245148, "QU坐标点4无功功率"),
    FIRST_LOW_VOLTAGE_CROSSING_VOLTAGE(245149, "一阶低压穿越电压"),
    FIRST_LOW_VOLTAGE_CROSSING_TIME(245150, "一阶低压穿越时间"),
    SECOND_LOW_VOLTAGE_CROSSING_VOLTAGE(245151, "二阶低压穿越电压"),
    SECOND_LOW_VOLTAGE_CROSSING_TIME(245152, "二阶低压穿越时间"),
    FIRST_HIGH_VOLTAGE_CROSSING_VOLTAGE(245153, "一阶高压穿越电压"),
    FIRST_HIGH_VOLTAGE_CROSSING_TIME(245154, "一阶高压穿越时间"),
    SECOND_HIGH_VOLTAGE_CROSSING_VOLTAGE(245155, "二阶高压穿越电压"),
    SECOND_HIGH_VOLTAGE_CROSSING_TIME(245156, "二阶高压穿越时间"),
    THIRD_HIGH_VOLTAGE_CROSSING_VOLTAGE(245157, "三阶高压穿越电压"),
    THIRD_HIGH_VOLTAGE_CROSSING_TIME(245158, "三阶高压穿越时间"),
    FOURTH_HIGH_VOLTAGE_CROSSING_VOLTAGE(245159, "四阶高压穿越电压"),
    FOURTH_HIGH_VOLTAGE_CROSSING_TIME(245160, "四阶高压穿越时间"),
    FIFTH_HIGH_VOLTAGE_CROSSING_VOLTAGE(245161, "五阶高压穿越电压"),
    FIFTH_HIGH_VOLTAGE_CROSSING_TIME(245162, "五阶高压穿越时间"),
    OVER_FREQUENCY_LOAD_DROP_RESPONSE_TIME(245163, "过频降载响应时间"),
    UNDER_FREQUENCY_LOAD_RESPONSE_TIME(245164, "欠频加载响应时间"),
    BAT_1_STANDBY_POWER_SOC(245165, "电池1备电SOC"),
    BAT_2_STANDBY_POWER_SOC(245166, "电池2备电SOC"),
    OTHER_TIME_PERIOD_MODE(245167, "其它时间段模式"),
    SINGLE_BAT_MULTIPLE_PARALLEL(245168, "单电池多并机"),
    LEAD_ACCUMULATOR_2_EQUALIZING_VOLTAGE(245169, "铅蓄电池2均充电压"),
    LEAD_ACCUMULATOR_2_FLOATING_CHARGING_VOLTAGE(245170, "铅蓄电池2浮充电压"),
    LEAD_ACCUMULATOR_2_END_OFF_VOLTAGE(245171, "铅蓄电池2放电截止电压"),
    OVER_FREQUENCY_DROP_ZERO_POWER_FREQUENCY(245172, "过频降载过零点功率频率"),
    UNDER_FREQUENCY_LOAD_ZERO_POWER_FREQUENCY(245173, "欠频加载过零点功率频率"),
    OVERFREQUENCY_LOAD_DROP_END(245174, "过频降载终点"),
    UNDERFREQUENCY_LOAD_END(245175, "过频降载终点"),
    ARC_SENSOR_ENABLE(245176, "电弧传感器启用"),
    REACTIVE_COMPENSATION(245177, "无功补偿"),

    GRID_VOLTAGE_A_CALIBRATION(249000, "A相电网电压校准"),
    GRID_VOLTAGE_B_CALIBRATION(249001, "B相电网电压校准"),
    GRID_VOLTAGE_C_CALIBRATION(249002, "C相电网电压校准"),
    LOAD_VOLTAGE_A_CALIBRATION(249003, "A相负载电压校准"),
    LOAD_VOLTAGE_B_CALIBRATION(249004, "B相负载电压校准"),
    LOAD_VOLTAGE_C_CALIBRATION(249005, "C相负载电压校准"),
    LOAD_CURRENT_A_CALIBRATION(249006, "A相负载电流校准"),
    LOAD_CURRENT_B_CALIBRATION(249007, "B相负载电流校准"),
    LOAD_CURRENT_C_CALIBRATION(249008, "C相负载电流校准"),
    INVERTER_VOLTAGE_A_CALIBRATION(249009, "A相逆变电压校准"),
    INVERTER_VOLTAGE_B_CALIBRATION(249010, "B相逆变电压校准"),
    INVERTER_VOLTAGE_C_CALIBRATION(249011, "C相逆变电压校准"),
    INVERTER_CURRENT_A_CALIBRATION(249012, "A相逆变电流校准"),
    INVERTER_CURRENT_B_CALIBRATION(249013, "B相逆变电流校准"),
    INVERTER_CURRENT_C_CALIBRATION(249014, "C相逆变电流校准"),
    CELL_VOLTAGE_1_CALIBRATION(249015, "电池1电压校准"),
    CELL_VOLTAGE_2_CALIBRATION(249016, "电池2电压校准"),
    CELL_CURRENT_1_CALIBRATION(249017, "电池1电流校准"),
    CELL_CURRENT_2_CALIBRATION(249018, "电池2电流校准"),
    PV_VOLTAGE_1_CALIBRATION(249019, "光伏1电压校准"),
    PV_VOLTAGE_2_CALIBRATION(249020, "光伏2电压校准"),
    PV_CURRENT_1_CALIBRATION(249021, "光伏1电流校准"),
    PV_CURRENT_2_CALIBRATION(249022, "光伏2电流校准"),
    DCAC_POSITIVE_BUS_VOLTAGE_CALIBRATION(249023, "正母线电压校准（DCAC）"),
    DCAC_NEGATIVE_BUS_VOLTAGE_CALIBRATION(249024, "负母线电压校准（DCAC）"),
    DCDC_POSITIVE_BUS_VOLTAGE_CALIBRATION(249025, "正母线电压校准（DCDC）"),
    DCDC_NEGATIVE_BUS_VOLTAGE_CALIBRATION(249026, "负母线电压校准（DCDC）"),
    DC_CONVERTER_VOLTAGE_CALIBRATION(249027, "直流变换器电压校准"),
    DC_CONVERTER_CURRENT_CALIBRATION(249028, "直流变换器电流校准"),
    LIMITED_POWER_CALIBRATION(249029, "限功率校准(功率偏差)"),
    DISPLAY_POWER_CALIBRATION(249030, "显示功率校准"),
    //    CALIBRATION_RESERVE(249031, "校准预留"),
    UNIT_CAPACITY(249032, "单机容量"),
    POWER_ON(249033, "开机"),
    POWER_OFF(249034, "关机"),
    CLEARING_FAULT(249035, "故障清除"),
    BAT_SELF_CHECK(249036, "电池自检"),
    BAT_SELF_CHECK_COMPLETE(249037, "电池自检结束"),
    DERATING_COEFFICIENT(249038, "降额系数"),
    //    DERATING_RESERVE(249039, "保留"),
    DERATING_CAPACITY(249040, "降额容量"),
    INVERTER_VOLTAGE_A_DIRECT_FLOW_ZERO(249041, "逆变电压A相直流量校零"),
    INVERTER_VOLTAGE_B_DIRECT_FLOW_ZERO(249042, "逆变电压B相直流量校零"),
    INVERTER_VOLTAGE_C_DIRECT_FLOW_ZERO(249043, "逆变电压C相直流量校零"),
    INVERTER_CURRENT_A_DIRECT_FLOW_ZERO(249044, "逆变电流A相直流量校零"),
    INVERTER_CURRENT_B_DIRECT_FLOW_ZERO(249045, "逆变电流B相直流量校零"),
    INVERTER_CURRENT_C_DIRECT_FLOW_ZERO(249046, "逆变电流C相直流量校零"),
    OIL_ENGINE_VOLTAGE_A_CALIBRATION(249047, "A相油机电压校准"),
    OIL_ENGINE_VOLTAGE_B_CALIBRATION(249048, "B相油机电压校准"),
    OIL_ENGINE_VOLTAGE_C_CALIBRATION(249049, "C相油机电压校准"),
    OIL_ENGINE_CURRENT_A_CALIBRATION(249050, "A相油机电流校准"),
    OIL_ENGINE_CURRENT_B_CALIBRATION(249051, "B相油机电流校准"),
    OIL_ENGINE_CURRENT_C_CALIBRATION(249052, "C相油机电流校准"),
    PV_VOLTAGE_3_CALIBRATION(249053, "光伏3电压校准"),
    PV_VOLTAGE_4_CALIBRATION(249054, "光伏4电压校准"),
    PV_CURRENT_3_CALIBRATION(249055, "光伏3电流校准"),
    PV_CURRENT_4_CALIBRATION(249056, "光伏4电流校准"),
    BATTERY_1_DISCHARGE_CURRENT_CALIBRATION(249057, "电池1放电电流校准"),
    BATTERY_2_DISCHARGE_CURRENT_CALIBRATION(249058, "电池2放电电流校准"),

    METER_DETECTION(249200, "电表检测"),
    BAT_VOLTAGE_LEVEL(249201, "电池电压等级"),
    BAT_1_ACTIVATION(249202, "电池1激活"),
    BAT_2_ACTIVATION(249203, "电池2激活"),


    CLEAR_TOTAL_KWH(265000, "清除总电量"),
    CLEAR_TODAY_KWH(265001, "清除当天电量"),
    METER_CT_RATIO(265002, "电表CT变比"),
    METER_TYPE(265003, "电表型号"),
    BAT_1_PROTOCOL(265004, "电池1协议"),
    BAT_2_PROTOCOL(265005, "电池2协议"),
    PV_TYPE(265006, "PV类型"),
    PV_MANUFACTURER(265007, "PV厂商"),
    //    PV_RESERVE(265008, "预留"),
    FACTORY_DATA_RESET(265009, "恢复出厂设置"),
    CHARGING_PERIOD_1_START_TIME(265010, "充电时间段1开始时间"),
    CHARGING_PERIOD_1_END_TIME(265011, "充电时间段1结束时间"),
    DISCHARGING_PERIOD_1_START_TIME(265012, "放电时间段1开始时间"),
    DISCHARGING_PERIOD_1_END_TIME(265013, "放电时间段1结束时间"),
    CHARGING_PERIOD_2_START_TIME(265014, "充电时间段2开始时间"),
    CHARGING_PERIOD_2_END_TIME(265015, "充电时间段2结束时间"),
    DISCHARGING_PERIOD_2_START_TIME(265016, "放电时间段2开始时间"),
    DISCHARGING_PERIOD_2_END_TIME(265017, "放电时间段2结束时间"),
    CHARGING_PERIOD_3_START_TIME(265018, "充电时间段3开始时间"),
    CHARGING_PERIOD_3_END_TIME(265019, "充电时间段3结束时间"),
    DISCHARGING_PERIOD_3_START_TIME(265020, "放电时间段3开始时间"),
    DISCHARGING_PERIOD_3_END_TIME(265021, "放电时间段3结束时间"),
    CHARGING_PERIOD_4_START_TIME(265022, "充电时间段4开始时间"),
    CHARGING_PERIOD_4_END_TIME(265023, "充电时间段4结束时间"),
    DISCHARGING_PERIOD_4_START_TIME(265024, "放电时间段4开始时间"),
    DISCHARGING_PERIOD_4_END_TIME(265025, "放电时间段4结束时间"),
    CHARGING_PERIOD_5_START_TIME(265026, "充电时间段5开始时间"),
    CHARGING_PERIOD_5_END_TIME(265027, "充电时间段5结束时间"),
    DISCHARGING_PERIOD_5_START_TIME(265028, "放电时间段5开始时间"),
    DISCHARGING_PERIOD_5_END_TIME(265029, "放电时间段5结束时间"),
    CHARGING_PERIOD_6_START_TIME(265030, "充电时间段6开始时间"),
    CHARGING_PERIOD_6_END_TIME(265031, "充电时间段6结束时间"),
    DISCHARGING_PERIOD_6_START_TIME(265032, "放电时间段6开始时间"),
    DISCHARGING_PERIOD_6_END_TIME(265033, "放电时间段6结束时间"),
    //    TIME_RESERVE_1(265034, "预留1"),
//    TIME_RESERVE_2(265035, "预留2"),
//    TIME_RESERVE_3(265036, "预留3"),
//    TIME_RESERVE_4(265037, "预留4"),
//    TIME_RESERVE_5(265038, "预留5"),
//    TIME_RESERVE_6(265039, "预留6"),
    MONITOR_OP_STATUS(265040, "监控运行状态"),
    MONITOR_ALARM(265041, "监控告警"),
    MULTIPLEXED_PORT_3_BAUD_RATE(265042, "复用端口3波特率"),
    MULTIPLEXED_PORT_3_PATTERN(265043, "复用端口3模式"),
    PCS_CHARGING_DISCHARGING_DECISION_VALUE(265044, "PCS充放决策值"),
    //    PCS_RESERVE_1(265045, "预留1"),
//    PCS_RESERVE_2(265046, "预留2"),
//    PCS_RESERVE_3(265047, "预留3"),
//    PCS_RESERVE_4(265048, "预留4"),
//    PCS_RESERVE_5(265049, "预留5"),
//    PCS_RESERVE_6(265050, "预留6"),
//    PCS_RESERVE_7(265051, "预留7"),
//    PCS_RESERVE_8(265052, "预留8"),
//    PCS_RESERVE_9(265053, "预留9"),
//    PCS_RESERVE_10(265054, "预留10"),
    EXPENSE_MODEL(265055, "费用模式"),
    CURRENCY_TYPE(265056, "货币类型"),
    CONTRACT_ELECTRICITY_PRICE(265057, "合约电价价格"),
    FIRST_STEP_TARIFF(265058, "第一阶梯电价"),
    SECOND_STEP_TARIFF(265059, "第二阶梯电价"),
    THIRD_STEP_TARIFF(265060, "第三阶梯电价"),
    //    TARIFF_RESERVE_4(265061, "预留"),
//    TARIFF_RESERVE_5(265062, "预留"),
    TOU_PRICE_1(265063, "分时电价价格1"),
    PEAK_LOAD_SHIFTING_TIME_PERIOD_1_START(265064, "削峰填谷时间段1起始"),
    PEAK_LOAD_SHIFTING_TIME_PERIOD_1_END(265065, "削峰填谷时间段1结束"),
    TOU_PRICE_2(265066, "分时电价价格2"),
    PEAK_LOAD_SHIFTING_TIME_PERIOD_2_START(265067, "削峰填谷时间段2起始"),
    PEAK_LOAD_SHIFTING_TIME_PERIOD_2_END(265068, "削峰填谷时间段2结束"),
    TOU_PRICE_3(265069, "分时电价价格3"),
    PEAK_LOAD_SHIFTING_TIME_PERIOD_3_START(265070, "削峰填谷时间段3起始"),
    PEAK_LOAD_SHIFTING_TIME_PERIOD_3_END(265071, "削峰填谷时间段3结束"),
    TOU_PRICE_4(265072, "分时电价价格4"),
    PEAK_LOAD_SHIFTING_TIME_PERIOD_4_START(265073, "削峰填谷时间段4起始"),
    PEAK_LOAD_SHIFTING_TIME_PERIOD_4_END(265074, "削峰填谷时间段4结束"),
    TOU_PRICE_5(265075, "分时电价价格5"),
    PEAK_LOAD_SHIFTING_TIME_PERIOD_5_START(265076, "削峰填谷时间段5起始"),
    PEAK_LOAD_SHIFTING_TIME_PERIOD_5_END(265077, "削峰填谷时间段5结束"),
    TOU_PRICE_6(265078, "分时电价价格6"),
    PEAK_LOAD_SHIFTING_TIME_PERIOD_6_START(265079, "削峰填谷时间段6起始"),
    PEAK_LOAD_SHIFTING_TIME_PERIOD_6_END(265080, "削峰填谷时间段6结束"),

    //    TIMED_CHARGE_RELEASE_SWITCH(265200, "定时充放开关 改为 预留"),
//    TIMED_RESERVE(265201, "预留"),
    REPEAT_CYCLE(265202, "重复周数"),
    EFFECTIVE_START_DATE(265203, "生效起始日期"),
    EFFECTIVE_END_DATE(265204, "生效结束日期"),
    TIME_PERIOD_1_CHARGE_POWER(265205, "时间段1充电功率"),
    TIME_PERIOD_1_DISCHARGE_POWER(265206, "时间段1放电功率"),
    TIME_PERIOD_2_CHARGE_POWER(265207, "时间段2充电功率"),
    TIME_PERIOD_2_DISCHARGE_POWER(265208, "时间段2放电功率"),
    TIME_PERIOD_3_CHARGE_POWER(265209, "时间段3充电功率"),
    TIME_PERIOD_3_DISCHARGE_POWER(265210, "时间段3放电功率"),
    TIME_PERIOD_4_CHARGE_POWER(265211, "时间段4充电功率"),
    TIME_PERIOD_4_DISCHARGE_POWER(265212, "时间段4放电功率"),
    TIME_PERIOD_5_CHARGE_POWER(265213, "时间段5充电功率"),
    TIME_PERIOD_5_DISCHARGE_POWER(265214, "时间段5放电功率"),
    TIME_PERIOD_6_CHARGE_POWER(265215, "时间段6充电功率"),
    TIME_PERIOD_6_DISCHARGE_POWER(265216, "时间段6放电功率"),

    BMS1_RS485_DEVICE_ADDRESS(265300, "BMS1 RS485设备地址"),
    BMS1_RS485_COMMUNICATION_BAUD_RATE(265301, "BMS1 RS485通讯波特率"),

    BMS2_RS485_DEVICE_ADDRESS(265400, "BMS2 RS485设备地址"),
    BMS2_RS485_COMMUNICATION_BAUD_RATE(265401, "BMS2 RS485通讯波特率"),
    //    BMS_RESERVE(265402, "预留"),
    BMS1_COMMUNICATION_MODE(265403, "BMS1通讯方式"),
    BMS2_COMMUNICATION_MODE(265404, "BMS2通讯方式"),
    BMS1_RS485_MULTIPLEX(265405, "BMS1 RS485复用"),
    BMS2_RS485_MULTIPLEX(265406, "BMS2 RS485复用"),
    PCS_FIRST_ACTIVATION_TIME(265407, "PCS首次激活时间"),
    PCS_SN_1(265408, "PCS序列号1"),
    PCS_SN_2(265409, "PCS序列号2"),
    PCS_SN_3(265410, "PCS序列号3"),
    PCS_SN_4(265411, "PCS序列号4"),
    PCS_SN_5(265412, "PCS序列号5"),
    PCS_SN_6(265413, "PCS序列号6"),

    STARTUP_SELF_TEST(265450, "启动自检"),
    VT_A_10_MINUTE_OVERVOLTAGE_PROTECTION_THRESHOLD_59S1(265451, "Vt:A相十分钟过压保护阈值"),
    VO_A_POWER_GRID_VOLTAGE_OFF_VALUE_59S1(265452, "Vo:A相电网电压断开值"),
    TT_A_PROTECTION_TIME_THRESHOLD__59S1(265453, "Tt:A相保护时间阈值"),
    TO_A_PROTECTION_TIME_THRESHOLD__59S1(265454, "To:A相保护时间阈值"),
    VS_A_POWER_GRID_VOLTAGE_PRE_OFF_VALUE_59S1(265455, "Vs:A相电网电压断开前值"),
    A_TEST_STATUS_59S1(265456, "（59.S1）A相测试状态"),
    A_COMPLETION_TIME_59S1(265457, "A相完成时间"),
    VT_A_OVERVOLTAGE_PROTECTION_THRESHOLD_59S2(265458, "Vt:A相过压保护阈值"),
    VO_A_POWER_GRID_VOLTAGE_OFF_VALUE_59S2(265459, "Vo:A相电网电压断开值"),
    TT_A_PROTECTION_TIME_THRESHOLD_59S2(265460, "Tt:A相保护时间阈值"),
    TO_A_PROTECTION_TIME_THRESHOLD_59S2(265461, "To:A相保护时间阈值"),
    VS_A_POWER_GRID_VOLTAGE_PRE_OFF_VALUE_59S2(265462, "Vs:A相电网电压断开前值"),
    A_TEST_STATUS_59S2(265463, "（59.S2）A相测试状态"),
    A_COMPLETION_TIME_59S2(265464, "A相完成时间"),
    VT_A_UNDERVOLTAGE_PROTECTION_THRESHOLD_27S1(265465, "Vt:A相欠压保护阈值"),
    VO_A_POWER_GRID_VOLTAGE_OFF_VALUE_27S1(265466, "Vo:A相电网电压断开值"),
    TT_A_PROTECTION_TIME_THRESHOLD_27S1(265467, "Tt:A相保护时间阈值"),
    TO_A_DISCONNECT_TIME_27S1(265468, "To:A相断开时间"),
    VS_A_POWER_GRID_VOLTAGE_PRE_OFF_VALUE_27S1(265469, "Vs:A相电网电压断开前值"),
    A_TEST_STATUS_27S1(265470, "（27.S1）A相测试状态"),
    A_COMPLETION_TIME_27S1(265471, "A相完成时间"),
    VT_A_UNDERVOLTAGE_PROTECTION_THRESHOLD_27S2(265472, "Vt:A相欠压保护阈值"),
    VO_A_POWER_GRID_VOLTAGE_OFF_VALUE_27S2(265473, "Vo:A相电网电压断开值"),
    TT_A_PROTECTION_TIME_THRESHOLD_27S2(265474, "Tt:A相保护时间阈值"),
    TO_A_DISCONNECT_TIME_27S2(265475, "To:A相断开时间"),
    VS_A_POWER_GRID_VOLTAGE_PRE_OFF_VALUE_27S2(265476, "Vs:A相电网电压断开前值"),
    A_TEST_STATUS_27S2(265477, "（27.S2）A相测试状态"),
    A_COMPLETION_TIME_27S2(265478, "A相完成时间"),
    FT_A_OVERFREQUENCY_PROTECTION_THRESHOLD_81S1(265479, "Ft:A相过频保护阈值"),
    VO_A_POWER_GRID_FREQUENCY_OFF_VALUE_81S1(265480, "Fo:A相电网频率断开值"),
    TT_A_PROTECTION_TIME_THRESHOLD_81S1(265481, "Tt:A相保护时间阈值"),
    TO_A_DISCONNECT_TIME_81S1(265482, "To:A相断开时间"),
    VS_A_POWER_GRID_FREQUENCY_PRE_OFF_VALUE_81S1(265483, "Fs:A相电网频率断开前值"),
    A_TEST_STATUS_81S1(265484, "（81.S1）A相测试状态"),
    A_COMPLETION_TIME_81S1(265485, "A相完成时间"),
    FT_A_OVERFREQUENCY_PROTECTION_THRESHOLD_81S2(265486, "Ft:A相过频保护阈值"),
    VO_A_POWER_GRID_FREQUENCY_OFF_VALUE_81S2(265487, "Fo:A相电网频率断开值"),
    TT_A_PROTECTION_TIME_THRESHOLD_81S2(265488, "Tt:A相保护时间阈值"),
    TO_A_DISCONNECT_TIME_81S2(265489, "To:A相断开时间"),
    VS_A_POWER_GRID_FREQUENCY_PRE_OFF_VALUE_81S2(265490, "Fs:A相电网频率断开前值"),
    A_TEST_STATUS_81S2(265491, "（81.S2）A相测试状态"),
    A_COMPLETION_TIME_81S2(265492, "A相完成时间"),
    FT_A_UNDERFREQUENCY_PROTECTION_THRESHOLD_81S1(265493, "Ft:A相欠频保护阈值"),
    FO_A_POWER_GRID_FREQUENCY_OFF_VALUE_81S1(265494, "Fo:A相电网频率断开值"),
    TT_A_UNDERFREQUENCY_PROTECTION_TIME_THRESHOLD_81S1(265495, "Tt:A相保护时间阈值"),
    TO_A_UNDERFREQUENCY_DISCONNECT_TIME_81S1(265496, "To:A相断开时间"),
    VS_A_UNDERFREQUENCY_POWER_GRID_FREQUENCY_PRE_OFF_VALUE_81S1(265497, "Fs:A相电网频率断开前值"),
    A_UNDERFREQUENCY_TEST_STATUS_81S1(265498, "（81.S1）A相测试状态"),
    A_UNDERFREQUENCY_COMPLETION_TIME_81S1(265499, "A相完成时间"),
    FT_A_UNDERFREQUENCY_PROTECTION_THRESHOLD_81S2(265500, "Ft:A相欠频保护阈值"),
    FO_A_POWER_GRID_FREQUENCY_OFF_VALUE_81S2(265501, "Fo:A相电网频率断开值"),
    TT_A_UNDERFREQUENCY_PROTECTION_TIME_THRESHOLD_81S2(265502, "Tt:A相保护时间阈值"),
    TO_A_UNDERFREQUENCY_DISCONNECT_TIME_81S2(265503, "To:A相断开时间"),
    VS_A_UNDERFREQUENCY_POWER_GRID_FREQUENCY_PRE_OFF_VALUE_81S2(265504, "Fs:A相电网频率断开前值"),
    A_UNDERFREQUENCY_TEST_STATUS_81S2(265505, "（81.S2）A相测试状态"),
    A_UNDERFREQUENCY_COMPLETION_TIME_81S2(265506, "A相完成时间"),

    VT_B_10_MINUTE_OVERVOLTAGE_PROTECTION_THRESHOLD_59S1(265550, "Vt:B相十分钟过压保护阈值"),
    VO_B_POWER_GRID_VOLTAGE_OFF_VALUE_59S1(265551, "Vo:B相电网电压断开值"),
    TT_B_PROTECTION_TIME_THRESHOLD__59S1(265552, "Tt:B相保护时间阈值"),
    TO_B_PROTECTION_TIME_THRESHOLD__59S1(265553, "To:B相保护时间阈值"),
    VS_B_POWER_GRID_VOLTAGE_PRE_OFF_VALUE_59S1(265554, "Vs:B相电网电压断开前值"),
    B_TEST_STATUS_59S1(265555, "（59.S1）B相测试状态"),
    B_COMPLETION_TIME_59S1(265556, "B相完成时间"),
    VT_B_OVERVOLTAGE_PROTECTION_THRESHOLD_59S2(265557, "Vt:B相过压保护阈值"),
    VO_B_POWER_GRID_VOLTAGE_OFF_VALUE_59S2(265558, "Vo:B相电网电压断开值"),
    TT_B_PROTECTION_TIME_THRESHOLD_59S2(265559, "Tt:B相保护时间阈值"),
    TO_B_PROTECTION_TIME_THRESHOLD_59S2(265560, "To:B相保护时间阈值"),
    VS_B_POWER_GRID_VOLTAGE_PRE_OFF_VALUE_59S2(265561, "Vs:B相电网电压断开前值"),
    B_TEST_STATUS_59S2(265562, "（59.S2）B相测试状态"),
    B_COMPLETION_TIME_59S2(265563, "B相完成时间"),
    VT_B_UNDERVOLTAGE_PROTECTION_THRESHOLD_27S1(265564, "Vt:B相欠压保护阈值"),
    VO_B_POWER_GRID_VOLTAGE_OFF_VALUE_27S1(265565, "Vo:B相电网电压断开值"),
    TT_B_PROTECTION_TIME_THRESHOLD_27S1(265566, "Tt:B相保护时间阈值"),
    TO_B_DISCONNECT_TIME_27S1(265567, "To:B相断开时间"),
    VS_B_POWER_GRID_VOLTAGE_PRE_OFF_VALUE_27S1(265568, "Vs:B相电网电压断开前值"),
    B_TEST_STATUS_27S1(265569, "（27.S1）B相测试状态"),
    B_COMPLETION_TIME_27S1(265570, "B相完成时间"),
    VT_B_UNDERVOLTAGE_PROTECTION_THRESHOLD_27S2(265571, "Vt:B相欠压保护阈值"),
    VO_B_POWER_GRID_VOLTAGE_OFF_VALUE_27S2(265572, "Vo:B相电网电压断开值"),
    TT_B_PROTECTION_TIME_THRESHOLD_27S2(265573, "Tt:B相保护时间阈值"),
    TO_B_DISCONNECT_TIME_27S2(265574, "To:B相断开时间"),
    VS_B_POWER_GRID_VOLTAGE_PRE_OFF_VALUE_27S2(265575, "Vs:B相电网电压断开前值"),
    B_TEST_STATUS_27S2(265576, "（27.S2）B相测试状态"),
    B_COMPLETION_TIME_27S2(265577, "B相完成时间"),
    FT_B_OVERFREQUENCY_PROTECTION_THRESHOLD_81S1(265578, "Ft:B相过频保护阈值"),
    VO_B_POWER_GRID_FREQUENCY_OFF_VALUE_81S1(265579, "Fo:B相电网频率断开值"),
    TT_B_PROTECTION_TIME_THRESHOLD_81S1(265580, "Tt:B相保护时间阈值"),
    TO_B_DISCONNECT_TIME_81S1(265581, "To:B相断开时间"),
    VS_B_POWER_GRID_FREQUENCY_PRE_OFF_VALUE_81S1(265582, "Fs:B相电网频率断开前值"),
    B_TEST_STATUS_81S1(265583, "（81.S1）B相测试状态"),
    B_COMPLETION_TIME_81S1(265584, "B相完成时间"),
    FT_B_OVERFREQUENCY_PROTECTION_THRESHOLD_81S2(265585, "Ft:B相过频保护阈值"),
    VO_B_POWER_GRID_FREQUENCY_OFF_VALUE_81S2(265586, "Fo:B相电网频率断开值"),
    TT_B_PROTECTION_TIME_THRESHOLD_81S2(265587, "Tt:B相保护时间阈值"),
    TO_B_DISCONNECT_TIME_81S2(265588, "To:B相断开时间"),
    VS_B_POWER_GRID_FREQUENCY_PRE_OFF_VALUE_81S2(265589, "Fs:B相电网频率断开前值"),
    B_TEST_STATUS_81S2(265590, "（81.S2）B相测试状态"),
    B_COMPLETION_TIME_81S2(265591, "B相完成时间"),
    FT_B_UNDERFREQUENCY_PROTECTION_THRESHOLD_81S1(265592, "Ft:B相欠频保护阈值"),
    FO_B_POWER_GRID_FREQUENCY_OFF_VALUE_81S1(265593, "Fo:B相电网频率断开值"),
    TT_B_UNDERFREQUENCY_PROTECTION_TIME_THRESHOLD_81S1(265594, "Tt:B相保护时间阈值"),
    TO_B_UNDERFREQUENCY_DISCONNECT_TIME_81S1(265595, "To:B相断开时间"),
    VS_B_UNDERFREQUENCY_POWER_GRID_FREQUENCY_PRE_OFF_VALUE_81S1(265596, "Fs:B相电网频率断开前值"),
    B_UNDERFREQUENCY_TEST_STATUS_81S1(265597, "（81.S1）B相测试状态"),
    B_UNDERFREQUENCY_COMPLETION_TIME_81S1(265598, "B相完成时间"),
    FT_B_UNDERFREQUENCY_PROTECTION_THRESHOLD_81S2(265599, "Ft:B相欠频保护阈值"),
    FO_B_POWER_GRID_FREQUENCY_OFF_VALUE_81S2(265600, "Fo:B相电网频率断开值"),
    TT_B_UNDERFREQUENCY_PROTECTION_TIME_THRESHOLD_81S2(265601, "Tt:B相保护时间阈值"),
    TO_B_UNDERFREQUENCY_DISCONNECT_TIME_81S2(265602, "To:B相断开时间"),
    VS_B_UNDERFREQUENCY_POWER_GRID_FREQUENCY_PRE_OFF_VALUE_81S2(265603, "Fs:B相电网频率断开前值"),
    B_UNDERFREQUENCY_TEST_STATUS_81S2(265604, "（81.S2）B相测试状态"),
    B_UNDERFREQUENCY_COMPLETION_TIME_81S2(265605, "B相完成时间"),

    VT_C_10_MINUTE_OVERVOLTAGE_PROTECTION_THRESHOLD_59S1(265650, "Vt:C相十分钟过压保护阈值"),
    VO_C_POWER_GRID_VOLTAGE_OFF_VALUE_59S1(265651, "Vo:C相电网电压断开值"),
    TT_C_PROTECTION_TIME_THRESHOLD__59S1(265652, "Tt:C相保护时间阈值"),
    TO_C_PROTECTION_TIME_THRESHOLD__59S1(265653, "To:C相保护时间阈值"),
    VS_C_POWER_GRID_VOLTAGE_PRE_OFF_VALUE_59S1(265654, "Vs:C相电网电压断开前值"),
    C_TEST_STATUS_59S1(265655, "（59.S1）C相测试状态"),
    C_COMPLETION_TIME_59S1(265656, "C相完成时间"),
    VT_C_OVERVOLTAGE_PROTECTION_THRESHOLD_59S2(265657, "Vt:C相过压保护阈值"),
    VO_C_POWER_GRID_VOLTAGE_OFF_VALUE_59S2(265658, "Vo:C相电网电压断开值"),
    TT_C_PROTECTION_TIME_THRESHOLD_59S2(265659, "Tt:C相保护时间阈值"),
    TO_C_PROTECTION_TIME_THRESHOLD_59S2(265660, "To:C相保护时间阈值"),
    VS_C_POWER_GRID_VOLTAGE_PRE_OFF_VALUE_59S2(265661, "Vs:C相电网电压断开前值"),
    C_TEST_STATUS_59S2(265662, "（59.S2）C相测试状态"),
    C_COMPLETION_TIME_59S2(265663, "C相完成时间"),
    VT_C_UNDERVOLTAGE_PROTECTION_THRESHOLD_27S1(265664, "Vt:C相欠压保护阈值"),
    VO_C_POWER_GRID_VOLTAGE_OFF_VALUE_27S1(265665, "Vo:C相电网电压断开值"),
    TT_C_PROTECTION_TIME_THRESHOLD_27S1(265666, "Tt:C相保护时间阈值"),
    TO_C_DISCONNECT_TIME_27S1(265667, "To:C相断开时间"),
    VS_C_POWER_GRID_VOLTAGE_PRE_OFF_VALUE_27S1(265668, "Vs:C相电网电压断开前值"),
    C_TEST_STATUS_27S1(265669, "（27.S1）C相测试状态"),
    C_COMPLETION_TIME_27S1(265670, "C相完成时间"),
    VT_C_UNDERVOLTAGE_PROTECTION_THRESHOLD_27S2(265671, "Vt:C相欠压保护阈值"),
    VO_C_POWER_GRID_VOLTAGE_OFF_VALUE_27S2(265672, "Vo:C相电网电压断开值"),
    TT_C_PROTECTION_TIME_THRESHOLD_27S2(265673, "Tt:C相保护时间阈值"),
    TO_C_DISCONNECT_TIME_27S2(265674, "To:C相断开时间"),
    VS_C_POWER_GRID_VOLTAGE_PRE_OFF_VALUE_27S2(265675, "Vs:C相电网电压断开前值"),
    C_TEST_STATUS_27S2(265676, "（27.S2）C相测试状态"),
    C_COMPLETION_TIME_27S2(265677, "C相完成时间"),
    FT_C_OVERFREQUENCY_PROTECTION_THRESHOLD_81S1(265678, "Ft:C相过频保护阈值"),
    VO_C_POWER_GRID_FREQUENCY_OFF_VALUE_81S1(265679, "Fo:C相电网频率断开值"),
    TT_C_PROTECTION_TIME_THRESHOLD_81S1(265680, "Tt:C相保护时间阈值"),
    TO_C_DISCONNECT_TIME_81S1(265681, "To:C相断开时间"),
    VS_C_POWER_GRID_FREQUENCY_PRE_OFF_VALUE_81S1(265682, "Fs:C相电网频率断开前值"),
    C_TEST_STATUS_81S1(265683, "（81.S1）C相测试状态"),
    C_COMPLETION_TIME_81S1(265684, "C相完成时间"),
    FT_C_OVERFREQUENCY_PROTECTION_THRESHOLD_81S2(265685, "Ft:C相过频保护阈值"),
    VO_C_POWER_GRID_FREQUENCY_OFF_VALUE_81S2(265686, "Fo:C相电网频率断开值"),
    TT_C_PROTECTION_TIME_THRESHOLD_81S2(265687, "Tt:C相保护时间阈值"),
    TO_C_DISCONNECT_TIME_81S2(265688, "To:C相断开时间"),
    VS_C_POWER_GRID_FREQUENCY_PRE_OFF_VALUE_81S2(265689, "Fs:C相电网频率断开前值"),
    C_TEST_STATUS_81S2(265690, "（81.S2）C相测试状态"),
    C_COMPLETION_TIME_81S2(265691, "C相完成时间"),
    FT_C_UNDERFREQUENCY_PROTECTION_THRESHOLD_81S1(265692, "Ft:C相欠频保护阈值"),
    FO_C_POWER_GRID_FREQUENCY_OFF_VALUE_81S1(265693, "Fo:C相电网频率断开值"),
    TT_C_UNDERFREQUENCY_PROTECTION_TIME_THRESHOLD_81S1(265694, "Tt:C相保护时间阈值"),
    TO_C_UNDERFREQUENCY_DISCONNECT_TIME_81S1(265695, "To:C相断开时间"),
    VS_C_UNDERFREQUENCY_POWER_GRID_FREQUENCY_PRE_OFF_VALUE_81S1(265696, "Fs:C相电网频率断开前值"),
    C_UNDERFREQUENCY_TEST_STATUS_81S1(265697, "（81.S1）C相测试状态"),
    C_UNDERFREQUENCY_COMPLETION_TIME_81S1(265698, "C相完成时间"),
    FT_C_UNDERFREQUENCY_PROTECTION_THRESHOLD_81S2(265699, "Ft:C相欠频保护阈值"),
    FO_C_POWER_GRID_FREQUENCY_OFF_VALUE_81S2(265700, "Fo:C相电网频率断开值"),
    TT_C_UNDERFREQUENCY_PROTECTION_TIME_THRESHOLD_81S2(265701, "Tt:C相保护时间阈值"),
    TO_C_UNDERFREQUENCY_DISCONNECT_TIME_81S2(265702, "To:C相断开时间"),
    VS_C_UNDERFREQUENCY_POWER_GRID_FREQUENCY_PRE_OFF_VALUE_81S2(265703, "Fs:C相电网频率断开前值"),
    C_UNDERFREQUENCY_TEST_STATUS_81S2(265704, "（81.S2）C相测试状态"),
    C_UNDERFREQUENCY_COMPLETION_TIME_81S2(265705, "C相完成时间"),

    /**
     * 调试（285000~294999）
     */
    DRY_NODE_OUTPUTS_1(285000, "干节点输出1"),
    DRY_NODE_OUTPUTS_2(285001, "干节点输出2"),
    DRY_NODE_OUTPUTS_3(285002, "干节点输出3"),
    DRY_NODE_OUTPUTS_4(285003, "干节点输出4"),
    DRY_NODE_OUTPUTS_5(285004, "干节点输出5"),
    DRY_NODE_OUTPUTS_6(285005, "干节点输出6"),
    DRY_NODE_OUTPUTS_7(285006, "干节点输出7"),
    DRY_NODE_OUTPUTS_8(285007, "干节点输出8"),
    DCAC_TEMPERATURE_1(285008, "DCAC温度1"),
    DCAC_TEMPERATURE_2(285009, "DCAC温度2"),
    DCAC_TEMPERATURE_3(285010, "DCAC温度3"),
    DCAC_TEMPERATURE_4(285011, "DCAC温度4"),
    DCAC_TEMPERATURE_5(285012, "DCAC温度5"),
    //    DCAC_TEMPERATURE_6(285013, "DCAC温度预留6"),
//    DCAC_TEMPERATURE_7(285014, "DCAC温度预留7"),
//    DCAC_TEMPERATURE_8(285015, "DCAC温度预留8"),
    DCAC_POSITIVE_DC_BUS_VOLTAGE(285016, "DCAC_正直流母线电压"),
    DCAC_NEGATIVE_DC_BUS_VOLTAGE(285017, "DCAC_负直流母线电压"),
    DEBUG_YEAR(285018, "年"),
    DEBUG_MONTH(285019, "月"),
    DEBUG_DAY(285020, "日"),
    DEBUG_HOUR(285021, "时"),
    DEBUG_MINUTE(285022, "分"),
    DEBUG_SECOND(285023, "秒"),
    DCAC_DEBUG_VARIABLE_1(285024, "DCAC_调试变量1"),
    DCAC_DEBUG_VARIABLE_2(285025, "DCAC_调试变量2"),
    DCAC_DEBUG_VARIABLE_3(285026, "DCAC_调试变量3"),
    DCAC_DEBUG_VARIABLE_4(285027, "DCAC_调试变量4"),
    DCAC_DEBUG_VARIABLE_5(285028, "DCAC_调试变量5"),
    DCAC_DEBUG_VARIABLE_6(285029, "DCAC_调试变量6"),
    DCDC_TEMPERATURE_1(285030, "DCDC温度1"),
    DCDC_TEMPERATURE_2(285031, "DCDC温度2"),
    DCDC_TEMPERATURE_3(285032, "DCDC温度3"),
    DCDC_TEMPERATURE_4(285033, "DCDC温度4"),
    DCDC_TEMPERATURE_5(285034, "DCDC温度5"),
    //    DCDC_TEMPERATURE_6(285035, "DCDC温度预留6"),
//    DCDC_TEMPERATURE_7(285036, "DCDC温度预留7"),
//    DCDC_TEMPERATURE_8(285037, "DCDC温度预留8"),
    DCDC_POSITIVE_DC_BUS_VOLTAGE(285038, "DCDC_正直流母线电压"),
    DCDC_NEGATIVE_DC_BUS_VOLTAGE(285039, "DCDC_负直流母线电压"),
    BALANCED_CIRCUIT_CURRENT(285040, "平衡电路电流"),
    DEBUG_FAN_GEAR(285041, "风扇档位"),
    BAT_CHARGE_TIMES_1(285042, "电池1充电次数"),
    BAT_DISCHARGE_TIMES_1(285043, "电池1放电次数"),
    BAT_CHARGE_TIMES_2(285044, "电池2充电次数"),
    BAT_DISCHARGE_TIMES_2(285045, "电池2放电次数"),
    DCDC_DEBUG_VARIABLE_1(285046, "DCDC_调试变量1"),
    DCDC_DEBUG_VARIABLE_2(285047, "DCDC_调试变量2"),
    DCDC_DEBUG_VARIABLE_3(285048, "DCDC_调试变量3"),
    DCDC_DEBUG_VARIABLE_4(285049, "DCDC_调试变量4"),
    DCDC_DEBUG_VARIABLE_5(285050, "DCDC_调试变量5"),
    DCDC_DEBUG_VARIABLE_6(285051, "DCDC_调试变量6"),
    DC_CONVERTER_VOLTAGE(285052, "直流变换器电压"),
    DC_CONVERTER_CURRENT(285053, "直流变换器电流"),

    ;

    @JsonValue
    final int code;
    final String desc;


    HiKvCode(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    @JsonCreator
    public static HiKvCode valueOf(Object codeIn) {
        if (codeIn == null) {
            return HiKvCode.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof HiKvCode) {
            return (HiKvCode) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (HiKvCode type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return HiKvCode.UNKNOWN;
    }
}
