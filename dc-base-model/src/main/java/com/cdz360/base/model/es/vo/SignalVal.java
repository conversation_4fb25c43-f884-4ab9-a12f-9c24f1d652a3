package com.cdz360.base.model.es.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 信号量
 */
@Accessors(chain = true)
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class SignalVal extends EssBaseVal<Integer> {

    public SignalVal(int code, Integer addr, String name, Integer v, String desc) {
        super(code, addr, name, v, desc);
    }

    public SignalVal() {
        super();
    }

//    @Schema(title = "编码", description = "XxxKvCode.code")
//    private int code;
//
//    @Schema(title = "寄存器地址", description = "寄存器地址")
//    private Integer addr;
//
//    @Schema(title = "寄存器名称", description = "寄存器名称")
//    @JsonInclude(Include.NON_EMPTY)
//    private String name;
//
//    private Integer v;
//
//    @JsonInclude(Include.NON_EMPTY)
//    private String desc;
}
