package com.cdz360.base.model.es.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum PcsKvCode implements DcEnum {
    UNKNOWN(100000, "未知"),

    /**
     * 静态信息
     */
    HW_SN(100001, "硬件序号"),
    HW_MODEL(100005, "硬件型号"),
    SW_VER(100010, "软件版本"),
    HW_VENDOR(100015, "硬件厂家信息"),
    SCREEN_SW_VER(100020, "屏幕软件版本"),

    /**
     * 设备状态信息
     */
    STATUS(100100, "运行状态"), // PcsStatus
    OPEN_CMD_STATUS(100111, "开关机命令下发状态"),
    IDLE_STATUS(100112, "待机状态"),
    SELF_CHECK_STATUS(100113, "自检状态"),
    ON_GRID_STATUS(100114, "并网状态"),
    OFF_GRID_STATUS(100115, "离网状态"),
    REMOTE_CTRL_STATUS(100116, "远程控制状态"),
    ASSISTANT_POWER_STATUS(100130, "辅助电源上电状态"),
    BATTERY_CHARGE_STATUS(100131, "电池充电状态"),
    BATTERY_DISCHARGE_STATUS(100132, "电池放电状态"),
    DC_POSITIVE_RELAY_STATUS(100133, "直流主正继电器状态"),
    DC_NEGATIVE_RELAY_STATUS(100134, "直流主负继电器状态"),
    AC_RELAY_STATUS(100135, "交流继电器状态"),
    GRID_LOST_STATUS(100136, "电网掉电状态"),
    ERROR_CODE_1(100151, "故障码1"),
    ERROR_CODE_2(100152, "故障码2"),
    ERROR_CODE_3(100153, "故障码3"),
    ERROR_CODE_4(100154, "故障码4"),
    ERROR_CODE_5(100155, "故障码5"),
    ERROR_CODE_6(100156, "故障码6"),
    ERROR_CODE_7(100157, "故障码7"),
    ERROR_CODE_8(100158, "故障码8"),
    ERROR_CODE_9(100159, "故障码9"),
    ERROR_CODE_10(100160, "故障码10"),
    FAULT_STATUS(100161, "故障状态"),
    HW_FAULT_STATUS(100162, "硬件故障"),
    AC_OVER_LOAD_STATUS(100163, "逆变侧过流保护故障"),
    BUS_VOLTAGE_ERROR_STATUS(100164, "母线电压故障"),
    ISLAND_ERROR_STATUS(100165, "孤岛运行故障"),
    OFFSET_ERROR_STATUS(100166, "偏移值故障"),
    CFG_ERROR_STATUS(100167, "参数配置故障"),
    SIGNAL_ERROR_STATUS(100168, "过调制故障"),
    GRID_VOLTAGE_ERROR_STATUS(100169, "电网相电压故障"),
    LINE_VOLTAGE_ERROR_STATUS(100170, "电网线电压故障"),
    GRID_RATE_STATUS(100171, "电网频率故障"),
    ON_GRID_ERROR_STATUS(100172, "电网连接故障"),
    LOW_TEMP_ERROR_STATUS(100173, "低温故障"),
    HIGH_TEMP_ERROR_STATUS(100174, "高温故障"),
    ISO_ERROR_STATUS(100175, "绝缘故障"),
    RCD_ERROR_STATUS(100176, "RCD故障"),
    RES_ERROR_STATUS(100177, "RES故障"),
    AFCI_ERROR_STATUS(100178, "AFCI故障"),
    AC_RELAY_ERROR_STATUS(100179, "交流继电器故障"),


    PCS_CURRENT_OVERLOAD(100180, "逆变电流过流"),
    CURRENT_LEAK_ERROR_STATUS(100181, "漏电流故障"),
    DC_OVER_LOAD_STATUS(100182, "直流过压"),
    GRID_VALUE_ERROR_STATUS(100183, "电网幅值异常"),
    IGBT_OVER_TEMP_ERROR_STATUS(100184, "IGBT 过温"),
    DC_SOFT_START_ERROR(100185, "直流软启动故障"),
    DC_MAIN_CONTACTOR_ERROR(100186, "直流主接触器故障"),
    PCS_FAN_ERROR(100187, "风机故障"),
    MAIN_CONTACTOR_ERROR(100188, "主接触器故障"),
    INPUT_SWITCH_DISCONNECT(100189, "输入开关运行断开"),
    PCS_TEMP_HIGH_ERROR(100190, "机内过温"),
    SOFT_START_ERROR(100191, "软启动故障"),
    COMMUNICATION_ERROR(100192, "通信故障"),
    SPD_ERROR(100193, "防雷器故障"),
    EMERGENCY_STOP(100194, "急停故障"),
    BMS_SYS_ERROR(100195, "BMS 系统故障"),
    BMS_COMM_ERROR(100196, "BMS 通信故障"),
    FNL_COMM_ERROR(100197, "防逆流通信故障"),
    REMOTE_COMM_ERROR(100198, "远程通信故障"),
    DOOR_STATUS_WARNING(100199, "门禁告警"),
    PHASE_LOCK_ERROR(100200, "锁相异常"),
    FNL_STOP(100201, "防逆流关机"),
    FAN_TEMP_HIGH_WARNING(100202, "散热器过温告警"),
    PCS_HW_CURRENT_OVERLOAD(100203, "逆变硬件过流"),
    DRIVER_ERROR(100204, "驱动故障"),
//    CFG_ERROR(100205, "机型设置错误"),
    BATTERY_VOLTAGE_OVERLOAD(100206, "电池过压"),
    BATTERY_LOW_LOAD_VOLTAGE_ERROR(100207, "电池轻载低压"),
    DC_OVERLOAD(100208, "直流过流"),
    OUTPUT_VOLTAGE_ERROR(100209, "输出电压异常"),
    OUTPUT_VOLTAGE_OFF_GRID_ERROR(100210, "输出电压不符合离网条件"),
    OVERLOAD_PROTECTION(100211, "过载保护"),
    SHORTED_PROTECTION(100212, "短路保护"),
    JOIN_COMM_ERROR(100213, "并机通信线异常保护"),
    DC_FUSE_DISCONNECT(100214, "直流保险丝断开"),
    BATTERY_HIGH_LOAD_VOLTAGE_ERROR(100215, "电池重载低压"),
    BATTERY_LOW_VOLTAGE_WARNING(100216, "电池低压告警"),
    OUT_EMERGENCY_STOP_ERROR(100217, "外部急停故障"),
    BATTERY_CHARGE_VOLTAGE_ERROR(100218, "电池电压不符合充电条件"),
    OVERLOAD_WARNING(100219, "过载告警"),
    OUT_CONTACTOR_ERROR(100220, "外部接触器故障"),
    GRID_VOLTAGE_HIGH_ERROR(100221, "电网过压"),
    GRID_VOLTAGE_LOW_ERROR(100222, "电网欠压"),
    GRID_RATE_HIGH_ERROR(100223, "电网过频异常"),
    GRID_RATE_LOW_ERROR(100224, "电网欠频异常"),
    SMOKE_ERROR(100225, "烟雾告警"),

    GRID_PHASE_ERROR(100226, "电网相序异常"),

    /**
     * 设定值
     */
    OPEN_MODE_SETTING(100300, "开关机指令"),
    CFG_AUTO_MODE(100301, "模式自动切换"),
    ACTIVE_POWER_SETTING(100305, "有功功率设定值"),
    REACTIVE_POWER_SETTING(100306, "无功功率设定值"),
    PCS_PF_SETTING(100307, "功率因数设定值"),
    GRID_MODE_SETTING(100315, "并离网模式"),
    ISLAND_MODE_SETTING(100321, "主动孤岛使能"),
    TIME_YEAR_SETTING(100381, "年"),
    TIME_MONTH_SETTING(100382, "月"),
    TIME_DAY_SETTING(100383, "日"),
    TIME_HOUR_SETTING(100384, "时"),
    TIME_MINUTE_SETTING(100385, "分"),
    TIME_SECOND_SETTING(100386, "秒"),

    /**
     * 交流侧数据
     */
    GRID_VOLTAGE_A(100401, "电网侧A相电压"),
    GRID_VOLTAGE_B(100402, "电网侧B相电压"),
    GRID_VOLTAGE_C(100403, "电网侧C相电压"),

    GRID_VOLTAGE_AB(100405, "AB线电压"),
    GRID_VOLTAGE_BC(100406, "BC线电压"),
    GRID_VOLTAGE_CA(100407, "CA线电压"),

    PCS_VOLTAGE_A(100410, "PCS A相电压"),
    PCS_VOLTAGE_B(100411, "PCS A相电压"),
    PCS_VOLTAGE_C(100412, "PCS A相电压"),

    GRID_CURRENT(100415, "电网侧总电流"),
    GRID_CURRENT_A(100416, "电网侧A相电流"),
    GRID_CURRENT_B(100417, "电网侧B相电流"),
    GRID_CURRENT_C(100418, "电网侧C相电流"),

    PCS_CURRENT(100420, "逆变器侧总电流"),
    PCS_CURRENT_A(100421, "逆变器侧A相电流"),
    PCS_CURRENT_B(100422, "逆变器侧B相电流"),
    PCS_CURRENT_C(100423, "逆变器侧C相电流"),

    PCS_ACTIVE_POWER(100425, "逆变器侧总有功功率"),
    PCS_ACTIVE_POWER_A(100426, "逆变器侧A相有功功率"),
    PCS_ACTIVE_POWER_B(100427, "逆变器侧B相有功功率"),
    PCS_ACTIVE_POWER_C(100428, "逆变器侧C相有功功率"),

    PCS_REACTIVE_POWER(100430, "逆变器侧总无功功率"),
    PCS_REACTIVE_POWER_A(100431, "逆变器侧A相无功功率"),
    PCS_REACTIVE_POWER_B(100432, "逆变器侧B相无功功率"),
    PCS_REACTIVE_POWER_C(100433, "逆变器侧C相无功功率"),

    PCS_APPARENT_POWER(100435, "逆变器侧总视在功率"),
    PCS_APPARENT_POWER_A(100436, "逆变器侧A相视在功率"),
    PCS_APPARENT_POWER_B(100437, "逆变器侧B相视在功率"),
    PCS_APPARENT_POWER_C(100438, "逆变器侧C相视在功率"),

    PCS_BALANCED_CURRENT(100440, "平衡电流有效值"),
    PCS_LEAK_CURRENT(100441, "交流漏电流"),

    GRID_RATE(100445, "电网频率"),
    PCS_RATE(100446, "离网频率"),

    PCS_PF(100450, "功率因数"),
    PCS_PF_A(100451, "逆变器侧A相功率因数"),
    PCS_PF_B(100452, "逆变器侧B相功率因数"),
    PCS_PF_C(100453, "逆变器侧C相功率因数"),

    PCS_LOAD(100455, "PCS总负载量(%)"),
    PCS_LOAD_A(100456, "逆变器侧A相负载量(%)"),
    PCS_LOAD_B(100457, "逆变器侧B相负载量(%)"),
    PCS_LOAD_C(100458, "逆变器侧C相负载量(%)"),

    PCS_AVAILABLE_KVA(100460, "可用功率"),

    /**
     * 直流侧数据
     */
    DC_BUS_VOLTAGE(100505, "母线电压"),
    DC_BUS_VOLTAGE_POSITIVE(100506, "正母线电压"),
    DC_BUS_VOLTAGE_NEGATIVE(100507, "负母线电压"),
    BATTERY_VOLTAGE(100510, "电池侧电压"),
    BATTERY_CURRENT(100511, "电池侧电流"),
    BATTERY_POWER(100512, "电池侧功率"),

    /**
     * 其他数据: 温度等
     */
    PCS_TEMP(100601, "PCS温度"),
    FAN_TEMP_1(100611, "风扇温度"),
    MODULAR_TEMP_1(100621, "模块1温度"),
    MODULAR_TEMP_2(100622, "模块2温度"),
    MODULAR_TEMP_3(100623, "模块3温度"),
    MODULAR_TEMP_4(100624, "模块4温度"),
    RUNNING_DUR(100650, "运行时间"),

    /**
     * 统计数据
     */
    TODAY_CHARGE_KWH(100701, "当日充电电量"),
    TODAY_DISCHARGE_KWH(100702, "当日放电电量"),
    TODAY_PROFIT(100703, "当日收益"),
    TOTAL_CHARGE_KWH(100711, "总充电电量"),
    TOTAL_DISCHARGE_KWH(100712, "总放电电量"),
    TOTAL_PROFIT(100713, "总收益"),

    /**
     * 电价数据
     */
    CURRENT_PRICE(100801, "当前电价"),
    ;

    @JsonValue
    final int code;


    final String desc;


    PcsKvCode(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    @JsonCreator
    public static PcsKvCode valueOf(Object codeIn) {
        if (codeIn == null) {
            return PcsKvCode.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof PcsKvCode) {
            return (PcsKvCode) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (PcsKvCode type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return PcsKvCode.UNKNOWN;
    }
}
