package com.cdz360.base.model.es.dto;

import com.cdz360.base.model.es.type.hi.EssType;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "ESS厂家设备信息")
@Data
@Accessors(chain = true)
public class EssFactoryDto {

    @Schema(description = "协议版本号")
    @JsonProperty("pv")
    private String protocolVer;

    @Schema(description = "DSP1软件版本号")
    @JsonProperty("d1sv")
    private String dsp1SoftwareVer;

    @Schema(description = "DSP2软件版本号")
    @JsonProperty("d2sv")
    private String dsp2SoftwareVer;

    @Schema(description = "ARM软件版本号")
    @JsonProperty("armsv")
    private String armSoftwareVer;

    @Schema(description = "CPLD版本")
    @JsonProperty("cv")
    private String cpldVer;

    @Schema(description = "AFCI软件版本号")
    @JsonProperty("afcisv")
    private String afciSoftwareVer;

    @Schema(description = "机器类型")
    @JsonProperty("t")
    private EssType type;

}
