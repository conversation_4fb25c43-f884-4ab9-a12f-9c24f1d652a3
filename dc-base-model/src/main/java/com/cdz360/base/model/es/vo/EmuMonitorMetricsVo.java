package com.cdz360.base.model.es.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "EMU设备监控数据")
@Data
@Accessors(chain = true)
public class EmuMonitorMetricsVo {

    @Schema(description = "设备故障数量")
    private Long errorCount;

    @Schema(description = "设备正常数量")
    private Long normalCount;

    @Schema(description = "设备离线数量")
    private Long offlineCount;

//    ==== 关键监控数据 ====
    @Schema(description = "实时有功功率，单位: kW")
    private BigDecimal activePower;

    @Schema(description = "实时无功功率，单位: kW")
    private BigDecimal reactivePower;

    @Schema(description = "电池汇总SOC，单位: %")
    private BigDecimal soc;

    @Schema(description = "电池汇总SOH，单位: %")
    private BigDecimal soh;

    @Schema(title = "充放电数据", description = "充放电数据")
    @JsonInclude(Include.NON_NULL)
    private EssChargeTinySummary chargeData;

}
