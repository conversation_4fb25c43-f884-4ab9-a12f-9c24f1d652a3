package com.cdz360.base.model.es.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum PcsStatus implements DcEnum {
    UNKNOWN(-1, "未知"),
    NORMAL(0, "正常"),
    INIT(1, "等待"),   //
    SELF_CHECK(2, "上电自检"),     //
    IDLE(3, "待机"),
    ON_GRID(10, "并网运行"),    //
    OFF_GRID(11, "离网运行"),   //
    PV_CHARGING(12, "光伏充电"),   //

    WARNING(20, "一般错误"),    //
    ERROR(21, "严重错误"),  //
    OFFLINE(22, "离线");

    @JsonValue
    final int code;

    final String desc;

    PcsStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static PcsStatus valueOf(Object codeIn) {
        if (codeIn == null) {
            return PcsStatus.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof PcsStatus) {
            return (PcsStatus) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (PcsStatus type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return PcsStatus.UNKNOWN;
    }
}
