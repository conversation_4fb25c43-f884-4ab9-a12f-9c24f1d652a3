package com.cdz360.base.model.base.param;

import com.cdz360.base.model.base.type.SummaryType;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "数据采样请求参数")
@Data
@Accessors(chain = true)
public class SamplingParam {

    @Schema(description = "采样粒度", requiredMode = RequiredMode.REQUIRED)
    @JsonInclude(Include.NON_NULL)
    private SummaryType summaryType;

    @Schema(description = "查询设备编号")
    @JsonInclude(Include.NON_NULL)
    private String dno;

    @Schema(description = "查询设备编号列表")
    @JsonInclude(Include.NON_NULL)
    private List<String> dnoList;

    @Schema(description = "根据采样粒度进行赋值")
    @JsonInclude(Include.NON_NULL)
    private TimeRange range;
}
