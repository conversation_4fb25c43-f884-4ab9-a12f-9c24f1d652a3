package com.cdz360.base.model.base.dto;

public class ObjectResponse<T> extends BaseResponse {
    private T data;

    public ObjectResponse() {

    }

    public ObjectResponse(int status, String error) {
        super(status, error);
    }

    public ObjectResponse(T data) {
        this.data = data;
    }

    public T getData() {
        return data;
    }

    public ObjectResponse<T> setData(T data) {
        this.data = data;
        return this;
    }
}