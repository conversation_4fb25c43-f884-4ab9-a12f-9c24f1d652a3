package com.cdz360.base.model.es.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Schema(description = "配置下发状态")
@Getter
public enum EquipCfgStatus implements DcEnum {

    UNKNOWN(0, "未知"),
    SEND_TIMEOUT(1, "下发超时"),
    SEND_2_GW(2, "下发中"),
    ARRIVE_GW(3, "已下发到网关"),
    SEND_2_GTI_ERROR(4, "下发到设备失败"),
    ARRIVE_GTI(5, "已下发到设备"),
    ;

    @JsonValue
    private final int code;
    private final String desc;

    EquipCfgStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static EquipCfgStatus valueOf(Object codeIn) {
        if (codeIn == null) {
            return EquipCfgStatus.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof EquipCfgStatus) {
            return (EquipCfgStatus) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (EquipCfgStatus type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return EquipCfgStatus.UNKNOWN;
    }
}
