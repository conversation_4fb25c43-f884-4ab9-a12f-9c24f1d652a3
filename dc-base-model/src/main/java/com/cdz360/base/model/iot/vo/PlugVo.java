package com.cdz360.base.model.iot.vo;

import com.cdz360.base.model.base.annotation.Cache;
import com.cdz360.base.model.base.type.EvseBizStatus;
import com.cdz360.base.model.base.type.EvseProtocolType;
import com.cdz360.base.model.base.type.PlugStatus;
import com.cdz360.base.model.base.type.SupplyType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 枪头(终端)信息
 */
@Data
@Accessors(chain = true)
public class PlugVo implements Serializable {


    private static final long serialVersionUID = 2975555133361132820L;
    /**
     * 网关编号
     */
    @Cache
    @Schema(description = "网关编号")
    private String gwno;
    /**
     * 桩编号
     */
    @Cache
    @Schema(description = "桩编号")
    private String evseNo;

    @Cache
    @Schema(description = "桩名称")
    private String evseName;

    /**
     * 桩号+抢号
     */
    @Cache
    @Schema(description = "桩号+抢号")
    private String plugNo;

    @Cache
    @Schema(description = "桩协议类型")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private EvseProtocolType protocol;



    @Cache
    @Schema(description = "集团商户ID")
    private Long topCommId;

    /**
     * 场站对应的商户ID
     */
    @Cache
    @Schema(description = "场站对应的商户ID")
    private Long siteCommId;


    @Cache
    @Schema(description = "场站对应的商户名称")
    private String siteCommName;

    /**
     * 场站id
     */
    @Cache
    @Schema(description = "场站ID")
    private String siteId;
    /**
     * 场站名称
     */
    @Cache
    @Schema(description = "场站名称")
    private String siteName;
    /**
     * 序号
     */
    @Cache
    @Schema(description = "序号")
    private Integer idx;
    /**
     * 枪头名称
     */
    @Cache
    @Schema(description = "枪头名称")
    private String name;

    @Cache
    @Schema(description = "运营状态")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private EvseBizStatus bizStatus;

    /**
     * 枪头状态
     */
    @Cache
    @Schema(description = "枪头状态")
    private PlugStatus status;


    /**
     * 价格模板ID
     */
    @Cache
    @Schema(description = "价格模板ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long priceCode;

    /**
     * 交直流类型
     */
    @Cache
    @Schema(description = "交直流类型")
    private SupplyType supply;
    /**
     * 进行中订单编号
     */
    @Cache
    @Schema(description = "进行中订单编号")
    private String orderNo;

    @Cache
    @Schema(description = "枪头是否被锁定")
    private Boolean lockStatus;

    @Cache
    @Schema(description = "锁定编号")
    private String lockNo;

    @Cache
    @Schema(description = "枪头二维码")
    private String qrCode;

    /**
     * 枪头温度
     */
    @Cache
    @Schema(description = "枪头温度, 单位'摄氏度'")
    private Integer temperature;

    /**
     * 错误码
     */
    @Cache
    @Schema(description = "错误码")
    private Integer errorCode;

    /**
     * 错误内容
     */
    @Cache
    @Schema(description = "错误内容")
    private String errorMsg;

    /**
     * 告警码
     */
    @Cache
    @Schema(description = "告警码")
    private Integer alertCode;

    /**
     * 电压
     */
    @Cache
    @Schema(description = "最低电压")
    private BigDecimal minVoltage;

    @Cache
    @Schema(description = "最高电压")
    private BigDecimal maxVoltage;
    /**
     * 电流
     */
    @Cache
    @Schema(description = "最低电流")
    private BigDecimal minCurrent;

    @Cache
    @Schema(description = "最高电流")
    private BigDecimal maxCurrent;

    @Cache
    @Schema(description = "枪头额定功率")
    private BigDecimal power;

    @Cache
    @Schema(description = "枪头需求功率")
    private BigDecimal requirePower;

    @Cache
    @Schema(description = "是否支持插枪状态")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean connSupport;

    @Cache
    @Schema(description = "是否支持不拔枪重连")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean constantCharge;

    @Cache
    @Schema(description = "实际(已确认)功率", example= "123")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer actualPower;

    @Cache
    @Schema(description = "分配功率", example= "123")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer assignPower;

    @Cache
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    @Schema(description = "最后更新时间")
    private Date updateTime;

    @Cache
    @Schema(description = "车辆VIN码")
    private String vin;

    @Cache
    @Schema(description = "车位号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String parkNo;

}
