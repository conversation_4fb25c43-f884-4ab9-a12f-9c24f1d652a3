package com.cdz360.base.model.es.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(title = "时段价格配置")
public class EssPriceDto {

    @Schema(title = "计费模板ID")
    private Long cfgId;

    @Schema(title = "计费模板编号", description = "计费模板修改后编号不变,用于追踪计费模板的修改")
    private String cfgNo;

    @Schema(title = "计费模板名称")
    private String name;


    private List<EssPriceItem> items = new ArrayList<>();


}
