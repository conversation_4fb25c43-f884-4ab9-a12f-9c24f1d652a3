package com.cdz360.base.model.es.vo.hi;

import com.cdz360.base.model.es.type.PcsWorkMode;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "系统设置")
@Data
@Accessors(chain = true)
@JsonInclude(Include.NON_NULL)
public class InverterSysData {

    @Schema(description = "工作模式设置 0x300C")
    @JsonProperty("wms")
    private PcsWorkMode workModeSetting;

    @Schema(description = "主动充放电功率 0x304C")
    @JsonProperty("ap")
    private BigDecimal activePower;

    @Schema(description = "其他时段工作模式 0x314E")
    @JsonProperty("os")
    private Integer otherStrategy;

}
