package com.cdz360.base.model.base.exception;

import com.cdz360.base.model.base.constants.DcConstants;
import org.slf4j.event.Level;

public class DcTokenException extends DcException {

    private static final int STATUS = DcConstants.KEY_RES_CODE_TOKEN_ERROR;
    private String gwno;

    public DcTokenException(String msg) {
        super(STATUS, msg);
    }

    public DcTokenException(String msg, Level logLevel) {
        super(STATUS, msg, logLevel);
    }


    public DcTokenException(String msg, Level logLevel, Throwable e) {
        super(STATUS, msg, logLevel, e);
    }

    public DcTokenException(int status, String msg) {
        super(status, msg);
    }

    public DcTokenException(String gwno, String msg) {
        super(STATUS, msg);
        this.gwno = gwno;
    }

    public String getGwno() {
        return gwno;
    }

    public DcTokenException setGwno(String gwno) {
        this.gwno = gwno;
        return this;
    }
}
