package com.cdz360.base.model.es.vo;


import com.cdz360.base.model.es.type.ChargeFlowType;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(title = "时段的计费数据")
public class EssFeeItem {


    @Schema(title = "时段开始时间", description = "单位‘分钟’， 0 ~ 1440")
    @JsonInclude(Include.NON_NULL)
    private int startTime;

    @Schema(title = "时段结束时间", description = "单位‘分钟’， 0 ~ 1440")
    @JsonInclude(Include.NON_NULL)
    private int endTime;

    @Schema(title = "计费模板ID")
    @JsonInclude(Include.NON_NULL)
    private Long priceId;

    @Schema(title = "时段电价", description = "单位: 元")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal price;

    @JsonInclude(Include.NON_EMPTY)
    @Schema(title = "时段电价名称", description = "priceItemName, 尖、锋、平、谷, 名称由用户设置自定义")
    private String piName;

    @Schema(title = "充/放电类型", description = "")
    @JsonInclude(Include.NON_NULL)
    private ChargeFlowType type;

    @Schema(title = "充电电量", description = "单位 kwh")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal inKwh;

    @Schema(title = "充电金额", description = "单位: 元")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal inFee;

    @Schema(title = "充电单价", description = "单位: 元/kwh")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal inPrice;


    @Schema(title = "开始充电电量", description = "时段开始时的总充电电量, 单位 kwh")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal totalInKwh;

    @Schema(title = "放电电量", description = "单位 kwh")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal outKwh;


    @Schema(title = "放电金额", description = "单位: 元")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal outFee;


    @Schema(title = "放电单价", description = "单位: 元/kwh")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal outPrice;


    @Schema(title = "开始放电电量", description = "时段开始时的总放电电量, 单位 kwh")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal totalOutKwh;

}
