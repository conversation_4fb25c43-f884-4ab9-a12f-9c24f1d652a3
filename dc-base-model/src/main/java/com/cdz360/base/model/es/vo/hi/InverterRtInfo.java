package com.cdz360.base.model.es.vo.hi;

import com.cdz360.base.model.es.type.hi.BatOpStatus;
import com.cdz360.base.model.es.type.hi.DcacStatus;
import com.cdz360.base.model.es.type.hi.HybridInverterOpStatus;
import com.cdz360.base.model.es.type.hi.HybridInverterVendor;
import com.cdz360.base.model.es.type.hi.PvOpStatus;
import com.cdz360.base.model.es.type.hi.SinexcelErrorCode;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@JsonInclude(Include.NON_NULL)
public class InverterRtInfo {

//    private HybridInverterVendor vendor;

    /**
     * 0：不需要初始化 1：需要初始化
     */
    @JsonProperty("if")
    private Boolean initFlag;

    @JsonProperty("hios")
    private HybridInverterOpStatus hybridInverterOpStatus;

    /**
     * 光伏运行状态
     */
    @JsonProperty("pos")
    private List<PvOpStatus> pvOpStatus;

    /**
     * 电池运行状态
     */
    @JsonProperty("bos")
    private List<BatOpStatus> batOpStatus;

    /**
     * DRED状态
     */
    @JsonProperty("ds")
    private Integer dredStatus;

    /**
     * DCDC状态
     */
    @JsonProperty("dds")
    private DcacStatus DcdcStatus;

    /**
     * DCAC状态
     */
    @JsonProperty("das")
    private DcacStatus DcacStatus;

    /**
     * 盛弘逆变器告警
     */
    @JsonProperty("siErrors")
    private List<SinexcelErrorCode> sinexcelInverterErrorList;

    /**
     * 盛弘整流器告警
     */
    @JsonProperty("srErrors")
    private List<SinexcelErrorCode> sinexcelRectifierErrorList;

    /**
     * 盛弘监控告警
     */
    @JsonProperty("smErrors")
    private List<SinexcelErrorCode> sinexcelMonitorErrorList;

    public InverterRtInfo() {
    }

    public InverterRtInfo(HybridInverterVendor vendor) {
//        this.vendor = vendor;
    }

}
