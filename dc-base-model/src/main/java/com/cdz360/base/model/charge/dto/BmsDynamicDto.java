package com.cdz360.base.model.charge.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@Schema(description = "BMS动态信息")
public class BmsDynamicDto {
    @Schema(description = "BMS需求电压， 单位 V", example = "11.22")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal needVoltage;

    @Schema(description = "BMS需求电流, 单位 A", example = "11.22")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal needCurrent;

    @Schema(description = "BMS充电电压测量值， 单位 V", example = "11.22")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal voltage;

    @Schema(description = "BMS充电电流测量值, 单位 A", example = "11.22")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal current;
}
