package com.cdz360.base.model.es.vo;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(title = "充放电缩略信息", description = "充放电缩略信息")
public class EssChargeTinySummary {

    @Schema(title = "今日充电量", description = "今日充电量,kwh")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal inKwh;


    @Schema(title = "总充电量", description = "总充电量,kwh")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal totalInKwh;

    @Schema(title = "今日放电量", description = "今日放电量,kwh")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal outKwh;

    @Schema(title = "总放电量", description = "总放电量,kwh")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal totalOutKwh;
}
