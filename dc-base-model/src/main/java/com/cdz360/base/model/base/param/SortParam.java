package com.cdz360.base.model.base.param;

import com.cdz360.base.model.base.type.OrderType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

@Schema(description = "排序参数")
@Data
@Accessors(chain = true)
public class SortParam {

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "排序列", example = "[\"aaa\", \"bbb\"]")
    private List<String> columns = new ArrayList<>();

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "排序方式. asc, 正序; desc, 倒序", example = "desc")
    private OrderType order = OrderType.desc;


    public static SortParam as(String column, OrderType order) {
        return SortParam.buildSort(order).setColumns(List.of(column));
    }

    public static SortParam as(String col1, String col2, OrderType order) {
        return SortParam.buildSort(order).setColumns(List.of(col1, col2));
    }

    public static SortParam as(String col1, String col2, String col3, OrderType order) {
        return SortParam.buildSort(order).setColumns(List.of(col1, col2, col3));
    }

    private static SortParam buildSort(OrderType order) {
        SortParam sort = new SortParam();
        sort.order = order;
        return sort;
    }


    @JsonIgnore
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    public String getColumnsString() {
        if (columns == null) {
            return "";
        }
        return String.join(",", this.columns);
        //return CollectionUtils.join(this.columns, ",");
    }

}
