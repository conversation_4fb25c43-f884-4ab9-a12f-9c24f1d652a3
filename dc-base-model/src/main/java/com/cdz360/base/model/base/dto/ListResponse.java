package com.cdz360.base.model.base.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.ArrayList;
import java.util.List;

public class ListResponse<T> extends BaseResponse {
    private List<T> data;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long total;

    public ListResponse() {
        this.data = new ArrayList<>();
    }

    public ListResponse(int status, String error) {
        super(status, error);
        this.data = new ArrayList<>();
    }

    public ListResponse(List<T> data) {
        this.data = data;
    }

    public ListResponse(List<T> data, Long total) {
        this.data = data;
        this.total = total;
    }

    public List<T> getData() {
        return data;
    }

    public ListResponse<T> setData(List<T> data) {
        this.data = data;
        return this;
    }

    public Long getTotal() {
        return total;
    }

    public ListResponse<T> setTotal(Long total) {
        this.total = total;
        return this;
    }
}
