package com.cdz360.base.model.es.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum BmsStackStatus implements DcEnum {
    UNKNOWN(-1, "未知", "未知"),
    INIT(0, "初始化", "初始化状态"),

    READY(1, "就绪", "继电器闭合，未充放电"),

    DISCHARGE(2, "放电", "放电"),

    CHARGE(3, "充电", "充电"),
//    WARNING(4, "告警", "告警"),
//    ERROR(5, "故障", "故障"),
    DISABLED(6, "未使能", "未使能"),
    ;

    @JsonValue
    final int code;

    final String name;

    final String desc;

    BmsStackStatus(int code, String name, String desc) {
        this.code = code;
        this.name = name;
        this.desc = desc;
    }

    @JsonCreator
    public static BmsStackStatus valueOf(Object codeIn) {
        if (codeIn == null) {
            return BmsStackStatus.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof BmsStackStatus) {
            return (BmsStackStatus) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (BmsStackStatus type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return BmsStackStatus.UNKNOWN;
    }
}
