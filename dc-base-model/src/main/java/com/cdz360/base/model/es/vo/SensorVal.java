package com.cdz360.base.model.es.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 温度、湿度传感器数据
 */
@Accessors(chain = true)
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class SensorVal extends EssBaseVal<BigDecimal> {

    @Schema(title = "显示的单位", description = "℃、%、V、A")
    @JsonInclude(Include.NON_EMPTY)
    private String unit;

    public SensorVal(int code, Integer addr, String name, BigDecimal v, String desc, String unit) {
        super(code, addr, name, v, desc);
        this.unit = unit;
    }
//    @Schema(title = "编码", description = "XxxKvCode.code")
//    private int code;
//
//    @Schema(title = "寄存器地址", description = "寄存器地址")
//    private Integer addr;
//
//    @Schema(title = "寄存器名称", description = "寄存器名称")
//    @JsonInclude(Include.NON_EMPTY)
//    private String name;
//
//    private BigDecimal v;

    public SensorVal() {
        super();
    }

//    @JsonInclude(Include.NON_EMPTY)
//    private String desc;
}
