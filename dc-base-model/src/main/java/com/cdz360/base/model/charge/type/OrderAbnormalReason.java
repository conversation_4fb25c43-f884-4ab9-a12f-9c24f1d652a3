package com.cdz360.base.model.charge.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 异常原因
 */
@Getter
public enum OrderAbnormalReason implements DcEnum {
    UNKNOWN(0, "未知"),

    HB_TIMEOUT(1, "心跳超时"),

    ORDER_UPDATE_TIMEOUT(2, "订单更新超时"),

    ORDER_INVALID_KWH(3, "订单电量越限"),

    ORDER_INVALID_PRICE(4, "订单金额越限"),

    ORDER_CHARGING_TIMEOUT(5, "充电中超时"),

    ORDER_START_TIMEOUT(6, "启动中超时"),

    ORDER_ABNORMAL_STOP(7, "充电中停用"),

    REFUND_FAIL(8, "退款失败"),

    PAY_FAIL(9, "支付失败"),

    ORDER_FROZEN_ERROR(10, "订单冻结金额异常"),

    ACCOUNT_FAIL(11, "账户异常"),

    ORDER_ELEC_ERROR(12, "订单电量异常"),

    ORDER_FEE_ERROR(13, "订单金额异常"),

    ORDER_INVALID_DURATION(14, "订单时长越限"),

    ;


    @JsonValue
    private final int code;
    private final String desc;

    OrderAbnormalReason(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static OrderAbnormalReason valueOf(Object codeIn) {
        if (codeIn == null) {
            return OrderAbnormalReason.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof OrderAbnormalReason) {
            return (OrderAbnormalReason) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (OrderAbnormalReason status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return OrderAbnormalReason.UNKNOWN;
    }
}