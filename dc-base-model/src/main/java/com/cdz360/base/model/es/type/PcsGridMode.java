package com.cdz360.base.model.es.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * PCS 并/离网模式
 */
@Getter
public enum PcsGridMode implements DcEnum {

    UNKNOWN(0),

    ON_GRID(1), // 并网

    OFF_GRID(2),    // 离网
    INIT(3); // 初始化

    @JsonValue
    final int code;

    PcsGridMode(int code) {
        this.code = code;
    }

    @JsonCreator
    public static PcsGridMode valueOf(Object codeIn) {
        if (codeIn == null) {
            return PcsGridMode.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof PcsGridMode) {
            return (PcsGridMode) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (PcsGridMode type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return PcsGridMode.UNKNOWN;
    }
}
