package com.cdz360.base.model.es.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.Setter;

@Getter
public enum LiquidAlarmCode implements DcEnum, EssBaseAlarmCode {
    UNKNOWN(0, "未知错误", ""),

    OFFLINE(1, "离线", "设备离线"),

    ;

    @JsonValue
    final int code;

    final String msg;

    final String desc;
    @Setter
    private Integer level;

    LiquidAlarmCode(int code, String msg, String desc) {
        this.code = code;
        this.msg = msg;
        this.desc = desc;
    }


    @JsonCreator
    public static LiquidAlarmCode valueOf(Object codeIn) {
        if (codeIn == null) {
            return LiquidAlarmCode.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof LiquidAlarmCode) {
            return (LiquidAlarmCode) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (LiquidAlarmCode type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return LiquidAlarmCode.UNKNOWN;
    }
    
}
