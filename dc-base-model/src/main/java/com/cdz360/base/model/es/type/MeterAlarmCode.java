package com.cdz360.base.model.es.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.Setter;

@Getter
public enum MeterAlarmCode implements DcEnum , EssBaseAlarmCode {
    UNKNOWN(0, "未知错误", ""),

    OFFLINE(1, "离线", "设备离线"),

    ;

    @JsonValue
    final int code;

    final String msg;

    final String desc;
    @Setter
    private Integer level;

    MeterAlarmCode(int code, String msg, String desc) {
        this.code = code;
        this.msg = msg;
        this.desc = desc;
    }


    @JsonCreator
    public static MeterAlarmCode valueOf(Object codeIn) {
        if (codeIn == null) {
            return MeterAlarmCode.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof MeterAlarmCode) {
            return (MeterAlarmCode) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (MeterAlarmCode type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return MeterAlarmCode.UNKNOWN;
    }
    
}
