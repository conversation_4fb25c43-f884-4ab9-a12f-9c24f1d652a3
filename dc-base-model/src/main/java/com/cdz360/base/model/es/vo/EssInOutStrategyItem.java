package com.cdz360.base.model.es.vo;

import com.cdz360.base.model.es.type.ChargeFlowType;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class EssInOutStrategyItem {
    @Schema(title = "时段开始时间", description = "单位‘分钟’， 0 ~ 1440")
    private int start;

    @Schema(title = "时段结束时间", description = "单位‘分钟’， 0 ~ 1440")
    private int end;


    @Schema(title = "工作模式", description = "待机/充电/放电")
    private ChargeFlowType flowType;

    @Schema(title = "最大充放电功率", description = "该时段的最大充放电功率,单位 kW")
    private BigDecimal activePower;

    @Schema(title = "限制SOC", description = "充电时段为最大SOC，放电时段为最小SOC")
    private Integer soc;
}
