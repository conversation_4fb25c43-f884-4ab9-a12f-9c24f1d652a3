package com.cdz360.base.model.charge.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@Schema(description = "电池动态信息")
public class BatteryDynamicDto {

    @Schema(description = "电池单体最高电压， 单位 V", example = "11.22")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal maxVoltage;


    @Schema(description = "电池单体最高电压所在组号", example = "11")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer maxVoltageGroupId;

    @Schema(description = "电池单体最高电压所在编号", example = "11")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer maxVoltageUnitId;

    @Schema(description = "电池单体最低电压， 单位 V", example = "11.22")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal minVoltage;

    @Schema(description = "电池温度, 单位'摄氏度'", example = "11")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer temp;

    @Schema(description = "最高电池单体温度", example = "11")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer maxTemp;

    @Schema(description = "最高温度检测点编号", example = "11")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer maxTempUnitId;

    @Schema(description = "最低电池单体温度", example = "11")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer minTemp;

    @Schema(description = "最低温度检测点编号", example = "11")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer minTempUnitId;
}
