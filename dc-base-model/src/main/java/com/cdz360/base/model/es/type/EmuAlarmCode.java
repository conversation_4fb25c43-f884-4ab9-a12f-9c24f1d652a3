package com.cdz360.base.model.es.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.Setter;

@Getter
public enum EmuAlarmCode implements DcEnum, EssBaseAlarmCode {
    UNKNOWN(0, "未知错误", ""),

    OFFLINE(1, "离线", "设备离线"),

    WATER(50, "水浸报警", ""),
    FIRE(55, "消防报警", ""),

    FIRE_WARN(56, "消防故障", ""),
    SPD(60, "浪涌保护器报警", ""),
    EMERGENCY_STOP(65, "急停故障", ""),
    DOOR_OPEN(70, "行程开关报警", "");

    @JsonValue
    final int code;

    final String msg;

    final String desc;
    @Setter
    private Integer level;

    EmuAlarmCode(int code, String msg, String desc) {
        this.code = code;
        this.msg = msg;
        this.desc = desc;
    }


    @JsonCreator
    public static EmuAlarmCode valueOf(Object codeIn) {
        if (codeIn == null) {
            return EmuAlarmCode.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof EmuAlarmCode) {
            return (EmuAlarmCode) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (EmuAlarmCode type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return EmuAlarmCode.UNKNOWN;
    }

}
