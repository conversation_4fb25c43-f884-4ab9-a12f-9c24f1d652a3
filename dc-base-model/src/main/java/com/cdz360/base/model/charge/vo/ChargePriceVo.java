package com.cdz360.base.model.charge.vo;


import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 电费模板信息
 */
@Data
@Accessors(chain = true)
@Schema(description = "电费模板信息")
public class ChargePriceVo implements Serializable {
    private static final long serialVersionUID = -8209265692807408585L;

    @Schema(description = "价格模板ID")
    private Long id;

    @Schema(description = "价格模板编码, 价格模板发生变更后, code保持不变, ID会变")
    private String code;

    @Schema(description = "价格模板版本号, 每次变更会增加")
    private Integer version;

    @Schema(description = "价格模板名称")
    private String name;


    @Schema(description = "是否有效")
    private boolean enable;

    @Schema(description = "分时价格信息")
    private List<ChargePriceItem> itemList;


}
