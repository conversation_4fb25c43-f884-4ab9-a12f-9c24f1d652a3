package com.cdz360.base.model.charge.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 动态功率分配模式(有序充电)
 */
@Getter
public enum SiteDynamicPowerType implements DcEnum {
    NONE(0, "无功率分配"),

    FIFO(1, "先到先充"),

    BALANCE(2, "均衡模式"),

    PRIORITY(3, "优先级模式");

    @JsonValue
    private final int code;
    private final String desc;


    SiteDynamicPowerType(int code, String desc) {
        this.code = code;
        this.desc = desc;

    }

    @JsonCreator
    public static SiteDynamicPowerType valueOf(Object codeIn) {
        if (codeIn == null) {
            return SiteDynamicPowerType.NONE;
        }
        int code = 0;
        if (codeIn instanceof SiteDynamicPowerType) {
            return (SiteDynamicPowerType) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (SiteDynamicPowerType status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return SiteDynamicPowerType.NONE;
    }
}
