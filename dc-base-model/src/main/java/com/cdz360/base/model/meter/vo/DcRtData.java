package com.cdz360.base.model.meter.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 直流数据
 */
@Data
public class DcRtData {

    @Schema(title = "母线电压", description = "母线电压")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal busVoltage;

    @Schema(description = "正母线电压")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal positiveBusVoltage;

    @Schema(description = "负母线电压")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal negativeBusVoltage;


    @Schema(title = "电池侧电压", description = "电池侧电压")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal voltage;


    @Schema(title = "电池侧电流", description = "电池侧电流")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal current;


    @Schema(title = "电池侧功率", description = "电池侧功率")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal power;

}
