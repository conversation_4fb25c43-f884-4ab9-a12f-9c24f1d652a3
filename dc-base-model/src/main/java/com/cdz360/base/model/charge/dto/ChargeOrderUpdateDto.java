package com.cdz360.base.model.charge.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 用于传递订单更新信息的对象，主要应用场景: 将非即时性数据落库
 */
@Data
@Accessors(chain = true)
public class ChargeOrderUpdateDto {

    private String orderNo;

    private OrderTimeUpdateDto time;


    @Data
    @Accessors(chain = true)
    public static class OrderTimeUpdateDto{
        @Schema(description = "订单创建时间")
        private Date createTime;

        @Schema(description = "充电开始时间")
        private Date chargeStartTime;

        @Schema(description = "充电结束时间")
        private Date chargeEndTime;

        @Schema(description = "订单结束时间")
        private Date stopTime;

        @Schema(description = "结算时间")
        private Date payTime;
    }
}
