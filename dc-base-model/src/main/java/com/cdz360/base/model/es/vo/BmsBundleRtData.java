package com.cdz360.base.model.es.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;


@Data
@Accessors(chain = true)
@Schema(title = "电池簇统计信息")
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@JsonInclude(Include.NON_NULL)
public class BmsBundleRtData extends EssBaseRtData {

    @Schema(title = "电池堆设备序列号")
    @JsonInclude(Include.NON_EMPTY)
    private String stackDno;


    @Schema(title = "BMS设备序列号")
    @JsonInclude(Include.NON_EMPTY)
    private String bmsDno;


    @Schema(title = "电池蔟在电池堆内的序号")
    @JsonInclude(Include.NON_NULL)
    private Integer idx;

    /**
     * 簇总压
     */
    @JsonProperty(value = "v")
    @JsonInclude(Include.NON_NULL)
    @Schema(title = "簇总压", description = "单位v")
    private BigDecimal voltage;

    /**
     * 簇总电流
     */
    @JsonProperty(value = "i")
    @JsonInclude(Include.NON_NULL)
    @Schema(title = "簇总电流", description = "单位A")
    private BigDecimal current;

    @Schema(title = "簇SOC")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal soc;

    @Schema(title = "簇SOH")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal soh;

//    @Deprecated
//    @JsonProperty(value = "ipr")
//    @Schema(title = "簇绝缘正电阻", description = "单位KΩ")
//    @JsonInclude(Include.NON_NULL)
//    private BigDecimal isolatePositiveResistance;
//
//
//    @Deprecated
//    @JsonProperty(value = "inr")
//    @Schema(title = "簇绝缘负电阻", description = "单位KΩ")
//    @JsonInclude(Include.NON_NULL)
//    private BigDecimal isolateNegativeResistance;


    @Schema(title = "最高单体电压")
    @JsonInclude(Include.NON_NULL)
    private BmuSummary maxBatteryVoltage;


    @Schema(title = "最低单体电压")
    @JsonInclude(Include.NON_NULL)
    private BmuSummary minBatteryVoltage;


    @Schema(title = "最高单体温度")
    @JsonInclude(Include.NON_NULL)
    private BmuSummary maxBatteryTemp;


    @Schema(title = "最低单体温度")
    @JsonInclude(Include.NON_NULL)
    private BmuSummary minBatteryTemp;

//    @Deprecated
//    @Schema(title = "最大单体压差值")
//    @JsonInclude(Include.NON_NULL)
//    private BigDecimal maxVoltageDiff;
//
//    @Deprecated
//    @Schema(title = "平均单体电压")
//    @JsonInclude(Include.NON_NULL)
//    private BigDecimal avgVoltage;
//
//    @Deprecated
//    @Schema(title = "最大温度差值")
//    @JsonInclude(Include.NON_NULL)
//    private BigDecimal maxTempDiff;
//
//    @Deprecated
//    @Schema(title = "平均温度")
//    @JsonInclude(Include.NON_NULL)
//    private BigDecimal avgTemp;

    @Schema(title = "充电限制电流")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal chargeLimitCurrent;

    @Schema(title = "放电限制电流")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal dischargeLimitCurrent;

//    @Deprecated
//    @Schema(title = "簇充放电循环次数")
//    @JsonInclude(Include.NON_NULL)
//    private Integer chargeNum;
//
//    @Deprecated
//    @Schema(title = "簇单次充电电量")
//    @JsonInclude(Include.NON_NULL)
//    private BigDecimal inKwh;
//
//    @Deprecated
//    @Schema(title = "簇单次放电电量")
//    @JsonInclude(Include.NON_NULL)
//    private BigDecimal outKwh;
//
//    @Deprecated
//    @Schema(title = "簇累计充电电量")
//    @JsonInclude(Include.NON_NULL)
//    private BigDecimal totalInKwh;
//
//
//    @Deprecated
//    @Schema(title = "簇累计放电电量")
//    @JsonInclude(Include.NON_NULL)
//    private BigDecimal totalOutKwh;

//    @Schema(title = "传感器数据", description = "电压、温度等")
//    private List<SensorVal> sensorValues;


    @Schema(title = "电池簇单体电压详细信息")
    @JsonInclude(Include.NON_NULL)
    private List<BigDecimal> batteryVoltages;


    @Schema(title = "电池簇单体温度详细信息")
    @JsonInclude(Include.NON_NULL)
    private List<BigDecimal> batteryTemps;

    @Data
    public static class BmuSummary {


        @Schema(title = "BMU号")
        @JsonInclude(Include.NON_NULL)
        private Integer bmuId;

        @Schema(title = "LMU号")
        @JsonInclude(Include.NON_NULL)
        private Integer lmuId;


        @Schema(title = "箱内序号")
        @JsonInclude(Include.NON_NULL)
        private Integer inboxId;

        @Schema(title = "值")
        @JsonInclude(Include.NON_NULL)
        private BigDecimal val;
    }
}
