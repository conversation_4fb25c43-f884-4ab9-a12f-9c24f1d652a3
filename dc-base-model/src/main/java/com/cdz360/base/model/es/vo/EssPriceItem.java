package com.cdz360.base.model.es.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class EssPriceItem {

    /**
     * 时段开始时间，单位‘分钟’， 0 ~ 1440
     */
    @Schema(title = "时段开始时间", description = "单位‘分钟’， 0 ~ 1440")
    private int start;

    /**
     * 时段结束时间，单位‘分钟’， 0 ~ 1440
     */
    @Schema(title = "时段结束时间", description = "单位‘分钟’， 0 ~ 1440")
    private int end;

    @Schema(title = "时段名称")
    private String name;

    /**
     * 时段电价
     */
    @Schema(title = "时段电价", description = "单位‘元’")
    private BigDecimal price;
}
