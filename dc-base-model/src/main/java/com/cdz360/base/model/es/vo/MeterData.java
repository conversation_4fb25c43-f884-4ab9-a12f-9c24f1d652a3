package com.cdz360.base.model.es.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "电表信息")
@Data
@Accessors(chain = true)
public class MeterData {

    // 位置信息

    //    0x1240	4	购电度数	kW.h		测量-电表
    //    0x1242	4	馈电度数	kW.h		测量-电表
    @Schema(description = "购电度数,单位: kW.h")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal purchaseKwh;

    @Schema(description = "馈电度数,单位: kW.h")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal offerKwh;
}
