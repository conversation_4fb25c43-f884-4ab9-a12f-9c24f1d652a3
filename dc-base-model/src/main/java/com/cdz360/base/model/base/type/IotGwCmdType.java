package com.cdz360.base.model.base.type;

/**
 * 网关下行指令
 */
public enum IotGwCmdType {
    CHARGE_START,   // 开启充电
    CHARGE_STOP,    // 停止充电
    EVSE_GET_CFG,   // 获取桩配置
    EVSE_CFG,       // 更新桩配置
    TUNNEL_START,   // 开启网关远程debug通道
    TUNNEL_STOP,    // 关闭网关远程debug通道
    EVSE_UPGRADE,   // 桩远程升级
    EVSE_REBOOT,    // 桩远程重启
    EVSE_DEBUG,   // 桩远程debug指令
    EVSE_GET_MODULE, // 获取桩模块信息
    CHARGE_SOC_CTRL,//调整SOC限制
    TRANSFORMER_UPDATE, // 变压器更新

}
