package com.cdz360.base.model.es.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 除湿器 dehumidifier
 */
@Data
@Accessors(chain = true)
@Schema(title = "空调/除湿实时数据")
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class DehRtData extends EssBaseRtData {

//    /**
//     * @deprecated 使用 sensorValues 替换
//     */
//    @Deprecated
//    @Schema(title = "控制器内部", description = "单位℃")
//    private BigDecimal dehTemp;
//
//    /**
//     * @deprecated 使用 sensorValues 替换
//     */
//    @Deprecated
//    @Schema(title = "当前温度", description = "单位℃")
//    private BigDecimal temp;
//
//    /**
//     * @deprecated 使用 sensorValues 替换
//     */
//    @Deprecated
//    @Schema(title = "当前湿度", description = "单位RH%")
//    private BigDecimal humidity;
//
//
//    @Schema(title = "传感器数据", description = "温度、湿度")
//    private List<SensorVal> sensorValues;

}
