package com.cdz360.base.model.es.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ToString
@EqualsAndHashCode
public class EssBaseRtData {

    @Schema(title = "设备号", description = "自身设备编号")
    @JsonInclude(Include.NON_EMPTY)
    private String dno;

    @Schema(title = "设备所在的储能设备号", description = "设备所在的储能设备号")
    @JsonInclude(Include.NON_EMPTY)
    private String essDno;

    @Schema(title = "时间戳", description = "数据采集的unix时间戳")
    @JsonInclude(Include.NON_NULL)
    private Long ts;

    @Schema(title = "时区", description = "时区", example = "+8")
    @JsonInclude(Include.NON_EMPTY)
    private String tz;

    @Schema(title = "设备本地时间", description = "数据采集的EMU本地时间")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonInclude(Include.NON_NULL)
    private LocalDateTime ldt;

    @Schema(title = "传感器数据", description = "各种电流、电压数据")
    private List<SensorVal> sensors;
}
