package com.cdz360.base.model.base.exception;

import com.cdz360.base.model.base.constants.DcConstants;
import org.slf4j.event.Level;

public class DcArgumentException extends DcException {

    private static final int STATUS = DcConstants.KEY_RES_CODE_ARGUMENT_ERROR;

    public DcArgumentException() {
        super(STATUS);
    }


    public DcArgumentException(String msg) {
        super(STATUS, msg);
    }

    public DcArgumentException(String msg, Level logLevel) {
        super(STATUS, msg, logLevel);
    }


    public DcArgumentException(String msg, Level logLevel, Throwable e) {
        super(STATUS, msg, logLevel, e);
    }

    public DcArgumentException(int status, String msg) {
        super(status, msg);
    }
}
