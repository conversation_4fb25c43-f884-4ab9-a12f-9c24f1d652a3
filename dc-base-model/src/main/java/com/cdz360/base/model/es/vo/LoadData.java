package com.cdz360.base.model.es.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "负载信息")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class LoadData extends ThreePhaseData {

    //    //    0x1046	4	负载A相电压	V		测量-负载
//    //    0x1048	4	负载B相电压	V		测量-负载
//    //    0x104A	4	负载C相电压	V		测量-负载
//    @Schema(description = "负载A相电压，单位: V")
//    private BigDecimal phaseAVoltage;
//    @Schema(description = "负载B相电压，单位: V")
//    private BigDecimal phaseBVoltage;
//    @Schema(description = "负载C相电压，单位: V")
//    private BigDecimal phaseCVoltage;
//    //    0x104C	4	负载AB线电压	V		测量-负载
//    //    0x104E	4	负载BC线电压	V		测量-负载
//    //    0x1050	4	负载CA线电压	V		测量-负载
//    @Schema(description = "负载AB线电压，单位: V")
//    private BigDecimal lineABVoltage;
//    @Schema(description = "负载BC线电压，单位: V")
//    private BigDecimal lineBCVoltage;
//    @Schema(description = "负载CA线电压，单位: V")
//    private BigDecimal lineCAVoltage;
//    //    0x1052	4	负载A相THDU	%		测量-负载
//    //    0x1054	4	负载B相THDU	%		测量-负载
//    //    0x1056	4	负载C相THDU	%		测量-负载
//    @Schema(description = "负载A相THDU，单位: %")
//    private BigDecimal phaseATHDU;
//    @Schema(description = "负载B相THDU，单位: %")
//    private BigDecimal phaseBTHDU;
//    @Schema(description = "负载C相THDU，单位: %")
//    private BigDecimal phaseCTHDU;
//    //    0x1058	4	负载电压频率	Hz		测量-负载
//    @Schema(description = "负载电压频率，单位: Hz")
//    private BigDecimal voltageFrequency;
//    //    0x105A	4	负载A相电流有效值	A		测量-负载
//    //    0x105C	4	负载B相电流有效值	A		测量-负载
//    //    0x105E	4	负载C相电流有效值	A		测量-负载
//    //    0x1060	4	负载N线电流有效值	A		测量-负载
//    @Schema(description = "负载A相电流有效值，单位: A")
//    private BigDecimal phaseACurrent;
//    @Schema(description = "负载B相电流有效值，单位: A")
//    private BigDecimal phaseBCurrent;
//    @Schema(description = "负载C相电流有效值，单位: A")
//    private BigDecimal phaseCCurrent;
//    @Schema(description = "负载N线电流有效值，单位: A")
//    private BigDecimal phaseNCurrent;
//    //    0x1062	4	负载A相电流THD	%		测量-负载
//    //    0x1064	4	负载B相电流THD	%		测量-负载
//    //    0x1066	4	负载C相电流THD	%		测量-负载
//    @Schema(description = "负载A相电流THD，单位: %")
//    private BigDecimal phaseACurrentTHD;
//    @Schema(description = "负载B相电流THD，单位: %")
//    private BigDecimal phaseBCurrentTHD;
//    @Schema(description = "负载C相电流THD，单位: %")
//    private BigDecimal phaseCCurrentTHD;
//    //    0x1068	4	负载A相电流峰值比	%		测量-负载
//    //    0x106A	4	负载B相电流峰值比	%		测量-负载
//    //    0x106C	4	负载C相电流峰值比	%		测量-负载
//    @Schema(description = "负载A相电流峰值比，单位: %")
//    private BigDecimal phaseACurrentPeakRatio;
//    @Schema(description = "负载B相电流峰值比，单位: %")
//    private BigDecimal phaseBCurrentPeakRatio;
//    @Schema(description = "负载C相电流峰值比，单位: %")
//    private BigDecimal phaseCCurrentPeakRatio;
//    //    0x106E	4	负载A相视在功率	kVA		测量-负载
//    //    0x1070	4	负载B相视在功率	kVA		测量-负载
//    //    0x1072	4	负载C相视在功率	kVA		测量-负载
//    @Schema(description = "负载A相视在功率，单位: kVA")
//    private BigDecimal phaseAApparentPower;
//    @Schema(description = "负载B相视在功率，单位: kVA")
//    private BigDecimal phaseBApparentPower;
//    @Schema(description = "负载C相视在功率，单位: kVA")
//    private BigDecimal phaseCApparentPower;
//    //    0x1074	4	负载A相有功功率	kW		测量-负载
//    //    0x1076	4	负载B相有功功率	kW		测量-负载
//    //    0x1078	4	负载C相有功功率	kW		测量-负载
//    @Schema(description = "负载A相有功功率，单位: kW")
//    private BigDecimal phaseAActivePower;
//    @Schema(description = "负载B相有功功率，单位: kW")
//    private BigDecimal phaseBActivePower;
//    @Schema(description = "负载C相有功功率，单位: kW")
//    private BigDecimal phaseCActivePower;
//    //    0x107A	4	负载A相无功功率	kVar		测量-负载
//    //    0x107C	4	负载B相无功功率	kVar		测量-负载
//    //    0x107E	4	负载C相无功功率	kVar		测量-负载
//    @Schema(description = "负载A相无功功率，单位: kVar")
//    private BigDecimal phaseAReactivePower;
//    @Schema(description = "负载B相无功功率，单位: kVar")
//    private BigDecimal phaseBReactivePower;
//    @Schema(description = "负载C相无功功率，单位: kVar")
//    private BigDecimal phaseCReactivePower;
//    //    0x1080	4	负载A相基波功率因数			测量-负载
//    //    0x1082	4	负载B相基波功率因数			测量-负载
//    //    0x1084	4	负载C相基波功率因数			测量-负载
//    @Schema(description = "负载A相基波功率因数")
//    private BigDecimal phaseAFactorFundamental;
//    @Schema(description = "负载B相基波功率因数")
//    private BigDecimal phaseBFactorFundamental;
//    @Schema(description = "负载C相基波功率因数")
//    private BigDecimal phaseCFactorFundamental;
//    //    0x1086	4	负载A相功率因数			测量-负载
//    //    0x1088	4	负载B相功率因数			测量-负载
//    //    0x108A	4	负载C相功率因数			测量-负载
//    @Schema(description = "负载A相功率因数")
//    private BigDecimal phaseAPowerFactor;
//    @Schema(description = "负载B相功率因数")
//    private BigDecimal phaseBPowerFactor;
//    @Schema(description = "负载C相功率因数")
//    private BigDecimal phaseCPowerFactor;
    //    0x108C	4	负载A相负载率	%		测量-负载
    //    0x108E	4	负载B相负载率	%		测量-负载
    //    0x1090	4	负载C相负载率	%		测量-负载
    @Schema(description = "负载A相负载率，单位: %")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal phaseALoadRatio;
    @Schema(description = "负载B相负载率，单位: %")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal phaseBLoadRatio;
    @Schema(description = "负载C相负载率，单位: %")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal phaseCLoadRatio;
}
