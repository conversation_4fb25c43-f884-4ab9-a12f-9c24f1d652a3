package com.cdz360.base.model.base.type;

import lombok.Getter;

@Getter
public enum PayChannel implements DcEnum {
    UNKNOWN(0, "未知"),
    ALIPAY(1, "支付宝"),
    WXPAY(2, "微信支付"),
    BANK_CARD(3, "银行卡支付"),
    BUSINESS_ACCOUNT(4, "对公转账"),
    DIGICCY_ACCOUNT(5, "数字货币"),

    WX_CREDIT(7, "微信信用充"),

    ALIPAY_CREDIT(8, "支付宝信用充"),
    ABC_BANK(9, "农行账户"),
    CCB_ECNY_BANK(10, "建设银行数字人民币"),

    OTHER(999, "其他");

    private final int code;
    private final String desc;

    PayChannel(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public static PayChannel valueOf(Object codeIn) {
        if (codeIn == null) {
            return PayChannel.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof PayChannel) {
            return (PayChannel) codeIn;
        } else if (codeIn instanceof Integer) {
            code = (Integer) codeIn;
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (PayChannel status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return PayChannel.UNKNOWN;
    }

}
