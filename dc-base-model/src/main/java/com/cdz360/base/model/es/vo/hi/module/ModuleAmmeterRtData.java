package com.cdz360.base.model.es.vo.hi.module;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@JsonInclude(Include.NON_NULL)
public class ModuleAmmeterRtData {

    @Schema(description = "A相电压 V")
    @JsonProperty("va")
    private BigDecimal voltageA;

    @Schema(description = "B相电压 V")
    @JsonProperty("vb")
    private BigDecimal voltageB;

    @Schema(description = "C相电压 V")
    @JsonProperty("vc")
    private BigDecimal voltageC;

    @Schema(description = "A相电流 A")
    @JsonProperty("ca")
    private BigDecimal currentA;

    @Schema(description = "B相电流 A")
    @JsonProperty("cb")
    private BigDecimal currentB;

    @Schema(description = "C相电流 A")
    @JsonProperty("cc")
    private BigDecimal currentC;

    @Schema(description = "A相视在功率 kVA")
    @JsonProperty("appa")
    private BigDecimal apparentPowerA;

    @Schema(description = "B相视在功率 kVA")
    @JsonProperty("appb")
    private BigDecimal apparentPowerB;

    @Schema(description = "C相视在功率 kVA")
    @JsonProperty("appc")
    private BigDecimal apparentPowerC;

    @Schema(description = "A相有功功率 kW")
    @JsonProperty("acpa")
    private BigDecimal activePowerA;

    @Schema(description = "B相有功功率 kW")
    @JsonProperty("acpb")
    private BigDecimal activePowerB;

    @Schema(description = "C相有功功率 kW")
    @JsonProperty("acpc")
    private BigDecimal activePowerC;

    @Schema(description = "A相无功功率 kVar")
    @JsonProperty("rapa")
    private BigDecimal reactivePowerA;

    @Schema(description = "B相无功功率 kVar")
    @JsonProperty("rapb")
    private BigDecimal reactivePowerB;

    @Schema(description = "C相无功功率 kVar")
    @JsonProperty("rapc")
    private BigDecimal reactivePowerC;

    @Schema(description = "A相功率因数")
    @JsonProperty("pfa")
    private BigDecimal powerFactorA;

    @Schema(description = "B相功率因数")
    @JsonProperty("pfb")
    private BigDecimal powerFactorB;

    @Schema(description = "C相功率因数")
    @JsonProperty("pfc")
    private BigDecimal powerFactorC;

    @Schema(description = "组合有功总电能 kW·h")
    @JsonProperty("catee")
    private BigDecimal combinedActiveTotalElectricalEnergy;

    @Schema(description = "正向有功电能 kW·h")
    @JsonProperty("fae")
    private BigDecimal forwardActiveEnergy;

    @Schema(description = "反向有功电能 kW·h")
    @JsonProperty("bae")
    private BigDecimal backwardActiveEnergy;

    @Schema(description = "正向无功电能 kW·h")
    @JsonProperty("fre")
    private BigDecimal forwardReactiveEnergy;

    @Schema(description = "反向无功电能 kW·h")
    @JsonProperty("bre")
    private BigDecimal backwardReactiveEnergy;


}
