package com.cdz360.base.model.es.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(title = "EMU单日计费数据")
public class EmuDailyFee {

    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Schema(title = "日期")
    private LocalDate date;

    @Schema(title = "EMU设备编号")
    private String dno;


    @Schema(title = "场站ID")
    @JsonInclude(Include.NON_EMPTY)
    private String siteId;

    @Schema(title = "计费模板ID", description = "日内如果计费有变更,仅记录其中一个,如果下属PCS使用不同计费模板,仅记录一个. 主要供参考")
    @JsonInclude(Include.NON_NULL)
    private Long priceId;

    @Schema(title = "充电电量", description = "单位 kwh")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal inKwh;

    @Schema(title = "放电电量", description = "单位 kwh")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal outKwh;

    @Schema(title = "充电金额", description = "单位: 元")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal inFee;

    @Schema(title = "放电金额", description = "单位: 元")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal outFee;

    @Schema(title = "开始充电电量", description = "当日开始时的总充电电量, 单位 kwh")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal totalInKwh;

    @Schema(title = "开始放电电量", description = "当日开始时的总放电电量, 单位 kwh")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal totalOutKwh;
}
