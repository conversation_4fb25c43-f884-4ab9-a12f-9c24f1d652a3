package com.cdz360.base.model.iot.vo;

import com.cdz360.base.model.iot.type.EssEquipType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Schema(description = "储能信息")
@Data
@Accessors(chain = true)
public class EssVo<T> {
    @Schema(description = "微网控制器编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String gwno;

    @Schema(description = "微网控制器名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String gwName;

    @Schema(description = "储能ESS唯一编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String dno;

    @Schema(description = "设备名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String name;

    @Schema(description = "EMS设备的SN")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String sn;

    @Schema(title = "错误代码", description = "一次可能出现多种故障")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<ErrorObj> errorList;

    @Schema(description = "场站所属商户")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long siteCommId;

    @Schema(description = "场站ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteId;

    @Schema(description = "场站名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteName;

    @Schema(description = "设备类型")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private EssEquipType essEquipType;

    @Schema(description = "设备数据")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private T rtData;


    @Data
    @Accessors(chain = true)
    public static class ErrorObj {

        @Schema(description = "设备类型")
        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        private EssEquipType equipType;

        @Schema(description = "设备列表")
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private List<ErrorEquip> equipList;

        @Schema(title = "错误码", description = "多设备发生同一错误码")
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Long errorCode;

        @Schema(title = "信息采集时间", description = "设备本地时间")
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonDeserialize(using = LocalDateTimeDeserializer.class)
        @JsonSerialize(using = LocalDateTimeSerializer.class)
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime lts;

        @Schema(title = "时区", example = "+8")
        private String tz;
    }

    @Data
    @Accessors(chain = true)
    public static class ErrorEquip {
        @Schema(description = "设备ID")
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Long equipId;

        @Schema(description = "设备唯一编号")
        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        private String dno;

        @Schema(description = "下属设备名称")
        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        private String equipName;

        @Schema(title = "电池簇号",
                description = "第N簇电池信息 register adrress = 第0簇电池信息 register adrress + 50* N)\n" +
                        "N代表簇号N(0,ClusterNum) ClusterNum参考583")
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Long clusterNo;

        @Schema(title = "电池组号",
                description = "电池箱X-Y register adrress = 电池箱0-0 register address + (X*LMUnum+Y)*40 )\n" +
                        "X代表簇号 X(0,ClusterNum),Y代表电池号Y(0,LMUnum) ClusterNum参考583, LMUnum参考584+A42")
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Long packNo;

        @Schema(title = "错误代码", description = "一次可能出现多种故障")
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private List<Long> errorCodeList;
    }
}
