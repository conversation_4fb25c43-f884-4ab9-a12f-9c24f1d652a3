package com.cdz360.base.model.es.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "三相数据")
@Data
@Accessors(chain = true)
public class ThreePhaseData {

    @Schema(description = "A相电压，单位: V")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal phaseAVoltage;
    @Schema(description = "B相电压，单位: V")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal phaseBVoltage;
    @Schema(description = "C相电压，单位: V")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal phaseCVoltage;

    @Schema(description = "AB线电压，单位: V")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal lineABVoltage;
    @Schema(description = "BC线电压，单位: V")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal lineBCVoltage;
    @Schema(description = "CA线电压，单位: V")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal lineCAVoltage;

    @Schema(description = "A相THDU，单位: %")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal phaseATHDU;
    @Schema(description = "B相THDU，单位: %")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal phaseBTHDU;
    @Schema(description = "C相THDU，单位: %")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal phaseCTHDU;

    @Schema(description = "电压频率，单位: Hz")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal voltageFrequency;

    @Schema(description = "A相电流有效值，单位: A")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal phaseACurrent;
    @Schema(description = "B相电流有效值，单位: A")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal phaseBCurrent;
    @Schema(description = "C线电流有效值，单位: A")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal phaseCCurrent;
    @Schema(description = "N线电流有效值，单位: A")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal phaseNCurrent;

    @Schema(description = "A相电流THD，单位: %")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal phaseACurrentTHD;
    @Schema(description = "B相电流THD，单位: %")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal phaseBCurrentTHD;
    @Schema(description = "C相电流THD，单位: %")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal phaseCCurrentTHD;

    @Schema(description = "A相电流峰值比，单位: %")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal phaseACurrentPeakRatio;
    @Schema(description = "B相电流峰值比，单位: %")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal phaseBCurrentPeakRatio;
    @Schema(description = "C相电流峰值比，单位: %")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal phaseCCurrentPeakRatio;

    @Schema(description = "A相视在功率，单位: kVA")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal phaseAApparentPower;
    @Schema(description = "B相视在功率，单位: kVA")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal phaseBApparentPower;
    @Schema(description = "C相视在功率，单位: kVA")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal phaseCApparentPower;

    @Schema(description = "A相有功功率，单位: kW")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal phaseAActivePower;
    @Schema(description = "B相有功功率，单位: kW")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal phaseBActivePower;
    @Schema(description = "C相有功功率，单位: kW")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal phaseCActivePower;

    @Schema(description = "A相无功功率，单位: kVar")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal phaseAReactivePower;
    @Schema(description = "B相无功功率，单位: kVar")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal phaseBReactivePower;
    @Schema(description = "C相无功功率，单位: kVar")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal phaseCReactivePower;

    @Schema(description = "A相基波功率因数")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal phaseAFactorFundamental;
    @Schema(description = "B相基波功率因数")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal phaseBFactorFundamental;
    @Schema(description = "C相基波功率因数")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal phaseCFactorFundamental;

    @Schema(description = "A相功率因数")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal phaseAPowerFactor;
    @Schema(description = "B相功率因数")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal phaseBPowerFactor;
    @Schema(description = "C相功率因数")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal phaseCPowerFactor;
}
