package com.cdz360.base.model.base.type;

import lombok.Getter;

@Getter
public enum EvseBrand {

    UNKNOWN( "未知"),
    DING_CHONG( "鼎充"),
    HUI_CHONG( "慧充"),
    XU_JI( "许继"),
    SHUO_ZHOU( "朔州"),
    JU_NENG( "聚能"),
    NENG_RUI( "能瑞"),
    TEST( "测试"),
    BU_BA_QIANG_CHONG_LIAN( "不拔抢重连"),
    TAI_TAN( "泰坦"),
    WAN_MA( "万马"),
    PRS( "PRS"),
    HENG_TONG( "hengtong"),
    XUN_DAO( "循道"),
    YONG_LIAN( "永联"),
    DIAN_MAO( "电猫"),
    RUN_CHENG_DA( "润诚达"),
    LING_CHONG( "领充"),
    RUN_CHENG_DA_TWO( "润诚达二终端"),
    RUN_CHENG_DA_SIX( "润城达六终端"),
    RUN_CHENG_DA_THREE( "润诚达三终端"),
    ;

    private final String desc;

    EvseBrand(String desc) {
        this.desc = desc;
    }

    public static EvseBrand getByDesc(String desc) {
        if (desc == null) {
            return UNKNOWN;
        }
        for (EvseBrand brand : EvseBrand.values()) {
            if (brand.getDesc().equals(desc)) {
                return brand;
            }
        }
        return UNKNOWN;
    }

}
