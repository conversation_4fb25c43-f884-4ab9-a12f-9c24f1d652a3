package com.cdz360.base.model.es.vo;

import com.cdz360.base.model.es.type.LiquidAlarmCode;
import com.cdz360.base.model.iot.type.EquipStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 液冷 Liquid Cooling 实际设备信息
 */
@Data
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class LiquidRtInfo extends EssBaseRtInfo<EquipStatus, LiquidAlarmCode> {


    @Schema(title = "告警状态", description = "true有告警,false无告警,null未知")
    private Boolean alarmStatus;

}
