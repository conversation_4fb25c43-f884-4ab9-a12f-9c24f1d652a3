package com.cdz360.base.model.es.type.hi;

import com.cdz360.base.model.base.type.DcEnum;
import com.cdz360.base.model.charge.type.OrderStartType;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * BMS高压告警
 */
@Getter
public enum BmsHighVoltageAlarm implements DcEnum {

    UNKNOWN(0, "未知"),
    BATTERY_LOW_VOLTAGE_ALARM(1, "电池单体低压告警"),
    BATTERY_HIGH_VOLTAGE_ALARM(2, "电池单体高压告警"),
    BATTERY_PACK_LOW_DISCHARGE_ALARM(3, "电池组放电低压告警"),
    BATTERY_PACK_HIGH_CHARGE_ALARM(4, "电池组充电高压告警"),
    CHARGE_LOW_TEMPERATURE_ALARM(5, "充电低温告警"),
    CHARGE_HIGH_TEMPERATURE_ALARM(6, "充电高温告警"),
    DISCHARGE_LOW_TEMPERATURE_ALARM(7, "放电低温告警"),
    DISCHARGE_HIGH_TEMPERATURE_ALARM(8, "放电高温告警"),
    BATTERY_PACK_CHARGE_OVERCURRENT_ALARM(9, "电池组充电过流告警"),
    BATTERY_PACK_DISCHARGE_OVERCURRENT_ALARM(10, "电池组放电过流告警"),
    BATTERY_MODULE_LOW_VOLTAGE_ALARM(11, "电池模块低压告警"),
    BATTERY_MODULE_HIGH_VOLTAGE_ALARM(12, "电池模块高压告警"),
    TERMINAL_OVERTEMPERATURE_ALARM(13, "端子温度高告警"),
    FAN_ABNORMAL_ALARM(14, "风扇异常"),
    LEAKAGE_CURRENT_ALARM(15, "漏电流异常告警"),
    OTHER_ALARMS(16, "其他告警"),


    CHARGE_OVERVOLTAGE_1(101, "充电过压 1"),
    DISCHARGE_UNDERVOLTAGE_1(102, "放电欠压 1"),
    CELL_HIGH_TEMPERATURE_1(103, "电芯高温 1"),
    CELL_LOW_TEMPERATURE_1(104, "电芯低温 1"),
    CHARGE_OVERCURRENT_1(105, "充电过流 1"),
    DISCHARGE_OVERCURRENT_1(106, "放电过流 1"),
    COMMUNICATION_FAILURE_1(107, "通讯失效 1"),
    SYSTEM_REBOOT(108, "系统重启"),
    CELL_PRESSURE_DIFFERENCE(109, "单体压差大 1"),
    SYSTEM_LOW_TEMPERATURE_1(110, "系统低温 1"),
    SYSTEM_LOW_TEMPERATURE_2(111, "系统低温 2"),
    SYSTEM_HIGH_TEMPERATURE(112, "系统高温"),
    CELL_OVERVOLTAGE_1(113, "单体过压 1"),
    CELL_UNDERVOLTAGE_1(114, "单体欠压 1"),
    CELL_TEMPERATURE_DIFFERENCE_1(115, "单体温差大 1"),
    CHARGE_OVERTEMPERATURE_1(116, "充电过温 1"),
    CHARGE_LOW_TEMPERATURE_1(117, "充电低温 1"),
    DISCHARGE_OVERTEMPERATURE_1(118, "放电过温 1"),
    DISCHARGE_LOW_TEMPERATURE_1(119, "放电低温 1"),
    FIRE_ALARM(132, "消防告警"),


    HIGH_TEMPERATURE(201, "温度高告警"),
    LOW_TEMPERATURE(202, "温度低告警"),
    LARGE_TEMPERATURE_DIFFERENCE(203, "温差大告警"),
    TOTAL_PRESSURE_HIGH(204, "总压高告警"),
    TOTAL_PRESSURE_LOW(205, "总压低告警"),
    HIGH_CELL_VOLTAGE(206, "单体电压高告警"),
    LOW_CELL_VOLTAGE(207, "单体电压低告警"),
    CELL_PRESSURE_DIFFERENCE_LARGE(208, "单体压差大告警"),
    HIGH_CHARGING_CURRENT(209, "充电电流大告警"),
    HIGH_DISCHARGING_CURRENT(210, "放电电流大告警"),
    LOW_SOC(212, "SOC低告警"),
    LOW_INSULATION_RESISTANCE(213, "  绝缘阻抗低告警"),
    FIRE_ALARM_2(232, "消防告警"),
    ;

    @JsonValue
    private final int code;
    private final String desc;

    BmsHighVoltageAlarm(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static BmsHighVoltageAlarm valueOf(Object codeIn) {
        if (codeIn == null) {
            return BmsHighVoltageAlarm.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof OrderStartType) {
            return (BmsHighVoltageAlarm) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (BmsHighVoltageAlarm status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return BmsHighVoltageAlarm.UNKNOWN;
    }

}