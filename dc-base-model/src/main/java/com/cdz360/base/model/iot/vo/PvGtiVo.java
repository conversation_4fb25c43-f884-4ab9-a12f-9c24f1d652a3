package com.cdz360.base.model.iot.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Schema(description = "光伏逆变器信息")
@Data
@Accessors(chain = true)
public class PvGtiVo {
    @Schema(description = "微网控制器编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String gwno;

    @Schema(description = "微网控制器名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String gwName;

    @Schema(description = "逆变器记录ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String recId;

    @Schema(title = "逆变器编号", description ="平台唯一")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String dno;

    @Schema(title = "逆变器地址", description ="串口通信(485/modbus) ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer sid;

    @Schema(title = "设备名称", description ="逆变器名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String name;

    @Schema(description = "运行状态: 未知(0);正常(1);异常(2);待机(3)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer rtStatus;

    @Schema(title = "错误代码", description ="一次可能出现多种故障")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<Long> errorCodeList;

    @Schema(description = "场站所属商户")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long siteCommId;

    @Schema(description = "场站ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteId;

    @Schema(description = "场站名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteName;
}
