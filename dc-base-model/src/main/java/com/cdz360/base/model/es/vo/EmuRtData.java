package com.cdz360.base.model.es.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(title = "EMU实时数据", description = "EMU实时数据")
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class EmuRtData extends EssBaseRtData {

    @Schema(title = "母线电压", description = "母线电压")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal busVoltage;

    @Schema(title = "充放电数据", description = "充放电数据")
    @JsonInclude(Include.NON_NULL)
    private EssChargeTinySummary chargeData;

    @Schema(title = "PCS设备号列表")
    @JsonInclude(Include.NON_NULL)
    private List<String> pcsDnos;

    @Schema(title = "BMS设备号列表")
    @JsonInclude(Include.NON_NULL)
    private List<String> bmsDnos;

    @Schema(title = "液冷设备号列表")
    @JsonInclude(Include.NON_NULL)
    private List<String> liquidDnos;


    @Schema(title = "除湿器设备号列表")
    @JsonInclude(Include.NON_NULL)
    private List<String> dehDnos;

    @Schema(title = "UPS设备号列表")
    @JsonInclude(Include.NON_NULL)
    private List<String> upsDnos;

    @Schema(title = "消防系统设备号列表")
    @JsonInclude(Include.NON_NULL)
    private List<String> ffsDnos;

    @Schema(title = "电表设备号列表")
    @JsonInclude(Include.NON_NULL)
    private List<String> meterDnos;
}
