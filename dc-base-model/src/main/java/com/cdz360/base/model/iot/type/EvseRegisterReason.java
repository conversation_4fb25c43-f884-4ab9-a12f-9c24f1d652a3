package com.cdz360.base.model.iot.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum EvseRegisterReason implements DcEnum {

    UNKNOWN(0, "未知"),

    POWER_ON(1, "上电后注册"),

    OFFLINE(2, "离线后重新注册"),

    SIGN_MISMATCH(3, "报文签名不匹配触发重新注册");


    @JsonValue
    private final int code;
    private final String desc;

    EvseRegisterReason(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static EvseRegisterReason valueOf(Object codeIn) {
        if (codeIn == null) {
            return EvseRegisterReason.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof DtuType) {
            return (EvseRegisterReason) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (EvseRegisterReason status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return EvseRegisterReason.UNKNOWN;
    }

}
