package com.cdz360.base.model.charge.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 订单启动方式 0x01 ~ 0x0F : 桩端(离线)启动 0x10 ~ 0x1F : 桩端(在线)启动 0x20 ~ 0x2F : 管理后台启动 0x30 ~ 0x3F : 客户移动端启动
 */
@Getter
public enum OrderStartType implements DcEnum {
    UNKNOWN(0, "未知", false),

    EVSE_OFFLINE_CARD(1, "紧急充电卡", true),

    EVSE_AUTO(2, "设备端自主", false),

    OFFLINE_VIN(3, "桩端VIN白名单", true),

    OFFLINE_CARD(4, "离线卡", true),

    CREDIT_CARD(5, "信用卡", true),

    ONLINE_CARD(17, "在线卡鉴权", true), // 0x11

    ONLINE_VIN(18, "VIN识别鉴权", true),     // 0x12

    MGM_WEB_MANUAL(33, "管理端手动", false),   // 0x21

    MGM_WEB_BATCH(34, "批量启动", false), // 0x22

    HLHT(35, "外部平台请求", true),   // 0x23

    SCHEDULE_JOB(36, "定时充电任务", true), // 0x24

    WX_LITE(50, "微信小程序", false),  // 0x32

    IOS_APP(51, "iOS APP", false),    // 0x33

    ANDROID_APP(52, "安卓APP", false),    // 0x34

    ALIPAY_LITE(53, "支付宝应用", false),    // 0x35

    GAODE_LITE(54, "高德小程序", false),  // 0x36

    CHARGE_H5(60, "充电H5", false),  // 0x3C
    ;

    @JsonValue
    private final int code;
    private final String desc;
    private boolean autoSettle; // 是否自动结算

    OrderStartType(int code, String desc, boolean autoSettle) {
        this.code = code;
        this.desc = desc;
        this.autoSettle = autoSettle;
    }

    @JsonCreator
    public static OrderStartType valueOf(Object codeIn) {
        if (codeIn == null) {
            return OrderStartType.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof OrderStartType) {
            return (OrderStartType) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (OrderStartType status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return OrderStartType.UNKNOWN;
    }

}
