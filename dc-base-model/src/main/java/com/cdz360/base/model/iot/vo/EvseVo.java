package com.cdz360.base.model.iot.vo;

import com.cdz360.base.model.base.annotation.Cache;
import com.cdz360.base.model.base.type.EvseBizStatus;
import com.cdz360.base.model.base.type.EvseProtocolType;
import com.cdz360.base.model.base.type.EvseStatus;
import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.base.model.iot.type.EvseRegisterReason;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 桩信息
 */
@Data
@Accessors(chain = true)
public class EvseVo implements Serializable {

    private static final long serialVersionUID = -5759396648735145154L;
    /**
     * 桩号
     */
    @Cache
    @Schema(description = "桩号")
    private String evseNo;
    /**
     * 场站对应的商户ID
     */
    @Cache
    @Schema(description = "场站对应的商户ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long siteCommId;
    /**
     * 场站ID
     */
    @Cache
    @Schema(description = "场站ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteId;
    /**
     * 网关编号
     */
    @Cache
    @Schema(description = "网关编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String gwno;

    @Schema(description = "桩IP")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String evseIp;

    /**
     * 状态
     */
    @Cache
    @Schema(description = "状态")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private EvseStatus status;

    @Cache
    @Schema(description = "运营状态")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private EvseBizStatus bizStatus;

    /**
     * 桩名称
     */
    @Cache
    @Schema(description = "桩名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String name;
    /**
     * 枪头数量
     */
    @Cache
    @Schema(description = "枪头数量")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer plugNum;

    /**
     * 价格模板ID
     */
    @Cache
    @Schema(description = "价格模板ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long priceCode;
    /**
     * 供电类型
     */
    @Cache
    @Schema(description = "供电类型")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private SupplyType supplyType;

    /**
     * 电桩型号, 如: G4-001
     */
    @Cache
    @Schema(description = "电桩型号, 如: G4-001")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String modelName;

    /**
     * 功率
     */
    @Cache
    @Schema(description = "额定功率")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer power;

    /**
     * 电压
     */
    @Cache
    @Schema(description = "额定电压, 单位'V'")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal voltage;

    /**
     * 电流
     */
    @Cache
    @Schema(description = "额定电流, 单位'A'")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal current;


    @Cache
    @Schema(description = "是否支持插枪状态")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean connSupport;


    /**
     * 桩协议版本
     */
    @Cache
    @Schema(description = "桩协议版本")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer protocolVer;

    @Cache
    @Schema(description = "桩协议类型")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private EvseProtocolType protocol;

    /**
     * 桩固件(软件)版本
     */
    @Cache
    @Schema(description = "桩固件(软件)版本")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String firmwareVer;

    @Cache
    @Schema(description = "PC01板版本号. 格式为: 硬件版本号-软件版本号-定制号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String pc01Ver;

    @Cache
    @Schema(description = "PC02板版本号. 格式为: 硬件版本号-软件版本号-定制号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String pc02Ver;

    @Cache
    @Schema(description = "PC03板版本号. 格式为: 硬件版本号-软件版本号-定制号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String pc03Ver;

    @Cache
    @Schema(description = "桩体温度, 单位'摄氏度'")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer temperature;

    @Schema(description = "场站名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteName;

    @Schema(description = "错误码")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer errorCode;

    @Schema(description = "告警码")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer alertCode;

    @Schema(description = "是否使用场站默认配置", example= "true")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean useSiteCfg;


    @Cache
    @Schema(description = "实际(已确认)功率", example= "123")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer actualPower;

    @Cache
    @Schema(description = "分配功率", example= "123")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer assignPower;

    @Cache
    @Schema(description = "上电标志位")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private EvseRegisterReason registerReason;

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    @Cache
    @Schema(description = "最后更新时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date updateTime;



}
