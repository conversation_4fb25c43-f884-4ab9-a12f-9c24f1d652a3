package com.cdz360.base.model.base.exception;

import com.cdz360.base.model.base.constants.DcConstants;
import org.slf4j.event.Level;

public class DcGwException extends DcException {
    private static final int STATUS = DcConstants.KEY_RES_CODE_SERVICE_ERROR;


    public DcGwException(int status, String msg) {
        super(status, msg);
    }

    public DcGwException(String msg) {
        super(STATUS, msg);
    }


    public DcGwException(String msg, Level logLevel) {
        super(STATUS, msg, logLevel);
    }

    public DcGwException(String msg, Level logLevel, Throwable e) {
        super(STATUS, msg, logLevel, e);
    }
}
