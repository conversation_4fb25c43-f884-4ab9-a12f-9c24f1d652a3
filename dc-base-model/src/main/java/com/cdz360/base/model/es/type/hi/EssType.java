package com.cdz360.base.model.es.type.hi;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "ESS机器型号")
public enum EssType {
    UNKNOWN(-1, "未知"),
    THREE_PHASE(0, "三相机型"),
    SINGLE_PHASE(1, "单相机型"),
    SPLIT_PHASE(2, "裂相机型") // 单相分裂
    ;

    private final int code;
    private final String desc;

    EssType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static EssType valueOf(Object codeIn) {
        if (codeIn == null) {
            return null;
        }
        int code = 0;
        if (codeIn instanceof EssType) {
            return (EssType) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (EssType type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }

}
