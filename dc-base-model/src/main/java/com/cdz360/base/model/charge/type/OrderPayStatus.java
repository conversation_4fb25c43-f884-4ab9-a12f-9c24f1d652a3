package com.cdz360.base.model.charge.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 订单支付状态
 */
@Getter
public enum OrderPayStatus implements DcEnum {
    UNKNOWN(0, "未知"),

    UNPAID(1, "待支付"),

    PRE_PAID(2, "已支付(即充即退)"),

    REFUNDING(3, "待退款(即充即退)"),

    REFUND_FAIL(4, "退款失败(即充即退)"),

    DEBT(8, "超额待支付"),

    PAID(9, "已支付(结束)"),

    CANCEL(10, "已取消(未支付结束)")
    ;

    @JsonValue
    private final int code;
    private final String desc;


    OrderPayStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;

    }

    @JsonCreator
    public static OrderPayStatus valueOf(Object codeIn) {
        if (codeIn == null) {
            return OrderPayStatus.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof OrderStartType) {
            return (OrderPayStatus) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (OrderPayStatus status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return OrderPayStatus.UNKNOWN;
    }
}
