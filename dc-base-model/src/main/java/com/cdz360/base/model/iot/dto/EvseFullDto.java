package com.cdz360.base.model.iot.dto;

import com.cdz360.base.model.charge.vo.ChargePriceVo;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.model.iot.vo.PlugVo;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EvseFullDto extends EvseVo {

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<PlugVo> plugList;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<ChargePriceVo> price;
}
