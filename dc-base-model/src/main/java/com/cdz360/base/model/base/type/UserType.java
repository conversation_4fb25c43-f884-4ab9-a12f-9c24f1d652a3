package com.cdz360.base.model.base.type;

import lombok.Getter;

@Getter
public enum UserType implements DcEnum {
    UNKNOWN(0, "未知"),
    SYS_USER(1, "商户/平台用户"),
    CUSTOMER(2, "C端客户/普通用户"),
    CORP_USER(3, "企业用户"),
    SYSTEM(4, "系统"),
    ;

    private final int code;
    private final String desc;

    UserType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static UserType valueOf(Object codeIn) {
        if (codeIn == null) {
            return UserType.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof UserType) {
            return (UserType) codeIn;
        } else if (codeIn instanceof Integer) {
            code = (Integer) codeIn;
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (UserType status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return UserType.UNKNOWN;
    }

}
