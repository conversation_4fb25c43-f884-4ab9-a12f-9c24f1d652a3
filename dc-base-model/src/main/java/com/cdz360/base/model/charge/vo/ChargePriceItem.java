package com.cdz360.base.model.charge.vo;


import com.cdz360.base.model.base.type.ChargePriceCategory;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@Schema(description = "电费分时计费模板信息")
public class ChargePriceItem implements Serializable {

    private static final long serialVersionUID = -2239844687790946573L;
    /**
     * 时段编号
     */
    @Schema(description = "时段编号", example = "2")
    private Integer code;

    /**
     * 0, 未知; 1, 尖; 2, 峰; 3, 平; 4, 谷
     */
    @Schema(title = "尖峰平谷标签", example = "2", description = "0, 未知; 1, 尖; 2, 峰; 3, 平; 4, 谷")
    private ChargePriceCategory category;

    /**
     * 价格时段的开始时间, 格式为HHMM, 如 0834 表示8点34分
     * 开始时间取闭区间, 结束时间取开区间.
     * 每日的开始时间为 0000, 当日结束时间为 2400
     */
    @Schema(title = "价格时段的开始时间", example = "1345",
            description ="格式为HHMM. 开始时间取闭区间, 结束时间取开区间. 每日的开始时间为 0000, 当日结束时间为 2400")
    private String startTime;

    /**
     * 价格时段的结束时间, 格式为HHMM, 如 1834 表示18点34分
     * 开始时间取闭区间, 结束时间取开区间.
     * 每日的开始时间为 0000, 当日结束时间为 2400
     */
    @Schema(title = "价格时段的结束时间", example = "1635",
            description ="格式为HHMM. 开始时间取闭区间, 结束时间取开区间. 每日的开始时间为 0000, 当日结束时间为 2400")
    private String endTime;

    /**
     * 电费单价
     */
    @Schema(description = "电费单价, 单位'元', 4位小数", example = "2.1234")
    private BigDecimal elecPrice;

    /**
     * 服务费单价
     */
    @Schema(description = "服务费单价, 单位'元', 4位小数", example = "2.1234")
    private BigDecimal servPrice;
}
