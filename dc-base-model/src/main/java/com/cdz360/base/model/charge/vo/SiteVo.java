package com.cdz360.base.model.charge.vo;

import com.cdz360.base.model.geo.vo.GeoInfo;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@Accessors(chain = true)
@Schema(description = "场站信息")
public class SiteVo implements Serializable {
    private static final long serialVersionUID = -3882614831080772081L;

    @Schema(description = "场站唯一ID")
    private String id;

    /**
     * 站点名称
     */
    @Schema(description = "场站名称")
    private String siteName;

    @Schema(description = "地理位置信息")
    private GeoInfo position;

    @Schema(description = "站点实景图片")
    private List<String> images;

    /**
     * 站点类型(0:未知,1:公共,2:个人,3:运营)
     **/
//    @Schema(description = "站点类型. ")
//    private Integer type;


    @Schema(description = "场站关联的价格模板")
    private ChargePriceVo chargePrice;

    @Schema(description = "场站额定功率（配电功率）")
    private Integer capacityPower;
}
