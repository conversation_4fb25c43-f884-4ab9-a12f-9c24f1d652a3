package com.cdz360.base.model.es.dto;

import com.cdz360.base.model.es.type.EssAlarmType;
import com.cdz360.base.model.es.type.WarnDeviceType;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "ESS告警信息")
@Data
@Accessors(chain = true)
public class EssAlarms {

    @Schema(description = "网关编号,仅用于户储")
    @JsonInclude(Include.NON_EMPTY)
    private String gwno;
    @Schema(description = "所属场站ID,用于工商储")
    @JsonInclude(Include.NON_EMPTY)
    private String siteId;

    @Schema(description = "所属场站名称,用于工商储")
    @JsonInclude(Include.NON_EMPTY)
    private String siteName;

    @Schema(description = "发生告警的ESS编号,用于工商储")
    @JsonInclude(Include.NON_EMPTY)
    private String essDno;

    @Schema(description = "发生告警的ESS名称")
    @JsonInclude(Include.NON_EMPTY)
    private String essName;

    @Schema(description = "子设备类型(告警目标设备)")
    private EssEquipType equipType;

    @Schema(description = "发生告警的子设备编号")
    @JsonInclude(Include.NON_EMPTY)
    private String equipDno;

    @Schema(description = "发生告警的子设备名称")
    @JsonInclude(Include.NON_EMPTY)
    private String equipName;


    @Schema(description = "发生告警主体设备归属类型", example = "充电桩/户用储能ESS/商户储能ESS")
    private WarnDeviceType equipCategory;

    @Schema(description = "设备本地时间戳,unix时间,单位秒. 告警结束使用 ts + tz 计算出设备的LocalDateTime")
    @JsonInclude(Include.NON_NULL)
    private Long ts;

    @Schema(description = "时区")
    @JsonInclude(Include.NON_EMPTY)
    private String tz;

    @Schema(description = "设备关联用户所在国家地区代码(Alpha-3 code), 用于户储",
        externalDocs = @ExternalDocumentation(
            description = "iso-3166 Country Codes",
            url = "https://www.iso.org/obp/ui/#search"), hidden = true)
    private String cc;

    @Schema(title = "告警列表", description = "一次可能出现多种故障")
    @JsonInclude(Include.NON_NULL)
    private List<EssAlarmTinyDto> alarms;

}
