package com.cdz360.base.model.es.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "电网数据")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class GridData extends ThreePhaseData {

//    //    0x1000	4	电网A相电压	V		测量-电网
//    //    0x1002	4	电网B相电压	V		测量-电网
//    //    0x1004	4	电网C相电压	V		测量-电网
//    @Schema(description = "电网A相电压，单位: V")
//    private BigDecimal phaseAVoltage;
//    @Schema(description = "电网B相电压，单位: V")
//    private BigDecimal phaseBVoltage;
//    @Schema(description = "电网C相电压，单位: V")
//    private BigDecimal phaseCVoltage;
//    //    0x1006	4	电网AB线电压	V		测量-电网
//    //    0x1008	4	电网BC线电压	V		测量-电网
//    //    0x100A	4	电网CA线电压	V		测量-电网
//    @Schema(description = "电网AB线电压，单位: V")
//    private BigDecimal lineABVoltage;
//    @Schema(description = "电网BC线电压，单位: V")
//    private BigDecimal lineBCVoltage;
//    @Schema(description = "电网CA线电压，单位: V")
//    private BigDecimal lineCAVoltage;
//    //    0x100C	4	电网A相THDU	%		测量-电网
//    //    0x100E	4	电网B相THDU	%		测量-电网
//    //    0x1010	4	电网C相THDU	%		测量-电网
//    @Schema(description = "电网A相THDU，单位: %")
//    private BigDecimal phaseATHDU;
//    @Schema(description = "电网B相THDU，单位: %")
//    private BigDecimal phaseBTHDU;
//    @Schema(description = "电网C相THDU，单位: %")
//    private BigDecimal phaseCTHDU;
//    //    0x1012	4	电网电压频率	Hz		测量-电网
//    @Schema(description = "电网电压频率，单位: Hz")
//    private BigDecimal voltageFrequency;
//    //    0x1014	4	电网A相电流有效值	A		测量-电网
//    //    0x1016	4	电网B相电流有效值	A		测量-电网
//    //    0x1018	4	电网C相电流有效值	A		测量-电网
//    //    0x101A	4	电网N线电流有效值	A		测量-电网
//    @Schema(description = "电网A相电流有效值，单位: A")
//    private BigDecimal phaseACurrent;
//    @Schema(description = "电网B相电流有效值，单位: A")
//    private BigDecimal phaseBCurrent;
//    @Schema(description = "电网N线电流有效值，单位: A")
//    private BigDecimal phaseCCurrent;
//    @Schema(description = "电网N线电流有效值，单位: A")
//    private BigDecimal phaseNCurrent;
//    //    0x101C	4	电网A相电流THD	%		测量-电网
//    //    0x101E	4	电网B相电流THD	%		测量-电网
//    //    0x1020	4	电网C相电流THD	%		测量-电网
//    @Schema(description = "电网A相电流THD，单位: %")
//    private BigDecimal phaseACurrentTHD;
//    @Schema(description = "电网B相电流THD，单位: %")
//    private BigDecimal phaseBCurrentTHD;
//    @Schema(description = "电网C相电流THD，单位: %")
//    private BigDecimal phaseCCurrentTHD;
//    //    0x1022	4	电网A相电流峰值比	%		测量-电网
//    //    0x1024	4	电网B相电流峰值比	%		测量-电网
//    //    0x1026	4	电网C相电流峰值比	%		测量-电网
//    @Schema(description = "电网A相电流峰值比，单位: %")
//    private BigDecimal phaseACurrentPeakRatio;
//    @Schema(description = "电网B相电流峰值比，单位: %")
//    private BigDecimal phaseBCurrentPeakRatio;
//    @Schema(description = "电网C相电流峰值比，单位: %")
//    private BigDecimal phaseCCurrentPeakRatio;
//    //    0x1028	4	电网A相视在功率	kVA		测量-电网
//    //    0x102A	4	电网B相视在功率	kVA		测量-电网
//    //    0x102C	4	电网C相视在功率	kVA		测量-电网
//    @Schema(description = "电网A相视在功率，单位: kVA")
//    private BigDecimal phaseAApparentPower;
//    @Schema(description = "电网B相视在功率，单位: kVA")
//    private BigDecimal phaseBApparentPower;
//    @Schema(description = "电网C相视在功率，单位: kVA")
//    private BigDecimal phaseCApparentPower;
//    //    0x102E	4	电网A相有功功率	kW		测量-电网
//    //    0x1030	4	电网B相有功功率	kW		测量-电网
//    //    0x1032	4	电网C相有功功率	kW		测量-电网
//    @Schema(description = "电网A相有功功率，单位: kW")
//    private BigDecimal phaseAActivePower;
//    @Schema(description = "电网B相有功功率，单位: kW")
//    private BigDecimal phaseBActivePower;
//    @Schema(description = "电网C相有功功率，单位: kW")
//    private BigDecimal phaseCActivePower;
//    //    0x1034	4	电网A相无功功率	kVar		测量-电网
//    //    0x1036	4	电网B相无功功率	kVar		测量-电网
//    //    0x1038	4	电网C相无功功率	kVar		测量-电网
//    @Schema(description = "电网A相无功功率，单位: kVar")
//    private BigDecimal phaseAReactivePower;
//    @Schema(description = "电网B相无功功率，单位: kVar")
//    private BigDecimal phaseBReactivePower;
//    @Schema(description = "电网C相无功功率，单位: kVar")
//    private BigDecimal phaseCReactivePower;
//    //    0x103A	4	电网A相基波功率因数			测量-电网
//    //    0x103C	4	电网B相基波功率因数			测量-电网
//    //    0x103E	4	电网C相基波功率因数			测量-电网
//    @Schema(description = "电网A相基波功率因数")
//    private BigDecimal phaseAFactorFundamental;
//    @Schema(description = "电网B相基波功率因数")
//    private BigDecimal phaseBFactorFundamental;
//    @Schema(description = "电网C相基波功率因数")
//    private BigDecimal phaseCFactorFundamental;
//    //    0x1040	4	电网A相功率因数			测量-电网
//    //    0x1042	4	电网B相功率因数			测量-电网
//    //    0x1044	4	电网C相功率因数			测量-电网
//    @Schema(description = "电网A相功率因数")
//    private BigDecimal phaseAPowerFactor;
//    @Schema(description = "电网B相功率因数")
//    private BigDecimal phaseBPowerFactor;
//    @Schema(description = "电网C相功率因数")
//    private BigDecimal phaseCPowerFactor;
}
