package com.cdz360.base.model.iot.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum BmsProtocolType implements DcEnum {

    UNKNOWN(0, "未知"),

    AUTO_1(1, "自动识别"),

    V2011(2, "2011版国标"),

    V2015(3, "2015版国标"),

    AUTO_2(4, "通讯/辅电自适应"),
    ;


    @JsonValue
    private final int code;
    private final String desc;

    BmsProtocolType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static BmsProtocolType valueOf(Object codeIn) {
        if (codeIn == null) {
            return BmsProtocolType.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof BmsProtocolType) {
            return (BmsProtocolType) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Byte) {
            code = ((Byte) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (BmsProtocolType status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return BmsProtocolType.UNKNOWN;
    }

}
