package com.cdz360.base.model.charge.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum HlhtType implements DcEnum {
    NON_HLHT(0, "未知"),

    POSITIVE_HLHT(1, "正向互联互通"),

    REVERSE_HLHT(2, "反向互联互通");


    @JsonValue
    private final int code;
    private final String desc;

    HlhtType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static HlhtType valueOf(Object codeIn) {
        if (codeIn == null) {
            return HlhtType.NON_HLHT;
        }
        int code = 0;
        if (codeIn instanceof HlhtType) {
            return (HlhtType) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (HlhtType status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return HlhtType.NON_HLHT;
    }
}