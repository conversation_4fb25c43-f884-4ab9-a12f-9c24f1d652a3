package com.cdz360.base.model.bi.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(title = "数据采样", description = "按分钟采集的数据")
public class SamplingMinuteDataVo {

    @Schema(title = "时间戳", description = "单位 分钟")
    private int minute;

    @JsonInclude(Include.NON_NULL)
    private BigDecimal v1;

    @JsonInclude(Include.NON_NULL)
    private BigDecimal v2;

    @JsonInclude(Include.NON_NULL)
    private BigDecimal v3;

    @JsonInclude(Include.NON_NULL)
    private BigDecimal v4;

    @JsonInclude(Include.NON_NULL)
    private BigDecimal v5;

    @JsonInclude(Include.NON_NULL)
    private BigDecimal v6;

    @JsonInclude(Include.NON_NULL)
    private BigDecimal v7;

    @JsonInclude(Include.NON_NULL)
    private BigDecimal v8;
}
