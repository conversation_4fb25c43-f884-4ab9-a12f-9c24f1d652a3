package com.cdz360.base.model.es.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.Setter;

@Getter
public enum UpsAlarmCode implements DcEnum, EssBaseAlarmCode {


    UNKNOWN(0, "Unknown", "未知"),


    OFFLINE(1, "离线", "设备离线"),

    BATTERY_OPEN(101, "电池未接报警", "Battery open"),
    IP_N_LOSS(102, "输入N线丢失报警", "IP N loss"),
    IP_SITE_FAIL(103, "输入零火线接反报警", "IP site fail"),
    LINE_PHASE_ERROR(104, "三相输入时，市电L1/L2/L3相序错误", "Line phase error"),
    BYPASS_PHASE_ERROR(105, "三相输入时，旁路L1/L2/L3相序错误", "Bypass phase error"),
    BYPASS_FREQUENCY_UNSTABLE(106, "旁路输入频率变化过快，超出UPS锁相能力",
        "Bypass frequency unstable"),
    BATTERY_OVER_CHARGE(107, "电池过充报警", "Battery over charge"),
    BATTERY_LOW(108, "电池低压报警", "Battery low"),
    OVERLOAD_WARNING(109, "过载报警", "Overload warning"),
    FAN_LOCK_WARNING(110, "风扇模组堵转报警", "Fan lock warning"),
    EPO_ACTIVE(111, "EPO 开关生效", "EPO active"),
    TURN_ON_ABNORMAL(112, "系统不允许开机", "Turn on abnormal"),
    OVER_TEMPERATURE(113, "过温报警", "Over temperature"),
    CHARGER_FAIL(114, "充电器报警", "Charger fail"),
    REMOTE_SHUT_DOWN(115, "远程自动关机报警", "Remote shut down"),
    L1_IP_FUSE_FAIL(116, "L1输入保险开路报警", "L1 IP fuse fail"),
    L2_IP_FUSE_FAIL(117, "L2输入保险开路报警", "L2 IP fuse fail"),
    L3_IP_FUSE_FAIL(118, "L3输入保险开路报警", "L3 IP fuse fail"),
    L1_PFC_POSITIVE_ERROR(119, "L1正边 PFC工作异常，连续48个COUNT PWM输出始终为满偏",
        "L1 PFC positive error"),
    L1_PFC_NEGATIVE_ERROR(120, "L1正边 PFC工作异常，连续48个COUNT PWM输出始终为满偏",
        "L1 PFC negative error"),
    L2_PFC_POSITIVE_ERROR(121, "L1正边 PFC工作异常，连续48个COUNT PWM输出始终为满偏",
        "L2 PFC positive error"),
    L2_PFC_NEGATIVE_ERROR(122, "L1正边 PFC工作异常，连续48个COUNT PWM输出始终为满偏",
        "L2 PFC negative error"),
    L3_PFC_POSITIVE_ERROR(123, "L1正边 PFC工作异常，连续48个COUNT PWM输出始终为满偏",
        "L3 PFC positive error"),
    L3_PFC_NEGATIVE_ERROR(124, "L1正边 PFC工作异常，连续48个COUNT PWM输出始终为满偏",
        "L3 PFC negative error"),
    CAN_COMMUNICATION_ERROR(125, "CAN bus通信报警", "CAN communication error"),
    SYNCHRONIZATION_LINE_ERROR(126, "同步信号线路报警", "Synchronization line error"),
    SYNCHRONIZATION_PULSE_ERROR(127, "同步触发信号线路报警", "Synchronization pulse error"),
    HOST_LINE_ERROR(128, "主机信号线路报警", "Host line error"),
    MALE_CONNECTION_ERROR(129, "并机通信线公端连接脱落报警", "Male connection error"),
    FEMALE_CONNECTION_ERROR(130, "并机通信线母端连接脱落报警", "Female connection error"),
    PARALLEL_LINE_CONNECTION_ERROR(131, "并机通信线脱落报警", "Parallel line connection error"),
    BATTERY_CONNECT_DIFFERENT(132, "并机系统各模块电池连接不一致", "Battery connect different"),
    LINE_CONNECT_DIFFERENT(133, "并机系统各模块市电连接不一致", "Line connect different"),
    BYPASS_CONNECT_DIFFERENT(134, "并机系统各模块旁路连接不一致", "Bypass connect different"),
    MODE_TYPE_DIFFERENT(135, "并机系统中各UPS机种类型不一致", "Mode type different"),
    PARALLEL_INVERTER_VOLTAGE_SETTING_DIFFERENT(136, "并机系统逆变电压设置不一致",
        "Parallel inverter voltage setting different"),
    PARALLEL_OUTPUT_FREQUENCY_SETTING_DIFFERENT(137, "并机系统输出频率设置不一致",
        "Parallel output frequency setting different"),
    BATTERY_CELL_OVER_CHARGE(138, "电池单体过充电", "Battery cell over charge"),
    PARALLEL_OUTPUT_PARALLEL_SETTING_DIFFERENT(139, "并机系统输出并联设置不一致",
        "Parallel output parallel setting different"),
    PARALLEL_OUTPUT_PHASE_SETTING_DIFFERENT(140, "并机系统输出相角设置不一致",
        "Parallel output phase setting different"),
    PARALLEL_BYPASS_FORBIDDEN_SETTING_DIFFERENT(141, "并机系统旁路禁止标志位设置不一致",
        "Parallel Bypass Forbidden setting different"),
    PARALLEL_CONVERTER_ENABLE_SETTING_DIFFERENT(142, "并机系统CVCF标志位设置不一致",
        "Parallel Converter Enable setting different"),
    PARALLEL_BYPASS_FREQ_HIGH_LOSS_SETTING_DIFFERENT(143, "并机系统旁路频率丢失点上限设置不一致",
        "Parallel Bypass Freq High loss setting different"),
    PARALLEL_BYPASS_FREQ_LOW_LOSS_SETTING_DIFFERENT(144, "并机系统旁路频率丢失点下限设置不一致",
        "Parallel Bypass Freq Low loss setting different"),
    PARALLEL_BYPASS_VOLT_HIGH_LOSS_SETTING_DIFFERENT(145, "并机系统旁路电压丢失点上限设置不一致",
        "Parallel Bypass Volt High loss setting different"),
    PARALLEL_BYPASS_VOLT_LOW_LOSS_SETTING_DIFFERENT(146, "并机系统旁路电压丢失点下限设置不一致",
        "Parallel Bypass Volt Low Loss setting different"),
    PARALLEL_LINE_FREQ_HIGH_LOSS_SETTING_DIFFERENT(147, "并机系统市电频率丢失点上限设置不一致",
        "Parallel Line Freq High Loss setting different"),
    PARALLEL_LINE_FREQ_LOW_LOSS_SETTING_DIFFERENT(148, "并机系统市电频率丢失点下限设置不一致",
        "Parallel Line Freq Low Loss setting different"),
    PARALLEL_LINE_VOLT_HIGH_LOSS_SETTING_DIFFERENT(149, "并机系统市电电压丢失点上限设置不一致",
        "Parallel Line Volt High Loss setting different"),
    PARALLEL_LINE_VOLT_LOW_LOSS_SETTING_DIFFERENT(150, "并机系统市电电压丢失点下限设置不一致",
        "Parallel Line Volt Low Loss setting different"),
    LOCKED_IN_BYPASS_AFTER_OVERLOAD_3_TIMES_IN_30MIN(151, "30分钟内过载三次锁在旁路告警",
        "Locked in bypass after overload 3 times in 30min"),
    WARNING_FOR_THREE_PHASE_AC_INPUT_CURRENT_UNBALANCE(152, "PFC输入电流不平衡告警",
        "Warning for three-phase AC input current unbalance"),
    BATTERY_FUSE_BROKEN(153, "电池保险开路告警", "Battery fuse broken"),
    INVERTER_INTER_CURRENT_UNBALANCE(154, "逆变并板不均流告警", "Inverter inter-current unbalance"),
    P1_CUT_OFF_PRE_ALARM(155, "P1切断预警", "P1 cut off pre-alarm"),
    WARNING_FOR_BATTERY_REPLACE(156, "电池需要更换告警", "Warning for Battery replace"),
    WARNING_FOR_INPUT_PHASE_ERROR_FOR_LV_6_10K_UPS(157, "输入相角不正常告警",
        "Warning for input phase error for LV 6-10K UPS"),
    COVER_OF_MAINTAIN_SWITCH_IS_OPEN(158, "维护旁路开路报警", "Cover of maintain switch is open"),
    PHASE_AUTO_ADAPT_FAILED(159, "相位自动侦测失败", "Phase Auto Adapt Failed"),
    UTILITY_EXTREMELY_UNBALANCED(160, "市电电压极度不平衡", "Utility extremely unbalanced"),
    BYPASS_UNSTABLE(161, "旁路状态不稳定", "Bypass unstable"),
    EEPROM_OPERATION_EEROR(162, "EEPROM操作异常", "EEPROM operation error"),
    PARALLEL_PROTECT_WARNING(163, "并机保护告警。提示机器上次运行时出现了并机通讯线丢失故障",
        "Parallel protect warning"),
    DISCHARGER_OVERLY(164, "电池过放电告警,需要进行保护", "Discharger overly"),
    ;

    @JsonValue
    private final int code;
    private final String msg;
    private final String desc;
    @Setter
    private Integer level;

    UpsAlarmCode(int code, String msg, String desc) {
        this.code = code;
        this.msg = msg;
        this.desc = desc;
    }

    @JsonCreator
    public static UpsAlarmCode valueOf(Object codeIn) {
        if (codeIn == null) {
            return UpsAlarmCode.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof UpsAlarmCode) {
            return (UpsAlarmCode) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (UpsAlarmCode type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return UpsAlarmCode.UNKNOWN;
    }
}
