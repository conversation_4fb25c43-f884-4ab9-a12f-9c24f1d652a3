package com.cdz360.base.model.es.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum BmsStackChargeable implements DcEnum {

    UNKNOWN(999, "未知"),
    FORBID(0, "禁止"),
    PERMIT(1, "允许"),
    FORCE(10, "强充"),
    ;

    @JsonValue
    private final int code;
    private final String desc;

    BmsStackChargeable(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static BmsStackChargeable valueOf(Object codeIn) {
        if (codeIn == null) {
            return BmsStackChargeable.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof BmsStackChargeable) {
            return (BmsStackChargeable) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (BmsStackChargeable type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return BmsStackChargeable.UNKNOWN;
    }

}
