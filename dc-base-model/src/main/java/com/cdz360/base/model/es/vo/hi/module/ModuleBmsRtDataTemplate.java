package com.cdz360.base.model.es.vo.hi.module;

import com.cdz360.base.model.es.type.hi.BmsBasicStatus;
import com.cdz360.base.model.es.type.hi.BmsHighVoltageAlarm;
import com.cdz360.base.model.es.type.hi.BmsHighVoltageFault;
import com.cdz360.base.model.es.type.hi.BmsHighVoltageProtection;
import com.cdz360.base.model.es.type.hi.BmsLowVoltageAlarm;
import com.cdz360.base.model.es.type.hi.BmsLowVoltageProtection;
import com.cdz360.base.model.es.type.hi.BmsProtocol;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@JsonInclude(Include.NON_NULL)
public class ModuleBmsRtDataTemplate {

    @Schema(description = "BMS运行状态 0离线 1在线")
    @JsonProperty("os")
    @JsonAlias({"opStatus", "os"})
    private Boolean opStatus;

    @Schema(description = "基本状态")
    @JsonInclude(Include.NON_EMPTY)
    @JsonProperty("s")
    @JsonAlias({"status", "s"})
    private List<BmsBasicStatus> status;

    @Schema(description = "循环周期")
    @JsonProperty("c")
    @JsonAlias({"cycle", "c"})
    private BigDecimal cycle;

    @Schema(description = "高压故障")
    @JsonInclude(Include.NON_EMPTY)
    @JsonProperty("hvf")
    @JsonAlias({"highVoltageFaults", "hvf"})
    private List<BmsHighVoltageFault> highVoltageFaults;

    @Schema(description = "高压告警")
    @JsonInclude(Include.NON_EMPTY)
    @JsonProperty("hva")
    @JsonAlias({"highVoltageAlarms", "hva"})
    private List<BmsHighVoltageAlarm> highVoltageAlarms;

    @Schema(description = "高压保护")
    @JsonInclude(Include.NON_EMPTY)
    @JsonProperty("hvp")
    @JsonAlias({"highVoltageProtections", "hvp"})
    private List<BmsHighVoltageProtection> highVoltageProtections;

//    @Schema(description = "低压故障")
//    @JsonInclude(Include.NON_EMPTY)
//    @JsonProperty("lvf")
//    @JsonAlias({"lowVoltageFaults", "lvf"})
//    private List<BmsLowVoltageFault> lowVoltageFaults;

    @Schema(description = "低压告警")
    @JsonInclude(Include.NON_EMPTY)
    @JsonProperty("lva")
    @JsonAlias({"lowVoltageAlarms", "lva"})
    private List<BmsLowVoltageAlarm> lowVoltageAlarms;

    @Schema(description = "低压保护")
    @JsonInclude(Include.NON_EMPTY)
    @JsonProperty("lvp")
    @JsonAlias({"lowVoltageProtections", "lvp"})
    private List<BmsLowVoltageProtection> lowVoltageProtections;

    @Schema(description = "充电总电能 kW·h")
    @JsonProperty("cea")
    @JsonAlias({"chargeEnergyAll", "cea"})
    private BigDecimal chargeEnergyAll;

    @Schema(description = "充电总电能 kW·h")
    @JsonProperty("dea")
    @JsonAlias({"dischargeEnergyAll", "dea"})
    private BigDecimal dischargeEnergyAll;

    @Schema(description = "电池组总电压 V")
    @JsonProperty("bpva")
    @JsonAlias({"batteryPackVoltageAll", "bpva"})
    private BigDecimal batteryPackVoltageAll;

    @Schema(description = "电池组总电流 A")
    @JsonProperty("bpca")
    @JsonAlias({"batteryPackCurrentAll", "bpca"})
    private BigDecimal batteryPackCurrentAll;

    @Schema(description = "温度 ℃")
    @JsonProperty("t")
    @JsonAlias({"temp", "t"})
    private BigDecimal temp;

    @Schema(description = "SOC %")
    private BigDecimal soc;

    @Schema(description = "SOH %")
    private BigDecimal soh;

    @Schema(description = "最高单体电池电压")
    @JsonProperty("mabv")
    @JsonAlias({"maxBatteryVoltage", "mabv"})
    private BatterySummary maxBatteryVoltage;

    @Schema(description = "最低单体电池电压")
    @JsonProperty("mibv")
    @JsonAlias({"minBatteryVoltage", "mibv"})
    private BatterySummary minBatteryVoltage;

    @Schema(description = "最高单体电池温度")
    @JsonProperty("mabt")
    @JsonAlias({"maxBatteryTemp", "mabt"})
    private BatterySummary maxBatteryTemp;

    @Schema(description = "最低单体电池温度")
    @JsonProperty("mibt")
    @JsonAlias({"minBatteryTemp", "mibt"})
    private BatterySummary minBatteryTemp;

    @Schema(description = "充电电流限幅")
    @JsonProperty("ccl")
    @JsonAlias({"chargingCurrentLimiting", "ccl"})
    private BigDecimal chargingCurrentLimiting;

    @Schema(description = "放电电流限幅")
    @JsonProperty("dcl")
    @JsonAlias({"dischargingCurrentLimiting", "dcl"})
    private BigDecimal dischargingCurrentLimiting;

    @Schema(description = "充电电压限幅")
    @JsonProperty("chvl")
    @JsonAlias({"chargingVoltageLimiting", "chvl"})
    private BigDecimal chargingVoltageLimiting;

    @Schema(description = "放电电压限幅")
    @JsonProperty("dvl")
    @JsonAlias({"dischargingVoltageLimiting", "dvl"})
    private BigDecimal dischargingVoltageLimiting;

    @Schema(description = "单体电池电压")
    @JsonInclude(Include.NON_EMPTY)
    @JsonProperty("cvl")
    @JsonAlias({"cellVoltageList", "cvl"})
    private List<BigDecimal> cellVoltageList;

    @Schema(description = "单体电池温度")
    @JsonInclude(Include.NON_EMPTY)
    @JsonProperty("ctl")
    @JsonAlias({"cellTempList", "ctl"})
    private List<BigDecimal> cellTempList;

    @Schema(description = "MOS温度")
    @JsonProperty("mt")
    @JsonAlias({"mosTemp", "mt"})
    private BigDecimal mosTemp;

    @Schema(description = "环境温度")
    @JsonProperty("et")
    @JsonAlias({"envTemp", "et"})
    private BigDecimal envTemp;

    @Schema(description = "在线的电池数量")
    @JsonProperty("onbc")
    @JsonAlias({"onlineBatteryCount", "onbc"})
    private Integer onlineBatteryCount;

    @Schema(description = "禁止充电的电池数量")
    @JsonProperty("fcbc")
    @JsonAlias({"forbiddenChargingBatteryCount", "fcbc"})
    private Integer forbiddenChargingBatteryCount;

    @Schema(description = "禁止放电的电池数量")
    @JsonProperty("fdbc")
    @JsonAlias({"forbiddenDischargingBatteryCount", "fdbc"})
    private Integer forbiddenDischargingBatteryCount;

    @Schema(description = "离线的电池数量")
    @JsonProperty("ofbc")
    @JsonAlias({"offlineBatteryCount", "ofbc"})
    private Integer offlineBatteryCount;

    @Schema(description = "装机总容量")
    @JsonProperty("ic")
    @JsonAlias({"installedCapacity", "ic"})
    private BigDecimal installedCapacity;

    @Schema(description = "协议")
    @JsonProperty("p")
    @JsonAlias({"protocol", "p"})
    private BmsProtocol protocol;

    @Schema(description = "软件版本")
    @JsonInclude(Include.NON_EMPTY)
    @JsonProperty("sv")
    @JsonAlias({"softwareVer", "sv"})
    private String softwareVer;

    @Schema(description = "首次激活时间")
    @JsonProperty("fat")
    @JsonAlias({"firstActivationTime", "fat"})
    private Date firstActivationTime;

    @Schema(description = "序列号")
    @JsonInclude(Include.NON_EMPTY)
    @JsonProperty("sn")
    @JsonAlias({"serialNumber", "sn"})
    private String serialNumber;

    @Schema(description = "硬件版本")
    @JsonInclude(Include.NON_EMPTY)
    @JsonProperty("hv")
    @JsonAlias({"hardwareVer", "hv"})
    private String hardwareVer;

    @Schema(description = "软件版本")
    @JsonInclude(Include.NON_EMPTY)
    @JsonProperty("sva")
    @JsonAlias({"softwareVerAscii", "sva"})
    private String softwareVerAscii;

    @Data
    @Accessors(chain = true)
    @Schema(description = "单体电池最高/低 电压、温度")
    public static class BatterySummary {

        @Schema(description = "编号")
        @JsonInclude(Include.NON_NULL)
        @JsonProperty("n")
        @JsonAlias({"number", "n"})
        private Integer number;

        @Schema(description = "值")
        @JsonInclude(Include.NON_NULL)
        @JsonProperty("v")
        @JsonAlias({"val", "v"})
        private BigDecimal val;

        public BatterySummary() {
        }

        public BatterySummary(Integer number, BigDecimal val) {
            this.number = number;
            this.val = val;
        }
    }

}
