package com.cdz360.base.model.cus.vo;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.base.annotation.Cache;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.io.Serializable;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class CusInfoVo implements Serializable {


    private static final long serialVersionUID = -958671958434172L;

    /**
     * 客户主键ID
     */
    @Cache
    private long cusId;
    /**
     * 登录账号
     */
    @Cache
    private String username;
    /**
     * 客户手机号
     */
    @Cache
    private String phone;
    /**
     * 客户名称
     */
    @Cache
    private String name;
    /**
     * 所属集团商户ID， 未来废弃,使用 topCommId替换
     */
    @Cache
    private long commId;

    /**
     * 所属集团商户ID
     */
    @Cache
    private Long topCommId;

    /**
     * 客户token
     */
    @Cache
    private String token;
    /**
     * 开放权限的客户端, AppClientType.code
     */
    private List<AppClientType> clients;

    /**
     * 该字段仅用于redis落库，不往外传递，格式为 11:22:33
     */
    @Cache
    @JsonIgnore
    private String clientIds;

    /**
     * 将 clients 编码为字符串, 格式为 11:22:33
     */
    public static String toStringClientIds(CusInfoVo userInfo) {
        if (userInfo == null || userInfo.clients == null || userInfo.clients.isEmpty()) {
            return "";
        }
        return String.join(":", userInfo.clients.stream()
                .map(AppClientType::getCode)
            .map(String::valueOf)
            .sorted()
            .collect(Collectors.toList()));
    }

    /**
     * 将字符串格式的 clientIds 转为枚举类型
     */
    public static List<AppClientType> parseClientIds(String strIds) {
        if(strIds == null || strIds.isBlank()) {
            return List.of();
        }
        return Arrays.stream(strIds.split(":"))
            .map(Integer::valueOf)
            .map(AppClientType::valueOf)
            .collect(Collectors.toList());
    }
}
