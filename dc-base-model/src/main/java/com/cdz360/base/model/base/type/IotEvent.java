package com.cdz360.base.model.base.type;

import lombok.Getter;

/**
 * 桩/枪事件推送的触发原因
 */
@Getter
public enum IotEvent implements DcEnum {
    UNKNOWN(0), // 未知事件

    CREATE(1),  // 新增

    STATE_CHANGE(2),    // 状态变更(含告警事件)

    BIND(3),    // 绑定场站

    UNBIND(4),  // 解绑场站

    CFG_CHANGE(5),  // 配置/价格变更
    
    RT_DATA_CHANGE(8),  // 运行数据变更
    ;

    private final int code;

    IotEvent(int code) {
        this.code = code;
    }


    public static IotEvent valueOf(Object codeIn) {
        if (codeIn == null) {
            return IotEvent.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof IotEvent) {
            return (IotEvent) codeIn;
        } else if (codeIn instanceof Integer) {
            code = (Integer) codeIn;
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (IotEvent status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return IotEvent.UNKNOWN;
    }
}
