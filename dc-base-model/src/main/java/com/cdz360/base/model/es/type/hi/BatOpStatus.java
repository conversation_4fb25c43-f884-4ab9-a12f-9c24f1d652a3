package com.cdz360.base.model.es.type.hi;

import com.cdz360.base.model.charge.type.OrderStartType;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 电池运行状态
 */
@Getter
public enum BatOpStatus {

    UNKNOWN(999, "未知"),
    STANDBY(0, "待机"),
    CHARGE(1, "充电"),
    DISCHARGE(2, "放电"),
    FULL(3, "充满"),
    SELF_CHECK(4, "自检"),
    ;

    @JsonValue
    private final int code;
    private final String desc;

    BatOpStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static BatOpStatus valueOf(Object codeIn) {
        if (codeIn == null) {
            return BatOpStatus.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof OrderStartType) {
            return (BatOpStatus) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (BatOpStatus status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return BatOpStatus.UNKNOWN;
    }

}
