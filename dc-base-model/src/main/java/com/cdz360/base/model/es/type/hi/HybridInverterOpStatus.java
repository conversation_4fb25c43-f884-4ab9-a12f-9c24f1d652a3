package com.cdz360.base.model.es.type.hi;

import com.cdz360.base.model.charge.type.OrderStartType;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 逆变器状态
 */
@Getter
public enum HybridInverterOpStatus {

    UNKNOWN(999, "未知"),
    STANDBY(0, "待机"),
    READY(1, "就绪"),
    OFF_GRID(2, "离网"),
    ON_GRID(3, "并网"),
    ;

    @JsonValue
    private final int code;
    private final String desc;

    HybridInverterOpStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static HybridInverterOpStatus valueOf(Object codeIn) {
        if (codeIn == null) {
            return HybridInverterOpStatus.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof OrderStartType) {
            return (HybridInverterOpStatus) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (HybridInverterOpStatus status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return HybridInverterOpStatus.UNKNOWN;
    }

}
