package com.cdz360.base.model.base.exception;

import com.cdz360.base.model.base.constants.DcConstants;
import org.slf4j.event.Level;

public abstract class DcException extends RuntimeException {

    private int status = DcConstants.KEY_RES_CODE_UNKNOWN_ERROR;
    private Level logLevel = Level.WARN;

    public DcException(int status) {
        super();
        this.status = status;
    }

    public DcException(int status, Level logLevel) {
        super();
        this.status = status;
        this.logLevel = logLevel;
    }


    public DcException(int status, String msg) {
        super(msg);
        this.status = status;
    }

    public DcException(int status, String msg, Throwable e) {
        super(msg, e);
        this.status = status;
    }

    public DcException(int status, String msg, Level logLevel) {
        super(msg);
        this.status = status;
        this.logLevel = logLevel;
    }

    public DcException(int status, String msg, Level logLevel, Throwable e) {
        super(msg, e);
        this.status = status;
        this.logLevel = logLevel;
    }

    public int getStatus() {
        return status;
    }

    public DcException setStatus(int status) {
        this.status = status;
        return this;
    }

    public Level getLogLevel() {
        return this.logLevel;
    }
}
