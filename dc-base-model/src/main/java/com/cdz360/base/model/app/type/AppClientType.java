package com.cdz360.base.model.app.type;

import com.cdz360.base.model.base.type.DcEnum;
import lombok.Getter;

/**
 * APP客户端类型
 */
@Getter
public enum AppClientType implements DcEnum {
    UNKNOWN(0, "未知"),

    PORTAL(1, "综合能源门户"),

    WX_LITE(2, "微信小程序"),

    ALIPAY_LITE(4, "支付宝小程序"),

    ANDROID_APP(6, "安卓APP"),

    IOS_APP(8, "iOS APP"),

    CHARGE_H5(10, "充电H5"),

    MGM_WEB(20, "充电管理后台WEB"),

    SASS_MGM_WEB(21, "运营支撑平台WEB"),

    CORP_WEB(22, "企业客户平台WEB"),

    MGM_WX_LITE(23, "桩管家微信小程序"),

    CARD_MAKER_PC(24, "制卡PC客户端"),

    GAODE_LITE(25, "高德小程序"),

    CHARGE_FOREIGN_H5(29, "[多语言版本]充电H5"),
    MGM_FOREIGN_WEB(30, "[多语言版本]充电管理后台WEB"),

    HLHT(50, "互联互通"),

    ESS_MGM(90, "户用储能平台WEB"),

    COMM_ESS_MGM(92, "工商业储能平台WEB"),
    COMM_ESS_APP(93, "工商业储能APP"),
    PV_MGM(94, "光伏管理平台"),
    ;

    private final int code;
    private final String desc;

    AppClientType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AppClientType valueOf(int code) {
        for (AppClientType source : values()) {
            if (source.code == code) {
                return source;
            }
        }
        return AppClientType.UNKNOWN;
    }

}
