package com.cdz360.base.model.es.vo;

import com.cdz360.base.model.es.type.BmsAlarmCode;
import com.cdz360.base.model.es.type.BmsStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ToString(callSuper = true)
@Schema(title = "BMS实时信息", description = "")
@EqualsAndHashCode(callSuper = true)
public class BmsRtInfo extends EssBaseRtInfo<BmsStatus, BmsAlarmCode> {

    @Schema(title = "IP", description = "BMS IP 地址")
    @JsonInclude(Include.NON_EMPTY)
    private String bmsIp;

    @Schema(title = "端口号", description = "BMS 端口号")
    @JsonInclude(Include.NON_NULL)
    private Integer bmsPort;

    @Schema(title = "品牌名称")
//    @NotNull(message = "vendor 不能为 null")
//    @Size(max = 16, message = "vendor 长度不能超过 16")
    @JsonInclude(Include.NON_EMPTY)
    private String vendor;

    @Schema(title = "BMS对应的PCS设备号")
    @JsonInclude(Include.NON_EMPTY)
    private String pcsDno;



//    @Schema(title = "状态")
//    @JsonInclude(Include.NON_NULL)
//    private BmsStatus bmsStatus;

    @Schema(title = "启动被动均衡压差值")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal voltageGepOfPassiveBalance;

    @Schema(title = "启动被动均衡最低单体电压值")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal minVoltageOfPassiveBalance;

    @Schema(title = "风扇开启温度")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal startFanTemp;

    @Schema(title = "风扇关闭温度")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal stopFanTemp;

    @Schema(title = "风扇单体电压")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal fanVoltage;

    @Schema(title = "单簇充电最大允许电流")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal bundleMaxChargeCurrent;

    @Schema(title = "单簇充电最小允许电流")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal bundleMinChargeCurrent;

    @Schema(title = "充电降流单体电压值")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal reduceChargeVoltage;

    @Schema(title = "充电停止单体电压值")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal stopChargeVoltage;

    @Schema(title = "单簇放电最大允许电流")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal bundleMaxDischargeCurrent;

    @Schema(title = "单簇放电最小允许电流")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal bundleMinDischargeCurrent;

    @Schema(title = "放电降流单体电压值")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal reduceDischargeVoltage;

    @Schema(title = "放电停止单体电压值")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal stopDischargeVoltage;

    @Schema(title = "预充电压")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal preChargeVoltage;

    @Schema(title = "使能堆上高压", description = "true上高压; false下高压")
    @JsonInclude(Include.NON_NULL)
    private Boolean highVoltage;

    @Schema(title = "绝缘检测", description = "开关 true打开; false关闭")
    @JsonInclude(Include.NON_NULL)
    private Boolean insulation;

    @Schema(title = "簇累加/采集总差报警值")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal bundleTotalVoltageDiffAlertValue;

    @Deprecated
    @Schema(description = "液冷实时信息")
    @JsonInclude(Include.NON_NULL)
    private LcsRtInfo lcsRtInfo;

    @Schema(description = "报警参数信息")
    @JsonInclude(Include.NON_NULL)
    private BmsAlarmVo bmsAlarmVo;

//    @Schema(title = "文本值", description = "文本信息")
//    @JsonInclude(Include.NON_NULL)
//    private List<StringVal> textValues;
//
//    @Schema(title = "各种设定值", description = "BMS级别的设定值")
//    @JsonInclude(Include.NON_NULL)
//    private List<RegisterRwValue> cfgValues;
//
//    @Schema(title = "BMS信号量", description = "BMS信号量")
//    @JsonInclude(Include.NON_NULL)
//    private List<SignalVal> signals;
//
//    @Schema(title = "故障列表")
//    @JsonInclude(Include.NON_NULL)
//    private List<BmsAlarmCode> errorList;

    @Schema(title = "电池堆列表")
    @JsonInclude(Include.NON_NULL)
    private List<BmsStackRtInfo> stackInfoList;
}
