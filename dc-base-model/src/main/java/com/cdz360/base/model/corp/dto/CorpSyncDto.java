package com.cdz360.base.model.corp.dto;


import com.cdz360.base.model.charge.type.SettlementType;
import com.cdz360.base.model.base.type.InvoicingMode;
import com.cdz360.base.model.corp.type.CorpType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
@Schema(description = "企业信息同步")
public class CorpSyncDto {

    @Schema(description = "企业ID", example = "123")
    private Long id;

    @Schema(description = "集团商户ID", example = "123")
    private Long topCommId;

    @Schema(description = "商户Id", example = "123")
    private Long commId;

    @Schema(description = "用户UID", example = "123")
    private Long uid;

    @Schema(description = "企业名称", example = "XX公司")
    private String corpName;

    @Schema(description = "联系人名称", example = "张三")
    private String contactName;

    @Schema(description = "手机号", example = "13012345678")
    private String phone;

    @Schema(description = "企业类型")
    private CorpType type;

    @Schema(description = "企业结算方式: 1,账户余额扣减; 3,后付费", format = "java.lang.Integer")
    private SettlementType settlementType;

    @Schema(description = "电子邮箱", example = "<EMAIL>")
    private String email;

    @Schema(description = "省", example = "123456")
    private String province;

    @Schema(description = "市", example = "123456")
    private String city;

    @Schema(description = "区", example = "123456")
    private String district;


    @Schema(description = "地址", example = "xx路123号")
    private String address;

    @Schema(description = "登陆账号", example = "abcd1234")
    private String account;

    @Schema(description = "企业客户开票方式: 充值预开票(PRE_PAY)、充电后开票(POST_CHARGER)、账单后开票(POST_SETTLEMENT)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private InvoicingMode invoiceWay;

    @Schema(description = "创建时间")
    private Date createTime;


    @Schema(description = "最后更新时间")
    private Date updateTime;


    @Schema(description = "是否有效", example = "true")
    private Boolean enable;

    @Schema(description = "操作人ID（非必填）")
    private Long opId;

    @Schema(description = "操作人姓名（非必填）")
    private String opName;

}
