package com.cdz360.base.model.iot.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * DTU类型
 */
@Getter
public enum DtuType implements DcEnum {

    UNKNOWN(0, "未知"),

    YOUREN_610(1, "有人610"),

    YOUREN_701(2, "有人701"),

    YOUREN_730(3, "有人730"),

    YOUREN_780(4, "有人780"),

    HANFENG_2411(10, "汉枫2411"),

    YIYUAN_EC20(16, "移远EC20"),

    SIMCOM_A7670(17, "芯讯通A7670"),
    
    ;


    @JsonValue
    private final int code;
    private final String desc;

    DtuType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static DtuType valueOf(Object codeIn) {
        if (codeIn == null) {
            return DtuType.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof DtuType) {
            return (DtuType) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (DtuType status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return DtuType.UNKNOWN;
    }

}
