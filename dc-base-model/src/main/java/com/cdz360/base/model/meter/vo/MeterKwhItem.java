package com.cdz360.base.model.meter.vo;


import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class MeterKwhItem {
    // 总电量
    private BigDecimal total;

    // 尖(正向有功费率 1 电能)
    private BigDecimal v1;

    // 峰(正向有功费率 2 电能)
    private BigDecimal v2;

    // 平(正向有功费率 3 电能)
    private BigDecimal v3;

    // 谷(正向有功费率 4 电能)
    private BigDecimal v4;

    // 深谷(正向有功费率 5 电能)
    private BigDecimal v5;
}
