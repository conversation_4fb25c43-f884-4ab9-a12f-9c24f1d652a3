package com.cdz360.base.model.es.type.hi;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum UpgradeFileType implements DcEnum {

    UNKNOWN(0xFFFF, new byte[]{(byte) 0xFF, (byte) 0xFF}),
    ARM(0xAA00, new byte[]{(byte) 0xAA, 0x00}),
    DCDC(0x5500, new byte[]{0x55, 0x00}),
    DCAC(0xEE00, new byte[]{(byte) 0xEE, 0x00}),
    BMS1(0xFF00, new byte[]{(byte) 0xFF, 0x00}),
    BMS2(0xCC00, new byte[]{(byte) 0xCC, 0x00}),
    ;

    @JsonValue
    private final int code;
    private final byte[] dataType;

    UpgradeFileType(int code, byte[] dataType) {
        this.code = code;
        this.dataType = dataType;
    }

    @JsonCreator
    public static UpgradeFileType valueOf(Object codeIn) {
        if (codeIn == null) {
            return UpgradeFileType.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof UpgradeFileType) {
            return (UpgradeFileType) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (UpgradeFileType status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return UpgradeFileType.UNKNOWN;
    }

    public static UpgradeFileType valueOfByName(String name) {
        if (name == null) {
            return UpgradeFileType.UNKNOWN;
        }
        for (UpgradeFileType status : values()) {
            if (status.name().equals(name)) {
                return status;
            }
        }
        return UpgradeFileType.UNKNOWN;
    }


}
