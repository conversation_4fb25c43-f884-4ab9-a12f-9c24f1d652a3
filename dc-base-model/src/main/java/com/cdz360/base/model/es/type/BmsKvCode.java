package com.cdz360.base.model.es.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum BmsKvCode implements DcEnum {
    UNKNOWN(110000, "未知"),

    /**
     * 静态信息
     */
    BMS_HW_SN(110001, "硬件序号"),
    BMS_HW_MODEL(110005, "硬件型号"),
    BMS_HW_VER(110006, "硬件版本号"),
    BMS_SW_VER(110010, "软件版本"),

    BMS_PROTOCOL_VER(110015, "协议版本号"),
    BMS_HW_VENDOR(110020, "硬件厂家信息"),


    TOPBMU_HW_MODEL(110030, "TOPBMU硬件型号"),
    BMU_HW_MODEL(110031, "BMU硬件型号"),
    ISO_HW_MODEL(110032, "ISO硬件型号"),
    LMU_HW_MODEL(110033, "LMU硬件型号"),
    BMU_HW_SN(110034, "BMU序列号 SN"),
    BMU_HW_VER(110035, "BMU硬件版本号"),
    BMU_SW_VER(110036, "BMU软件版本"),

    /**
     * 设备状态信息
     */
    BMS_STATUS(110100, "BMS状态"),
    BMS_PCS_COMM_STATUS(110101, "逆变器通信状态"),
    BMS_EMS_COMM_STATUS(110102, "EMS通讯状态"),
    BMS_EMS_COMM_ENABLE(110104, "EMS通讯使能"),


    BMS_CHARGE_STATUS(110105, "充电标志"),
    BMS_DISCHARGE_STATUS(110106, "放电标志"),
    BMS_SOC_CORRECT_MODE(110107, "SOC校准模式"),
    BMS_SOC_CORRECT_ENABLE(110108, "SOC校准使能"),

    BATTERY_STACK_STATUS(110110, "电池堆运行状态"),

    REBOOT_COUNT(110111, "重启次数"),
    BATTERY_STACK_BUNDLE_OPEN_STATUS(110112, "簇是否投入运行标志"),
    BATTERY_STACK_FINISH_STATUS(110114, "充放电完成标志"),

    BATTERY_STACK_SBCU_NUMBER(110115, "堆下辖SBCU簇数"),
    BATTERY_STACK_SBMU_NUMBER(110116, "簇下辖SBMU数量"),
    BATTERY_STACK_BUNDLE_NUMBER(110117, "并簇数量"),
    BATTERY_STACK_SBMU_SINGLE_NUMBER(110120, "SBMU下辖单体数"),
    BATTERY_STACK_SBMU_TEMP_NUMBER(110121, "SBMU下辖累计温度数"),

    BATTERY_STACK_RELAY_STATUS(110125, "堆继电器状态"),
    BATTERY_STACK_PRE_CHARGE_RELAY_STATUS(110126, "堆预充继电器状态"),
    BATTERY_STACK_RELAY_POSITIVE_STATUS(110127, "堆负极继电器状态"),

    BATTERY_STACK_BREAKER_STATUS(110130, "堆断路器状态"),

    BATTERY_STACK_WARN_CODE_1(110150, "堆I级报警码"),
    BATTERY_STACK_WARN_CODE_2(110151, "堆II级报警码"),
    BATTERY_STACK_WARN_CODE_3(110152, "堆III级报警码"),
    BATTERY_STACK_MAINTAIN_CODE_1(110160, "堆维护报警码"),
    BATTERY_STACK_ASSIST_DEVICE_WARN_CODE_1(110166, "辅助设备报警码"),

    BATTERY_BUNDLE_STATUS(110170, "电池簇运行状态"),
    BATTERY_BUNDLE_RELAY_STATUS(110171, "簇继电器状态"),
    BATTERY_BUNDLE_RELAY_ENABLE(110172, "簇继电器远程控制使能"),

    BATTERY_BUNDLE_RELAY_POSITIVE_STATUS(110173, "簇主正继电器状态"),
    BATTERY_BUNDLE_RELAY_NEGATIVE_STATUS(110174, "簇主负继电器状态"),
    BATTERY_BUNDLE_RELAY_PRE_CHARGE_STATUS(110175, "簇主预充继电器状态"),
    BATTERY_BUNDLE_RELAY_BREAKER_STATUS(110176, "簇断路器继电器状态"),
    BATTERY_BUNDLE_RELAY_FAN_STATUS(110177, "簇风扇继电器状态"),
    BATTERY_BUNDLE_RELAY_CHARGE_STATUS(110178, "簇充电继电器状态"),
    BATTERY_BUNDLE_RELAY_DISCHARGE_STATUS(110179, "簇放电继电器状态"),
    BATTERY_BUNDLE_RELAY_DRY_CONTACT_STATUS(110185, "簇干接点状态"),
    BATTERY_BUNDLE_WARN_CODE_1(110190, "簇I级报警码"),
    BATTERY_BUNDLE_WARN_CODE_2(110191, "簇II级报警码"),
    BATTERY_BUNDLE_WARN_CODE_3(110192, "簇III级报警码"),
    BATTERY_BUNDLE_MAINTAIN_CODE_1(110195, "簇维护报警码1"),
    BATTERY_BUNDLE_MAINTAIN_CODE_2(110196, "簇维护报警码2"),

    BMU_SN_DUPLICATE(110210, "BMU SN 重复"),
    BMU_ID_DUPLICATE(110211, "BMU ID 重复"),
    BMU_ID_INCORRECT(110212, "BMU ID 不连续"),
    LMU_NUM_INCORRECT(110215, "各簇 LMU 数量不一致"),
    PARALLEL_STATUS(110220, "并机状态"),
    BMU_WARN_STATUS(110230, "BMU 报警"),

    /**
     * 电池堆数据
     */
    BATTERY_STACK_VOLTAGE(110300, "堆总压"),
    BATTERY_STACK_CURRENT(110301, "堆总电流"),
    BATTERY_STACK_SOC(110302, "堆SOC"),
    BATTERY_STACK_SOH(110303, "堆SOH"),
    BATTERY_STACK_SOE(110304, "堆SOE"),
    BATTERY_STACK_ISO_RESISTOR(110305, "堆绝缘电阻"),
    BATTERY_STACK_ISO_RESISTOR_POSITIVE(110306, "堆绝缘正电阻"),
    BATTERY_STACK_ISO_RESISTOR_NEGATIVE(110307, "堆绝缘负电阻"),
    BATTERY_STACK_MAX_POLE_TEMP(110308, "最高极柱温度"),
    BATTERY_STACK_SINGLE_MAX_VOLTAGE_BUNDLE_ID(110310, "最高单体电压簇号"),
    BATTERY_STACK_SINGLE_MAX_VOLTAGE_BMU_ID(110311, "最高单体电压BMU号"),
    BATTERY_STACK_SINGLE_MAX_VOLTAGE_PACK_ID(110312, "最高单体电压箱内序号"),
    BATTERY_STACK_SINGLE_MAX_VOLTAGE(110313, "最高单体电压值"),
    BATTERY_STACK_SINGLE_MIN_VOLTAGE_BUNDLE_ID(110315, "最低单体电压簇号"),
    BATTERY_STACK_SINGLE_MIN_VOLTAGE_BMU_ID(110316, "最低单体电压BMU号"),
    BATTERY_STACK_SINGLE_MIN_VOLTAGE_PACK_ID(110317, "最低单体电压箱内序号"),
    BATTERY_STACK_SINGLE_MIN_VOLTAGE(110318, "最低单体电压值"),

    BATTERY_STACK_SINGLE_MAX_TEMP_BUNDLE_ID(110320, "最高单体温度簇号"),
    BATTERY_STACK_SINGLE_MAX_TEMP_BMU_ID(110321, "最高单体温度BMU号"),
    BATTERY_STACK_SINGLE_MAX_TEMP_PACK_ID(110322, "最高单体温度箱内序号"),

    BATTERY_STACK_SINGLE_MAX_TEMP(110323, "最高单体温度值"),

    BATTERY_STACK_SINGLE_MIN_TEMP_BUNDLE_ID(110325, "最低单体温度簇号"),
    BATTERY_STACK_SINGLE_MIN_TEMP_BMU_ID(110326, "最低单体温度BMU号"),
    BATTERY_STACK_SINGLE_MIN_TEMP_PACK_ID(110327, "最低单体温度箱内序号"),

    BATTERY_STACK_SINGLE_MIN_TEMP(110328, "最低单体温度值"),

    BATTERY_STACK_SINGLE_MAX_VOLTAGE_DIFF(110330, "最大单体压差值"),
    BATTERY_STACK_SINGLE_MAX_VOLTAGE_DIFF_BUNDLE_ID(110331, "最大单体压差簇号"),
    BATTERY_STACK_SINGLE_MAX_TEMP_DIFF(110332, "最大温度差值"),

    BATTERY_STACK_SINGLE_MAX_TEMP_DIFF_BUNDLE_ID(110333, "最大温度差簇号"),

    BATTERY_STACK_BUNDLE_MAX_VOLTAGE(110335, "最高簇总压"),
    BATTERY_STACK_BUNDLE_MAX_VOLTAGE_BUNDLE_ID(110336, "最高簇总压簇号"),
    BATTERY_STACK_BUNDLE_MIN_VOLTAGE(110337, "最低簇总压"),

    BATTERY_STACK_BUNDLE_MIN_VOLTAGE_BUNDLE_ID(110338, "最低簇总压簇号"),

    BATTERY_STACK_MAX_VOLTAGE_DIFF(110340, "最大总压差"),

    BATTERY_STACK_BUNDLE_MAX_SOC(110345, "最大簇SOC值"),
    BATTERY_STACK_BUNDLE_MAX_SOC_BUNDLE_ID(110346, "最大簇SOC簇号"),
    BATTERY_STACK_BUNDLE_MIN_SOC(110347, "最小簇SOC值"),
    BATTERY_STACK_BUNDLE_MIN_SOC_BUNDLE_ID(110348, "最小簇SOC簇号"),


    BATTERY_STACK_MAX_CHARGE_CURRENT(110350, "堆充电最大限制电流"),
    BATTERY_STACK_MAX_DISCHARGE_CURRENT(110351, "堆放电最大限制电流"),

    BATTERY_STACK_LOOP_NUMBER(110355, "充放电次数"),
    BATTERY_STACK_TODAY_IN_KWH(110360, "堆当日充电电量"),
    BATTERY_STACK_TODAY_OUT_KWH(110361, "堆当日放电电量"),
    BATTERY_STACK_AVAILABLE_IN_KWH(110362, "堆可充电量"),
    BATTERY_STACK_AVAILABLE_OUT_KWH(110363, "堆可放电量"),
    BATTERY_STACK_TOTAL_IN_KWH(110365, "堆累计充电电量"),
    BATTERY_STACK_TOTAL_OUT_KWH(110366, "堆累计放电电量"),

    /**
     * 电池簇数据
     */
    BATTERY_BUNDLE_VOLTAGE(110400, "簇总压"),
    BATTERY_BUNDLE_CURRENT(110401, "簇总电流"),
    BATTERY_BUNDLE_SOC(110402, "簇SOC"),
    BATTERY_BUNDLE_SOH(110403, "簇SOH"),
    BATTERY_BUNDLE_ISO_RESISTOR(110405, "簇绝缘电阻"),
    BATTERY_BUNDLE_ISO_RESISTOR_POSITIVE(110406, "簇绝缘正电阻"),
    BATTERY_BUNDLE_ISO_RESISTOR_NEGATIVE(110407, "簇绝缘负电阻"),

    BATTERY_BUNDLE_SINGLE_MAX_VOLTAGE_BMU_ID(110410, "簇内最高单体电压BMU号"),
    BATTERY_BUNDLE_SINGLE_MAX_VOLTAGE_PACK_ID(110411, "簇内最高单体电压箱内序号"),
    BATTERY_BUNDLE_SINGLE_MAX_VOLTAGE(110412, "簇内最高单体电压值"),

    BATTERY_BUNDLE_SINGLE_MIN_VOLTAGE_BMU_ID(110415, "簇内最低单体电压BMU号"),
    BATTERY_BUNDLE_SINGLE_MIN_VOLTAGE_PACK_ID(110416, "簇内最低单体电压箱内序号"),
    BATTERY_BUNDLE_SINGLE_MIN_VOLTAGE(110417, "簇内最低单体电压值"),

    BATTERY_BUNDLE_SINGLE_MAX_TEMP_BMU_ID(110420, "簇内最高单体温度BMU号"),
    BATTERY_BUNDLE_SINGLE_MAX_TEMP_PACK_ID(110421, "簇内最高单体温度箱内序号"),
    BATTERY_BUNDLE_SINGLE_MAX_TEMP(110422, "簇内最高单体温度值"),

    BATTERY_BUNDLE_SINGLE_MIN_TEMP_BMU_ID(110425, "簇内最低单体温度BMU号"),
    BATTERY_BUNDLE_SINGLE_MIN_TEMP_PACK_ID(110426, "簇内最低单体温度箱内序号"),
    BATTERY_BUNDLE_SINGLE_MIN_TEMP(110427, "簇内最低单体温度值"),

    BATTERY_BUNDLE_SINGLE_MAX_VOLTAGE_DIFF(110430, "簇内最大单体压差值"),
    BATTERY_BUNDLE_SINGLE_AVERAGE_VOLTAGE(110431, "簇内平均单体电压"),
    BATTERY_BUNDLE_SINGLE_MAX_TEMP_DIFF(110432, "簇内最大温度差值"),

    BATTERY_BUNDLE_SINGLE_AVERAGE_TEMP(110435, "簇内平均温度"),

    BATTERY_BUNDLE_MAX_CHARGE_CURRENT(110437, "簇充电最大限制电流"),
    BATTERY_BUNDLE_MAX_DISCHARGE_CURRENT(110438, "簇放电最大限制电流"),

    BATTERY_BUNDLE_LOOP_NUMBER(110440, "簇充放电循环次数"),


    BATTERY_BUNDLE_ONCE_IN_KWH(110441, "簇单次充电电量"),
    BATTERY_BUNDLE_ONCE_OUT_KWH(110442, "簇单次放电电量"),

    BATTERY_BUNDLE_TOTAL_IN_KWH(110443, "簇累计充电电量"),
    BATTERY_BUNDLE_TOTAL_OUT_KWH(110444, "簇累计放电电量"),
    BATTERY_BUNDLE_CHARGE_FINISH(110450, "电池簇充满标志"),
    BATTERY_BUNDLE_DISCHARGE_FINISH(110451, "电池簇放空标志"),

    BATTERY_BUNDLE_SINGLE_VOLTAGE(110455, "簇单体电压"),
    BATTERY_BUNDLE_SINGLE_TEMP(110456, "簇单体温度"),
    BATTERY_BUNDLE_VOLTAGE_DIFF(110460, "簇总压差异检测"),

    BATTERY_BUNDLE_LMU_NUM(110470, "簇LMU个数"),

    /**
     * 设置参数 - 系统运行参数
     */
    BMS_CFG_YEAR(110501, "年"),
    BMS_CFG_MONTH(110502, "月"),
    BMS_CFG_DATE(110503, "日"),
    BMS_CFG_HOUR(110504, "时"),
    BMS_CFG_MINUTE(110505, "分"),
    BMS_CFG_SECOND(110506, "秒"),
    BMS_CFG_VOLTAGE_GAP_OF_PASSIVE_BALANCE(110510, "启动被动均衡压差值"),
    BMS_CFG_SINGLE_MIN_VOLTAGE_OF_PASSIVE_BALANCE(110511, "启动被动均衡最低单体电压值"),
    BMS_CFG_FAN_START_TEMP(110512, "风扇开启温度"),
    BMS_CFG_FAN_STOP_TEMP(110513, "风扇关闭温度"),
    BMS_CFG_FAN_SINGLE_VOLTAGE(110514, "风扇单体电压"),
    BMS_CFG_BUNDLE_MAX_CHARGE_CURRENT(110520, "单簇充电最大允许电流"),
    BMS_CFG_BUNDLE_MIN_CHARGE_CURRENT(110521, "单簇充电最小允许电流"),
    BMS_CFG_BUNDLE_REDUCE_CHARGE_VOLTAGE(110525, "充电降流单体电压值"),
    BMS_CFG_BUNDLE_STOP_CHARGE_VOLTAGE(110526, "充电停止单体电压值"),
    BMS_CFG_BUNDLE_MAX_DISCHARGE_CURRENT(110530, "单簇放电最大允许电流"),
    BMS_CFG_BUNDLE_MIN_DISCHARGE_CURRENT(110531, "单簇放电最小允许电流"),
    BMS_CFG_BUNDLE_REDUCE_DISCHARGE_VOLTAGE(110535, "放电降流单体电压值"),
    BMS_CFG_BUNDLE_STOP_DISCHARGE_VOLTAGE(110536, "放电停止单体电压值"),
    BMS_CFG_BUNDLE_PRE_CHARGE_VOLTAGE(110540, "预充电压"),
    BMS_CFG_BUNDLE_TOTAL_VOLTAGE_DIFF(110541, "簇累加/采集总差报警值"),

    /**
     * 设置参数 - 电池堆控制参数
     */
    BMS_CFG_STACK_HIGH_VOLTAGE_ENABLE(110800, "使能堆上高压"),
    BMS_CFG_STACK_ISO_ENABLE(110801, "关闭绝缘检测"),
    BMS_CMD_STOP(110840, "BMS关机"),

    /**
     * 设置参数 - 报警参数
     */
    BMS_CFG_SINGLE_HIGH_VOLTAGE_WARN_1(110850, "单体电压高1级报警值"),
    BMS_CFG_SINGLE_HIGH_VOLTAGE_WARN_2(110851, "单体电压高2级报警值"),
    BMS_CFG_SINGLE_HIGH_VOLTAGE_WARN_3(110852, "单体电压高3级报警值"),
    BMS_CFG_SINGLE_HIGH_VOLTAGE_WARN_BACKLASH(110853, "单体电压高报警回差值"),
    BMS_CFG_SINGLE_LOW_VOLTAGE_WARN_1(110855, "单体电压低1级报警值"),
    BMS_CFG_SINGLE_LOW_VOLTAGE_WARN_2(110856, "单体电压低2级报警值"),
    BMS_CFG_SINGLE_LOW_VOLTAGE_WARN_3(110857, "单体电压低3级报警值"),
    BMS_CFG_SINGLE_LOW_VOLTAGE_WARN_BACKLASH(110858, "单体电压低报警回差值"),
    BMS_CFG_SINGLE_VOLTAGE_DIFF_WARN_1(110860, "单体压差大1级报警值"),
    BMS_CFG_SINGLE_VOLTAGE_DIFF_WARN_2(110861, "单体压差大2级报警值"),
    BMS_CFG_SINGLE_VOLTAGE_DIFF_WARN_3(110862, "单体压差大3级报警值"),
    BMS_CFG_SINGLE_VOLTAGE_DIFF_WARN_BACKLASH(110863, "单体压差大报警回差值"),
    BMS_CFG_STACK_HIGH_VOLTAGE_WARN_1(110865, "总压高1级报警值"),
    BMS_CFG_STACK_HIGH_VOLTAGE_WARN_2(110866, "总压高2级报警值"),
    BMS_CFG_STACK_HIGH_VOLTAGE_WARN_3(110867, "总压高3级报警值"),
    BMS_CFG_STACK_HIGH_VOLTAGE_WARN_BACKLASH(110868, "总压高报警回差值"),
    BMS_CFG_STACK_LOW_VOLTAGE_WARN_1(110870, "总压低1级报警值"),
    BMS_CFG_STACK_LOW_VOLTAGE_WARN_2(110871, "总压低2级报警值"),
    BMS_CFG_STACK_LOW_VOLTAGE_WARN_3(110872, "总压低3级报警值"),
    BMS_CFG_STACK_LOW_VOLTAGE_WARN_BACKLASH(110873, "总压低报警回差值"),
    BMS_CFG_STACK_HIGH_DISCHARGE_CURRENT_WARN_1(110875, "放电电流大1级报警值"),
    BMS_CFG_STACK_HIGH_DISCHARGE_CURRENT_WARN_2(110876, "放电电流大2级报警值"),
    BMS_CFG_STACK_HIGH_DISCHARGE_CURRENT_WARN_3(110877, "放电电流大3级报警值"),
    BMS_CFG_STACK_HIGH_DISCHARGE_CURRENT_WARN_BACKLASH(110878, "放电电流大报警回差值"),
    BMS_CFG_STACK_HIGH_CHARGE_CURRENT_WARN_1(110880, "充电电流大1级报警值"),
    BMS_CFG_STACK_HIGH_CHARGE_CURRENT_WARN_2(110881, "充电电流大2级报警值"),
    BMS_CFG_STACK_HIGH_CHARGE_CURRENT_WARN_3(110882, "充电电流大3级报警值"),
    BMS_CFG_STACK_HIGH_CHARGE_CURRENT_WARN_BACKLASH(110883, "充电电流大报警回差值"),
    BMS_CFG_STACK_LOW_ISO_WARN_1(110885, "绝缘低1级报警值"),
    BMS_CFG_STACK_LOW_ISO_WARN_2(110886, "绝缘低2级报警值"),
    BMS_CFG_STACK_LOW_ISO_WARN_3(110887, "绝缘低3级报警值"),
    BMS_CFG_STACK_LOW_ISO_WARN_BACKLASH(110888, "绝缘低报警回差值"),
    BMS_CFG_STACK_HIGH_CHARGE_TEMP_WARN_1(110890, "充电温度高1级报警值"),
    BMS_CFG_STACK_HIGH_CHARGE_TEMP_WARN_2(110891, "充电温度高2级报警值"),
    BMS_CFG_STACK_HIGH_CHARGE_TEMP_WARN_3(110892, "充电温度高3级报警值"),
    BMS_CFG_STACK_HIGH_CHARGE_TEMP_WARN_BACKLASH(110893, "充电温度高回差值"),
    BMS_CFG_STACK_LOW_CHARGE_TEMP_WARN_1(110895, "充电温度低1级报警值"),
    BMS_CFG_STACK_LOW_CHARGE_TEMP_WARN_2(110896, "充电温度低2级报警值"),
    BMS_CFG_STACK_LOW_CHARGE_TEMP_WARN_3(110897, "充电温度低3级报警值"),
    BMS_CFG_STACK_LOW_CHARGE_TEMP_WARN_BACKLASH(110898, "充电温度低回差值"),
    BMS_CFG_STACK_HIGH_DISCHARGE_TEMP_WARN_1(110900, "放电温度高1级报警值"),
    BMS_CFG_STACK_HIGH_DISCHARGE_TEMP_WARN_2(110901, "放电温度高2级报警值"),
    BMS_CFG_STACK_HIGH_DISCHARGE_TEMP_WARN_3(110902, "放电温度高3级报警值"),
    BMS_CFG_STACK_HIGH_DISCHARGE_TEMP_WARN_BACKLASH(110903, "放电温度高回差值"),
    BMS_CFG_STACK_LOW_DISCHARGE_TEMP_WARN_1(110905, "放电温度低1级报警值"),
    BMS_CFG_STACK_LOW_DISCHARGE_TEMP_WARN_2(110906, "放电温度低2级报警值"),
    BMS_CFG_STACK_LOW_DISCHARGE_TEMP_WARN_3(110907, "放电温度低3级报警值"),
    BMS_CFG_STACK_LOW_DISCHARGE_TEMP_WARN_BACKLASH(110908, "放电温度低回差值"),
    BMS_CFG_STACK_TEMP_DIFF_WARN_1(110910, "温差大1级报警值"),
    BMS_CFG_STACK_TEMP_DIFF_WARN_2(110911, "温差大2级报警值"),
    BMS_CFG_STACK_TEMP_DIFF_WARN_3(110912, "温差大3级报警值"),
    BMS_CFG_STACK_TEMP_DIFF_WARN_BACKLASH(110913, "温差大回差值"),
    BMS_CFG_STACK_LOW_SOC_WARN_1(110915, "SOC低1级报警值"),
    BMS_CFG_STACK_LOW_SOC_WARN_2(110916, "SOC低2级报警值"),
    BMS_CFG_STACK_LOW_SOC_WARN_3(110917, "SOC低3级报警值"),
    BMS_CFG_STACK_LOW_SOC_WARN_BACKLASH(110918, "SOC低回差值"),
    BMS_CFG_STACK_HIGH_SOC_WARN_1(110920, "SOC高1级报警值"),
    BMS_CFG_STACK_HIGH_SOC_WARN_2(110921, "SOC高2级报警值"),
    BMS_CFG_STACK_HIGH_SOC_WARN_3(110922, "SOC高3级报警值"),
    BMS_CFG_STACK_HIGH_SOC_WARN_BACKLASH(110923, "SOC高回差值"),
    BMS_CFG_STACK_BUNDLE_VOLTAGE_DIFF_WARN_1(110925, "簇间压差大1级报警"),
    BMS_CFG_STACK_BUNDLE_VOLTAGE_DIFF_WARN_2(110926, "簇间压差大2级报警"),
    BMS_CFG_STACK_BUNDLE_VOLTAGE_DIFF_WARN_3(110927, "簇间压差大3级报警"),
    BMS_CFG_STACK_BUNDLE_VOLTAGE_DIFF_WARN_BACKLASH(110928, "簇间压差大回差值"),

    ;

    @JsonValue
    final int code;


    final String desc;


    BmsKvCode(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    @JsonCreator
    public static BmsKvCode valueOf(Object codeIn) {
        if (codeIn == null) {
            return BmsKvCode.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof BmsKvCode) {
            return (BmsKvCode) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (BmsKvCode type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return BmsKvCode.UNKNOWN;
    }

}
