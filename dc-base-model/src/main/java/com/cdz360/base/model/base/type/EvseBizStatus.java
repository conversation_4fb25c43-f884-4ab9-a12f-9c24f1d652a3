package com.cdz360.base.model.base.type;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 设备运营状态
 */
@Getter
public enum EvseBizStatus implements DcEnum {

    UNKNOWN(0, "未知"),

    NORMAL(1, "正常"),

    STOP(11, "停用"),

    OPENING(12, "未开通"),

    REMOVE(13, "拆除"),

    DEAD(14, "报废")
    ;


    @JsonValue
    private final int code;
    private final String desc;

    EvseBizStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static EvseBizStatus valueOf(Object codeIn) {
        if (codeIn == null) {
            return EvseBizStatus.UNKNOWN;
        }
        int code = 0;
        if(codeIn instanceof EvseBizStatus) {
            return (EvseBizStatus)codeIn;
        }
        else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (EvseBizStatus status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return EvseBizStatus.UNKNOWN;
    }
    
}
