package com.cdz360.base.model.base.exception;

import com.cdz360.base.model.base.constants.DcConstants;
import org.slf4j.event.Level;

public class DcServerException extends DcException {
    private static final int STATUS = DcConstants.KEY_RES_CODE_SERVER_ERROR;


    public DcServerException(int status, String msg) {
        super(status, msg);
    }

    public DcServerException(String msg) {
        super(STATUS, msg);
    }


    public DcServerException(String msg, Level logLevel) {
        super(STATUS, msg, logLevel);
    }

    public DcServerException(String msg, Level logLevel, Throwable e) {
        super(STATUS, msg, logLevel, e);
    }
}
