package com.cdz360.base.model.es.dto;

import com.cdz360.base.model.es.type.DeviceWarnType;
import com.cdz360.base.model.es.type.WarnDeviceType;
import com.cdz360.base.model.es.type.WarnSubDeviceType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "ESS告警信息")
@Data
@Accessors(chain = true)
public class EssAlarmNotify {

    @Schema(description = "设备挂在网关的编号")
    private String gwno;

    @Schema(description = "设备挂在网关的名称")
    private String gwName;

    @Schema(description = "设备关联用户所在国家地区代码(Alpha-3 code)",
        externalDocs = @ExternalDocumentation(
            description = "iso-3166 Country Codes",
            url = "https://www.iso.org/obp/ui/#search"), hidden = true)
    private String countryCode;

    @Schema(description = "所属场站ID")
    private String siteId;

    @Schema(description = "所属场站名称")
    private String siteName;

    @Schema(description = "发生告警主体设备归属类型", example = "充电桩/户用储能ESS/商户储能ESS")
    private WarnDeviceType deviceType;

//    --------------- 👆 上面字段可选 ---------------

    @Schema(description = "发生告警的设备编号")
    private String dno;

    @Schema(description = "发生告警的设备名称")
    private String deviceName;

    @Schema(description = "对告警码做映射版本标识(平台唯一)-用来转换告警吗对应信息")
    private String version;

    @Schema(description = "告警归集设备类型")
    @JsonInclude(Include.NON_NULL)
    private WarnSubDeviceType subDeviceType;

    @Schema(description = "子设备名称(告警目标设备)")
    @JsonInclude(Include.NON_EMPTY)
    private String subDeviceName;

    @Schema(description = "子设备编号(告警目标设备)")
    @JsonInclude(Include.NON_EMPTY)
    private String subDeviceDno;

    @Schema(title = "错误代码", description = "一次可能出现多种故障")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<EssAlarmNotify.ErrorObj> errorList;

    @Schema(title = "时区", example = "GMT+08")
    private String tz;

    @Data
    @Accessors(chain = true)
    public static class ErrorObj {

        @Schema(description = "告警目标设备上属告警类型: 逆变器/整流器")
        private DeviceWarnType warnType;

//        @Schema(description = "告警名称")
//        private String warnName;

        @Schema(description = "告警码")
        private Long warnCode;

//        @Schema(description = "告警说明（备注）")
//        private Long warnNote;

        @Schema(title = "信息采集时间", description = "设备本地时间")
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonDeserialize(using = LocalDateTimeDeserializer.class)
        @JsonSerialize(using = LocalDateTimeSerializer.class)
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime lts;

        @Schema(title = "时区", example = "+8")
        private String tz;
    }
}
