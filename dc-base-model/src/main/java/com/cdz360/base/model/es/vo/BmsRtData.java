package com.cdz360.base.model.es.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(title = "BMS实时数据")
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class BmsRtData extends EssBaseRtData {

    @Schema(title = "对应的PCS设备号")
    @JsonInclude(Include.NON_NULL)
    private String pcsDno;


    @Schema(title = "SOC", description = "BMS的SOC")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal soc;

    @Schema(title = "SOH", description = "BMS的SOH")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal soh;


    @Schema(title = "单体最高温度", description = "")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal batteryTempMax;


    @Schema(title = "单体最低温度", description = "")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal batteryTempMin;


    @Schema(title = "电池堆数据列表")
    @JsonInclude(Include.NON_NULL)
    private List<BmsStackRtData> stackDataList = new ArrayList<>();
}
