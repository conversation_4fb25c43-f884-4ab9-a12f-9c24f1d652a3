package com.cdz360.base.model.es.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.Setter;

@Getter
public enum DehAlarmCode implements DcEnum, EssBaseAlarmCode {
    UNKNOWN(0, "未知错误", ""),

    OFFLINE(1, "离线", "设备离线"),

    HIGH_TEMPERATURE_WARN(4401, "高温告警", ""),
    LOW_TEMPERATURE_WARN(4402, "低温告警", ""),
    HIGH_HUMIDITY_WARN(4403, "高湿告警", ""),
    LOW_HUMIDITY_WARN(4404, "低湿告警", ""),
    VENT_HIGH_TEMPERATURE_WARN(4405, "排气高温", ""),
    PIPE_FROZEN_WARN(4411, "盘管防冻", ""),
    EVAPORATOR_FROZEN_WARN(4412, "蒸发器冻结报警", ""),
    PIPE_SENSOR_LOST_WARN(4421, "盘管温感失效", ""),
    OUTSIDE_SENSOR_LOST_WARN(4422, "室外温感失效", ""),
    INSIDE_SENSOR_LOST_WARN(4423, "内温感失效", ""),
    VENT_SENSOR_LOST_WARN(4424, "排气温感失效", ""),
    HUMIDITY_SENSOR_LOST_WARN(4425, "湿感失效", ""),
    RETURN_AIR_SENSOR_LOST_WARN(4426, "回风温感失效", ""),
    EVAPORATOR_SENSOR_LOST_WARN(4427, "蒸发器温感失效", ""),
    FROZEN_SENSOR_LOST_WARN(4428, "冷凝温感失效", ""),


    INSIDE_FAN_ERROR(4431, "内风机故障", ""),
    OUTSIDE_FAN_ERROR(4432, "外风机故障", ""),
    COMPRESSOR_ERROR(4433, "压缩机故障", ""),
    ELEC_HEATING_ERROR(4434, "电加热故障", ""),
    EMERGENCY_FAN_ERROR(4435, "应急风机故障", ""),
    HIGH_VOLTAGE_WARN(4450, "高压告警", ""),
    LOW_VOLTAGE_WARN(4451, "低压告警", ""),
    HIGH_VOLTAGE_LOCK(4452, "高压锁定", ""),
    LOW_VOLTAGE_LOCK(4453, "低压锁定", ""),
    AC_VOLTAGE_OVER_LOAD(4454, "交流过压", ""),
    AC_VOLTAGE_LEAK(4455, "交流欠压", ""),
    AC_LOST(4456, "交流掉电", ""),
    AC_PHASE_LOST(4457, "缺相", ""),
    AC_RATE_ERROR(4458, "频率异常", ""),
    AC_PHASE_REVERSED_ERROR(4459, "逆相", ""),
    DC_VOLTAGE_OVER_LOAD(4460, "直流过压", ""),
    DC_VOLTAGE_LEAK(4461, "直流欠压", ""),
    WATER_WARN(4471, "水浸告警", ""),
    SMOKE_WARN(4472, "烟感告警", ""),
    DOOR_OPEN_WARN(4473, "门禁告警", ""),
    VENT_LOCK(4474, "排气锁定", ""),
    DEH_FAULT(4480, "严重告警总状态", ""),
    DEH_WARN(4481, "通用告警状态", ""),
    SYS_OVER_LOAD(4482, "系统高压力告警", ""),
    SYS_CONTINUAL_OVER_LOAD(4483, "频繁高压力告警", ""),
    ;

    @JsonValue
    final int code;

    final String msg;

    final String desc;
    @Setter
    private Integer level;

    DehAlarmCode(int code, String msg, String desc) {
        this.code = code;
        this.msg = msg;
        this.desc = desc;
    }


    @JsonCreator
    public static DehAlarmCode valueOf(Object codeIn) {
        if (codeIn == null) {
            return DehAlarmCode.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof DehAlarmCode) {
            return (DehAlarmCode) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (DehAlarmCode type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return DehAlarmCode.UNKNOWN;
    }

}
