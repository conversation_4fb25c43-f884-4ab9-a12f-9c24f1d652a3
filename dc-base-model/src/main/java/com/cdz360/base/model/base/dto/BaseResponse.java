package com.cdz360.base.model.base.dto;

import com.cdz360.base.model.base.vo.BaseObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;


@Schema(description = "API调用响应消息的基础数据结构")
public class BaseResponse extends BaseObject {

    private static final BaseResponse SUCCESS = new BaseResponse();
    @Schema(description = "错误描述")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    public String error = null;
    @Schema(description = "状态码, 0表示成功, 其他都为错误码")
    private int status = 0;

    public BaseResponse() {

    }

    public BaseResponse(int status, String error) {
        this.status = status;
        this.error = error;
    }

    public static BaseResponse newInstance() {
        return new BaseResponse();
    }

    public static final BaseResponse success() {
        return SUCCESS;
    }

    public int getStatus() {
        return status;
    }

    public BaseResponse setStatus(int status) {
        this.status = status;
        return this;
    }

    public String getError() {
        return error;
    }

    public BaseResponse setError(String error) {
        this.error = error;
        return this;
    }
}
