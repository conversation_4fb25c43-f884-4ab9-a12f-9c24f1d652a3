package com.cdz360.base.model.es.type.hi;

import com.cdz360.base.model.base.type.DcEnum;
import com.cdz360.base.model.charge.type.OrderStartType;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Getter
@Schema(description = "PV类型")
public enum MonitorPvType implements DcEnum {

    UNKNOWN(0, "未知"),
    module_166(1, "166组件"),
    module_182(2, "182组件"),
    module_210(3, "210组件"),
    ;

    @JsonValue
    private final int code;
    private final String desc;

    MonitorPvType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static MonitorPvType valueOf(Object codeIn) {
        if (codeIn == null) {
            return MonitorPvType.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof OrderStartType) {
            return (MonitorPvType) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (MonitorPvType status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return MonitorPvType.UNKNOWN;
    }

}