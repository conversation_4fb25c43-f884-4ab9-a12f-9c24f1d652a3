package com.cdz360.base.model.es.vo;

import com.cdz360.base.model.es.type.EquipOpenModeType;
import com.cdz360.base.model.es.type.PcsAlarmCode;
import com.cdz360.base.model.es.type.PcsGridMode;
import com.cdz360.base.model.es.type.PcsStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * PCS实际设备信息
 */
@Data
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class PcsRtInfo extends EssBaseRtInfo<PcsStatus, PcsAlarmCode> {


    @Schema(title = "开关机指令", description = "0-关机，1-开机，2-待机")
    @JsonInclude(Include.NON_NULL)
    private EquipOpenModeType openMode;


    @Schema(title = "机器型号")
    @JsonInclude(Include.NON_EMPTY)
    private String deviceModel;

    @Schema(title = "软件版本信息")
    @JsonInclude(Include.NON_EMPTY)
    private String swVer;

    @Schema(title = "设备时间", description = "最后一次从设备上获取到的时钟,  unix 时间戳")
    @JsonInclude(Include.NON_NULL)
    private Long deviceTime;

    @Schema(title = "逆变器有功功率设定", description = " 1、正功率代表电池放电，功率从DC侧到AC侧 2、负功率代表电池充电，功率从AC侧到DC侧 单位 kW")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal activePower;

    @Schema(title = "逆变器无功功率设定", description = "单位 kVAR")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal reactivePower;


    @Schema(title = "逆变器错误向量", description = "0-硬件异常 1-逆变器过流保护 2-母线电压异常 3-PV过流保护 4-PV过压保护 5-孤岛运行异常 6-偏移值异常 7-配置异常 31-永久错误")
    @JsonInclude(Include.NON_NULL)
    private Integer errorCode;

    @Schema(title = "模式", description = "并/离网模式")
    @JsonInclude(Include.NON_NULL)
    private PcsGridMode gridMode;


    @Schema(title = "辅助电源上电状态", description = " 1-是，0-否")
    @JsonInclude(Include.NON_NULL)
    private Integer assistantPowerStatus;

    @Schema(title = "待机状态", description = " 1-是，0-否")
    @JsonInclude(Include.NON_NULL)
    private Integer standbyStatus;

    @Schema(title = "自检状态", description = " 1-是，0-否")
    @JsonInclude(Include.NON_NULL)
    private Integer selfCheckStatus;

    @Schema(title = "并网状态", description = " 1-是，0-否")
    @JsonInclude(Include.NON_NULL)
    private Integer onGridStatus;

    @Schema(title = "离网状态", description = " 1-是，0-否")
    @JsonInclude(Include.NON_NULL)
    private Integer offGridStatus;

    @Schema(title = "故障状态", description = " 1-是，0-否")
    @JsonInclude(Include.NON_NULL)
    private Integer errorStatus;

    @Schema(title = "电池充电状态", description = " 1-是，0-否")
    @JsonInclude(Include.NON_NULL)
    private Integer batteryChargeStatus;


    @Schema(title = "电池放电状态", description = " 1-是，0-否")
    @JsonInclude(Include.NON_NULL)
    private Integer batteryDischargeStatus;

    @Schema(title = "开关机命令下发状态", description = "1-下发的是开机指令，0-下发的是关机指令")
    @JsonInclude(Include.NON_NULL)
    private Integer switchCommandDeliveryStatus;

    @Schema(title = "电网掉电状态", description = " 1-是，0-否")
    @JsonInclude(Include.NON_NULL)
    private Integer gridLostStatus;


}
