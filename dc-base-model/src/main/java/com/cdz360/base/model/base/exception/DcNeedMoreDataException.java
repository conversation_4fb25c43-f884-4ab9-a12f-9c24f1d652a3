package com.cdz360.base.model.base.exception;

import com.cdz360.base.model.base.constants.DcConstants;
import org.slf4j.event.Level;

/**
 * 用于网络层组包。抛出该异常表示收到的消息不完整，需要等待更多的通信报文
 */
public class DcNeedMoreDataException extends DcException {

    private static final int STATUS = DcConstants.KEY_RES_CODE_NEED_MORE_DATA;

    public DcNeedMoreDataException() {
        super(STATUS, "Need more data", Level.INFO);
    }

    public DcNeedMoreDataException(int status, String msg) {
        super(status, msg);
    }

    public DcNeedMoreDataException(String msg) {
        super(STATUS, msg);
    }

    public DcNeedMoreDataException(String msg, Throwable e) {
        super(STATUS, msg, e);
    }


    public DcNeedMoreDataException(String msg, Level logLevel) {
        super(STATUS, msg, logLevel);
    }


    public DcNeedMoreDataException(String msg, Level logLevel, Throwable e) {
        super(STATUS, msg, logLevel, e);
    }


    public DcNeedMoreDataException(int status, String msg, Level logLevel) {
        super(status, msg, logLevel);
    }

}
