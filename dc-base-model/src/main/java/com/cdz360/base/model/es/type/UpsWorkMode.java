package com.cdz360.base.model.es.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum UpsWorkMode implements DcEnum {

    UNKNOWN(0, "未知"),

    POWER_ON_MODE(1, "Power On Mode"),

    STANDBY_MODE(2, "Standby Mode"),

    BYPASS_MODE(3, "Bypass Mode"),

    LINE_MODE(4, "Line Mode"),

    BATTERY_MODE(5, "Battery Mode"),

    BATTERY_TEST_MODE(6, "Battery Test Mode"),

    FAULT_MODE(7, "Fault Mode"),

    HE_ECO_MODE(8, "HE/ECO Mode"),

    CONVERTER_MODE(9, "Converter Mode"),

    SHUTDOWN_MODE(10, "Shutdown Mode");

    @JsonValue
    private final int code;
    private final String desc;

    UpsWorkMode(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static UpsWorkMode valueOf(Object codeIn) {
        if (codeIn == null) {
            return UpsWorkMode.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof UpsWorkMode) {
            return (UpsWorkMode) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (UpsWorkMode type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return UpsWorkMode.UNKNOWN;
    }
}
