package com.cdz360.base.model.es.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 文本数据
 */
@Accessors(chain = true)
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class StringVal extends EssBaseVal<String> {

    public StringVal(int code, Integer addr, String name, String v, String desc) {
        super(code, addr, name, v, desc);
    }

    public StringVal() {
        super();
    }
//    @Schema(title = "编码", description = "XxxKvCode.code")
//    private int code;
//
//    @Schema(title = "寄存器地址", description = "寄存器地址")
//    private Integer addr;
//
//    @Schema(title = "寄存器名称", description = "寄存器名称")
//    @JsonInclude(Include.NON_EMPTY)
//    private String name;
//
//    private String v;
//
//    @JsonInclude(Include.NON_EMPTY)
//    private String desc;
}
