package com.cdz360.base.model.es.vo.hi;

import com.cdz360.base.model.es.dto.EssFactoryDto;
import com.cdz360.base.model.es.vo.hi.module.InverterDevMonitor;
import com.cdz360.base.model.es.vo.hi.module.ModuleAmmeterRtData;
import com.cdz360.base.model.es.vo.hi.module.ModuleBmsRtData;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@JsonInclude(Include.NON_NULL)
public class InverterRtData {

//    private HybridInverterVendor vendor;

    private EssFactoryDto factoryDto;

    private InverterSysData sysData;

    private InverterDevMonitor devMonitor;

    private ModuleAmmeterRtData ammeterRtData;

    private ModuleBmsRtData bmsRtData;

    @Schema(description = "电网电压A相 V 0x1000")
    @JsonProperty("gva")
    private BigDecimal gridVoltageA;

    @Schema(description = "电网电压B相 V")
    @JsonProperty("gvb")
    private BigDecimal gridVoltageB;

    @Schema(description = "电网电压C相 V")
    @JsonProperty("gvc")
    private BigDecimal gridVoltageC;

    @Schema(description = "电网电压AB线 V")
    @JsonProperty("gvlab")
    private BigDecimal gridVoltageLineAB;

    @Schema(description = "电网电压BC线 V")
    @JsonProperty("gvlbc")
    private BigDecimal gridVoltageLineBC;

    @Schema(description = "电网电压CA线 V 0x100A")
    @JsonProperty("gvlca")
    private BigDecimal gridVoltageLineCA;

    @Schema(description = "电网A相THDU %")
    @JsonProperty("gta")
    private BigDecimal gridThduA;

    @Schema(description = "电网B相THDU %")
    @JsonProperty("gtb")
    private BigDecimal gridThduB;

    @Schema(description = "电网C相THDU %")
    @JsonProperty("gtc")
    private BigDecimal gridThduC;

    @Schema(description = "电网电压频率 Hz")
    @JsonProperty("gvf")
    private BigDecimal gridVoltageFrequency;

    @Schema(description = "电网A相电流有效值 A")
    @JsonProperty("gcea")
    private BigDecimal gridCurrentEffectiveA;

    @Schema(description = "电网B相电流有效值 A")
    @JsonProperty("gceb")
    private BigDecimal gridCurrentEffectiveB;

    @Schema(description = "电网C相电流有效值 A")
    @JsonProperty("gcec")
    private BigDecimal gridCurrentEffectiveC;

    @Schema(description = "电网N相电流有效值 A")
    @JsonProperty("gcen")
    private BigDecimal gridCurrentEffectiveN;

    @Schema(description = "电网A相电流THD %")
    @JsonProperty("gcta")
    private BigDecimal gridCurrentThdA;

    @Schema(description = "电网B相电流THD %")
    @JsonProperty("gctb")
    private BigDecimal gridCurrentThdB;

    @Schema(description = "电网C相电流THD % 0x101E")
    @JsonProperty("gctc")
    private BigDecimal gridCurrentThdC;

    @Schema(description = "电网A相电流峰值比 %")
    @JsonProperty("gcpra")
    private BigDecimal gridCurrentPeakRatioA;

    @Schema(description = "电网B相电流峰值比 %")
    @JsonProperty("gcprb")
    private BigDecimal gridCurrentPeakRatioB;

    @Schema(description = "电网C相电流峰值比 %")
    @JsonProperty("gcprc")
    private BigDecimal gridCurrentPeakRatioC;

    @Schema(description = "电网A相视在功率 kVA")
    @JsonProperty("gappa")
    private BigDecimal gridApparentPowerA;

    @Schema(description = "电网B相视在功率 kVA")
    @JsonProperty("gappb")
    private BigDecimal gridApparentPowerB;

    @Schema(description = "电网C相视在功率 kVA")
    @JsonProperty("gappc")
    private BigDecimal gridApparentPowerC;

    @Schema(description = "电网A相有功功率 kW 0x102E")
    @JsonProperty("gacpa")
    private BigDecimal gridActivePowerA;

    @Schema(description = "电网B相有功功率 kW")
    @JsonProperty("gacpb")
    private BigDecimal gridActivePowerB;

    @Schema(description = "电网C相有功功率 kW")
    @JsonProperty("gacpc")
    private BigDecimal gridActivePowerC;

    @Schema(description = "电网A相无功功率 kVar")
    @JsonProperty("grpa")
    private BigDecimal gridReactivePowerA;

    @Schema(description = "电网B相无功功率 kVar")
    @JsonProperty("grpb")
    private BigDecimal gridReactivePowerB;

    @Schema(description = "电网C相无功功率 kVar")
    @JsonProperty("grpc")
    private BigDecimal gridReactivePowerC;

    @Schema(description = "电网A相基波功率因数")
    @JsonProperty("gfpfa")
    private BigDecimal gridFundamentalPowerFactorA;

    @Schema(description = "电网B相基波功率因数")
    @JsonProperty("gfpfb")
    private BigDecimal gridFundamentalPowerFactorB;

    @Schema(description = "电网C相基波功率因数")
    @JsonProperty("gfpfc")
    private BigDecimal gridFundamentalPowerFactorC;

    @Schema(description = "电网A相功率因数")
    @JsonProperty("gpfa")
    private BigDecimal gridPowerFactorA;

    @Schema(description = "电网B相功率因数")
    @JsonProperty("gpfb")
    private BigDecimal gridPowerFactorB;

    @Schema(description = "电网C相功率因数 0x1044")
    @JsonProperty("gpfc")
    private BigDecimal gridPowerFactorC;

    @Schema(description = "负载A相电压 V 0x1046")
    @JsonProperty("lva")
    private BigDecimal loadVoltageA;

    @Schema(description = "负载B相电压 V 0x1048")
    @JsonProperty("lvb")
    private BigDecimal loadVoltageB;

    @Schema(description = "负载C相电压 V 0x104A")
    @JsonProperty("lvc")
    private BigDecimal loadVoltageC;

    @Schema(description = "负载电压AB线 V")
    @JsonProperty("lvlab")
    private BigDecimal loadVoltageLineAB;

    @Schema(description = "负载电压BC线 V")
    @JsonProperty("lvlbc")
    private BigDecimal loadVoltageLineBC;

    @Schema(description = "负载电压CA线 V 0x1050")
    @JsonProperty("lvlca")
    private BigDecimal loadVoltageLineCA;

    @Schema(description = "负载A相THDU %")
    @JsonProperty("lta")
    private BigDecimal loadThduA;

    @Schema(description = "负载B相THDU %")
    @JsonProperty("ltb")
    private BigDecimal loadThduB;

    @Schema(description = "负载C相THDU %")
    @JsonProperty("ltc")
    private BigDecimal loadThduC;

    @Schema(description = "负载电压频率 Hz")
    @JsonProperty("lvf")
    private BigDecimal loadVoltageFrequency;

    @Schema(description = "负载A相电流有效值 A")
    @JsonProperty("lcea")
    private BigDecimal loadCurrentEffectiveA;

    @Schema(description = "负载B相电流有效值 A")
    @JsonProperty("lceb")
    private BigDecimal loadCurrentEffectiveB;

    @Schema(description = "负载C相电流有效值 A")
    @JsonProperty("lcec")
    private BigDecimal loadCurrentEffectiveC;

    @Schema(description = "负载N相电流有效值 A")
    @JsonProperty("lcen")
    private BigDecimal loadCurrentEffectiveN;

    @Schema(description = "负载A相电流THD %")
    @JsonProperty("lcta")
    private BigDecimal loadCurrentThdA;

    @Schema(description = "负载B相电流THD %")
    @JsonProperty("lctb")
    private BigDecimal loadCurrentThdB;

    @Schema(description = "负载C相电流THD % 0x101E")
    @JsonProperty("lctc")
    private BigDecimal loadCurrentThdC;

    @Schema(description = "负载A相电流峰值比 %")
    @JsonProperty("lcpra")
    private BigDecimal loadCurrentPeakRatioA;

    @Schema(description = "负载B相电流峰值比 %")
    @JsonProperty("lcprb")
    private BigDecimal loadCurrentPeakRatioB;

    @Schema(description = "负载C相电流峰值比 %")
    @JsonProperty("lcprc")
    private BigDecimal loadCurrentPeakRatioC;

    @Schema(description = "负载A相视在功率 kVA")
    @JsonProperty("lapa")
    private BigDecimal loadApparentPowerA;

    @Schema(description = "负载B相视在功率 kVA")
    @JsonProperty("lapb")
    private BigDecimal loadApparentPowerB;

    @Schema(description = "负载C相视在功率 kVA 0x1072")
    @JsonProperty("lapc")
    private BigDecimal loadApparentPowerC;

    @Schema(description = "负载A相有功功率 kW 0x1074")
    @JsonProperty("lacpa")
    private BigDecimal loadActivePowerA;

    @Schema(description = "负载B相有功功率 kW")
    @JsonProperty("lacpb")
    private BigDecimal loadActivePowerB;

    @Schema(description = "负载C相有功功率 kW")
    @JsonProperty("lacpc")
    private BigDecimal loadActivePowerC;

    @Schema(description = "负载A相无功功率 kVar")
    @JsonProperty("lrpa")
    private BigDecimal loadReactivePowerA;

    @Schema(description = "负载B相无功功率 kVar")
    @JsonProperty("lrpb")
    private BigDecimal loadReactivePowerB;

    @Schema(description = "负载C相无功功率 kVar")
    @JsonProperty("lrpc")
    private BigDecimal loadReactivePowerC;

    @Schema(description = "负载A相基波功率因数")
    @JsonProperty("lfpfa")
    private BigDecimal loadFundamentalPowerFactorA;

    @Schema(description = "负载B相基波功率因数")
    @JsonProperty("lfpfb")
    private BigDecimal loadFundamentalPowerFactorB;

    @Schema(description = "负载C相基波功率因数")
    @JsonProperty("lfpfc")
    private BigDecimal loadFundamentalPowerFactorC;

    @Schema(description = "负载A相功率因数")
    @JsonProperty("lpfa")
    private BigDecimal loadPowerFactorA;

    @Schema(description = "负载B相功率因数")
    @JsonProperty("lpfb")
    private BigDecimal loadPowerFactorB;

    @Schema(description = "负载C相功率因数 0x108A")
    @JsonProperty("lpfc")
    private BigDecimal loadPowerFactorC;

    @Schema(description = "负载A相负载率 % 0x108C")
    @JsonProperty("lra")
    private BigDecimal loadRateA;

    @Schema(description = "负载B相负载率 %")
    @JsonProperty("lrb")
    private BigDecimal loadRateB;

    @Schema(description = "负载C相负载率 %")
    @JsonProperty("lrc")
    private BigDecimal loadRateC;

    @Schema(description = "逆变A相电压 V 0x1092")
    @JsonProperty("iva")
    private BigDecimal inverterVoltageA;

    @Schema(description = "逆变B相电压 V")
    @JsonProperty("ivb")
    private BigDecimal inverterVoltageB;

    @Schema(description = "逆变C相电压 V")
    @JsonProperty("ivc")
    private BigDecimal inverterVoltageC;

    @Schema(description = "逆变电压AB线 V")
    @JsonProperty("ivlab")
    private BigDecimal inverterVoltageLineAB;

    @Schema(description = "逆变电压BC线 V")
    @JsonProperty("ivlbc")
    private BigDecimal inverterVoltageLineBC;

    @Schema(description = "逆变电压CA线 V 0x109C")
    @JsonProperty("ivlca")
    private BigDecimal inverterVoltageLineCA;

    @Schema(description = "逆变A相THDU %")
    @JsonProperty("ita")
    private BigDecimal inverterThduA;

    @Schema(description = "逆变B相THDU %")
    @JsonProperty("itb")
    private BigDecimal inverterThduB;

    @Schema(description = "逆变C相THDU %")
    @JsonProperty("itc")
    private BigDecimal inverterThduC;

    @Schema(description = "逆变电压频率 Hz")
    @JsonProperty("ivf")
    private BigDecimal inverterVoltageFrequency;

    @Schema(description = "逆变A相电流有效值 A")
    @JsonProperty("icea")
    private BigDecimal inverterCurrentEffectiveA;

    @Schema(description = "逆变B相电流有效值 A")
    @JsonProperty("iceb")
    private BigDecimal inverterCurrentEffectiveB;

    @Schema(description = "逆变C相电流有效值 A")
    @JsonProperty("icec")
    private BigDecimal inverterCurrentEffectiveC;

    @Schema(description = "逆变N相电流有效值 A")
    @JsonProperty("icen")
    private BigDecimal inverterCurrentEffectiveN;

    @Schema(description = "逆变A相电流THD %")
    @JsonProperty("icta")
    private BigDecimal inverterCurrentThdA;

    @Schema(description = "逆变B相电流THD %")
    @JsonProperty("ictb")
    private BigDecimal inverterCurrentThdB;

    @Schema(description = "逆变C相电流THD % 0x10B2")
    @JsonProperty("ictc")
    private BigDecimal inverterCurrentThdC;

    @Schema(description = "逆变A相电流峰值比 %")
    @JsonProperty("icpra")
    private BigDecimal inverterCurrentPeakRatioA;

    @Schema(description = "逆变B相电流峰值比 %")
    @JsonProperty("icprb")
    private BigDecimal inverterCurrentPeakRatioB;

    @Schema(description = "逆变C相电流峰值比 %")
    @JsonProperty("icprc")
    private BigDecimal inverterCurrentPeakRatioC;

    @Schema(description = "逆变A相视在功率 kVA")
    @JsonProperty("iapa")
    private BigDecimal inverterApparentPowerA;

    @Schema(description = "逆变B相视在功率 kVA")
    @JsonProperty("iapb")
    private BigDecimal inverterApparentPowerB;

    @Schema(description = "逆变C相视在功率 kVA 0x10BE")
    @JsonProperty("iapc")
    private BigDecimal inverterApparentPowerC;

    @Schema(description = "逆变A相有功功率 kW 0x10C0")
    @JsonProperty("iacpa")
    private BigDecimal inverterActivePowerA;

    @Schema(description = "逆变B相有功功率 kW")
    @JsonProperty("iacpb")
    private BigDecimal inverterActivePowerB;

    @Schema(description = "逆变C相有功功率 kW")
    @JsonProperty("iacpc")
    private BigDecimal inverterActivePowerC;

    @Schema(description = "逆变A相无功功率 kVar")
    @JsonProperty("ircpa")
    private BigDecimal inverterReactivePowerA;

    @Schema(description = "逆变B相无功功率 kVar")
    @JsonProperty("ircpb")
    private BigDecimal inverterReactivePowerB;

    @Schema(description = "逆变C相无功功率 kVar")
    @JsonProperty("ircpc")
    private BigDecimal inverterReactivePowerC;

    @Schema(description = "逆变A相基波功率因数")
    @JsonProperty("ifpfa")
    private BigDecimal inverterFundamentalPowerFactorA;

    @Schema(description = "逆变B相基波功率因数")
    @JsonProperty("ifpfb")
    private BigDecimal inverterFundamentalPowerFactorB;

    @Schema(description = "逆变C相基波功率因数")
    @JsonProperty("ifpfc")
    private BigDecimal inverterFundamentalPowerFactorC;

    @Schema(description = "逆变A相功率因数")
    @JsonProperty("ipfa")
    private BigDecimal inverterPowerFactorA;

    @Schema(description = "逆变B相功率因数")
    @JsonProperty("ipfb")
    private BigDecimal inverterPowerFactorB;

    @Schema(description = "逆变C相功率因数 0x10D6")
    @JsonProperty("ipfc")
    private BigDecimal inverterPowerFactorC;

    @Schema(description = "逆变A相负载率 % 0x10D8")
    @JsonProperty("iaa")
    private BigDecimal inverterRateA;

    @Schema(description = "逆变B相负载率 %")
    @JsonProperty("iab")
    private BigDecimal inverterRateB;

    @Schema(description = "逆变C相负载率 % 0x10DC")
    @JsonProperty("iac")
    private BigDecimal inverterRateC;

    @Schema(description = "DCAC温度 ℃ 0x10DE ~ 0x10E6")
    @JsonInclude(Include.NON_EMPTY)
    @JsonProperty("datl")
    private List<BigDecimal> dcacTempList;

    @Schema(description = "DCAC预留一路温度 ℃ 0x10E8")
    @JsonProperty("dar1")
    private BigDecimal dcacReserved1;

    @Schema(description = "DCAC预留一路温度 ℃ 0x10EA")
    @JsonProperty("dar2")
    private BigDecimal dcacReserved2;

    @Schema(description = "DCAC预留一路温度 ℃ 0x10EC")
    @JsonProperty("dar3")
    private BigDecimal dcacReserved3;

    @Schema(description = "正直流母线电压 V 0x10EE")
    @JsonProperty("pdbv")
    private BigDecimal positiveDcBusVoltage;

    @Schema(description = "负直流母线电压 V 0x10F0")
    @JsonProperty("ndbv")
    private BigDecimal negativeDcBusVoltage;

    @Schema(description = "年 0x10F2")
    @JsonProperty("y")
    private Integer year;

    @Schema(description = "月")
    @JsonProperty("mo")
    private Integer month;

    @Schema(description = "日")
    @JsonProperty("d")
    private Integer day;

    @Schema(description = "时")
    @JsonProperty("h")
    private Integer hour;

    @Schema(description = "分")
    @JsonProperty("mi")
    private Integer minute;

    @Schema(description = "秒 0x10FC")
    @JsonProperty("s")
    private Integer second;

    @Schema(description = "DCAC调试变量 0x10FE ~ 0x1108")
    @JsonInclude(Include.NON_EMPTY)
    @JsonProperty("dadpl")
    private List<BigDecimal> dcacDebugParamList;

//    ↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑

    // 油机相关数据先忽略

//    ↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓


    @Schema(description = "光伏电压 V")
    @JsonInclude(Include.NON_EMPTY)
    @JsonProperty("pvl")
    private List<BigDecimal> pvVoltageList;

    @Schema(description = "光伏电流 A")
    @JsonInclude(Include.NON_EMPTY)
    @JsonProperty("pcl")
    private List<BigDecimal> pvCurrentList;

    @Schema(description = "光伏功率 kW")
    @JsonInclude(Include.NON_EMPTY)
    @JsonProperty("ppl")
    private List<BigDecimal> pvPowerList;

    @Schema(description = "电池电压 V")
    @JsonInclude(Include.NON_EMPTY)
    @JsonProperty("bvl")
    private List<BigDecimal> batVoltageList;

    @Schema(description = "电池电流 A")
    @JsonInclude(Include.NON_EMPTY)
    @JsonProperty("bcl")
    private List<BigDecimal> batCurrentList;

    @Schema(description = "电池功率 kW")
    @JsonInclude(Include.NON_EMPTY)
    @JsonProperty("bpl")
    private List<BigDecimal> batPowerList;

    @Schema(description = "电池满容量百分比 %")
    @JsonInclude(Include.NON_EMPTY)
    @JsonProperty("bcpl")
    private List<BigDecimal> batCapacityPercentageList;

    @Schema(description = "电池满载支撑时间 h")
    @JsonInclude(Include.NON_EMPTY)
    @JsonProperty("bflstl")
    private List<BigDecimal> batFullLoadSupportTimeList;

    @Schema(description = "DCDC温度 ℃ 0x1220 ~ 0x1228")
    @JsonInclude(Include.NON_EMPTY)
    @JsonProperty("ddtl")
    private List<BigDecimal> dcdcTempList;

    @Schema(description = "DCDC预留路温度 ℃ 0x122A ~ 0x122E")
    @JsonInclude(Include.NON_EMPTY)
    @JsonProperty("ddrl")
    private List<BigDecimal> dcdcReservedList;

    @Schema(description = "正直流母线电压 V 0x1230")
    @JsonProperty("pdbv2")
    private BigDecimal positiveDcBusVoltage2;

    @Schema(description = "负直流母线电压 V 0x1232")
    @JsonProperty("ndbv2")
    private BigDecimal negativeDcBusVoltage2;

    @Schema(description = "平衡电路电流 A 0x1234")
    @JsonProperty("bcc")
    private BigDecimal balancedCircuitCurrent;

    @Schema(description = "风扇档位 0x1236")
    @JsonProperty("fg")
    private BigDecimal fanGear;

    @Schema(description = "电池充电次数 0x1238、0x123C")
    @JsonInclude(Include.NON_EMPTY)
    @JsonProperty("bctl")
    private List<Integer> batChargeTimesList;

    @Schema(description = "电池放电次数 0x123A、0x123E")
    @JsonInclude(Include.NON_EMPTY)
    @JsonProperty("bdtl")
    private List<Integer> batDischargeTimesList;

    @Schema(description = "总购电度数 kW·h 0x1240")
    @JsonProperty("pea")
    private BigDecimal purchaseElecAll;

    @Schema(description = "总馈电度数 kW·h 0x1242")
    @JsonProperty("fea")
    private BigDecimal feedEelcAll;

    @Schema(description = "光伏负载率 %")
    @JsonInclude(Include.NON_EMPTY)
    @JsonProperty("plrl")
    private List<BigDecimal> pvLoadRateList;

    @Schema(description = "电池负载率 %")
    @JsonInclude(Include.NON_EMPTY)
    @JsonProperty("blrl")
    private List<BigDecimal> batLoadRateList;

    @Schema(description = "DCDC调试变量 0x124C ~ 0x1256")
    @JsonInclude(Include.NON_EMPTY)
    @JsonProperty("dddpl")
    private List<BigDecimal> dcdcDebugParamList;

    @Schema(description = "直流变换器电压 V 0x1258")
    @JsonProperty("dcv")
    private BigDecimal dcConverterVoltage;

    @Schema(description = "直流变换器电流 A 0x125A")
    @JsonProperty("dcc")
    private BigDecimal dcConverterCurrent;

    @Schema(description = "总光伏发电量 kW·h 0x125C")
    @JsonProperty("ka")
    private BigDecimal kwhAll;

    @Schema(description = "总电池充电量 kW·h")
    @JsonProperty("bca")
    private BigDecimal batChargeAll;

    @Schema(description = "总电池放电量 kW·h")
    @JsonProperty("bda")
    private BigDecimal batDischargeAll;

    @Schema(description = "总负载用电量 kW·h")
    @JsonProperty("lka")
    private BigDecimal loadKwhAll;

    @Schema(description = "当天光伏发电量")
    @JsonProperty("kt")
    private BigDecimal kwhToday;

    @Schema(description = "当天购电度数")
    @JsonProperty("pet")
    private BigDecimal purchaseElecToday;

    @Schema(description = "当天馈电度数")
    @JsonProperty("fet")
    private BigDecimal feedEelcToday;

    @Schema(description = "当天电池充电量")
    @JsonProperty("bct")
    private BigDecimal batChargeToday;

    @Schema(description = "当天电池放电量")
    @JsonProperty("bdt")
    private BigDecimal batDischargeToday;

    @Schema(description = "当天负载用电量 kW·h 0x126E")
    @JsonProperty("lkt")
    private BigDecimal loadKwhToday;

    @Schema(description = "光伏总功率 kW 0x1270")
    @JsonProperty("ppa")
    private BigDecimal pvPowerAll;

    @Schema(description = "电池总功率 kW")
    @JsonProperty("bpa")
    private BigDecimal batPowerAll;

    @Schema(description = "负载总功率 kW")
    @JsonProperty("lpa")
    private BigDecimal loadPowerAll;

    @Schema(description = "电网总功率 kW")
    @JsonProperty("gpa")
    private BigDecimal gridPowerAll;

    @Schema(description = "重要负载总功率 kW")
    @JsonProperty("clpa")
    private BigDecimal criticalLoadPowerAll;

    @Schema(description = "一般负载总功率 kW 0x127A")
    @JsonProperty("glpa")
    private BigDecimal generalLoadPowerAll;

}
