package com.cdz360.base.model.es.vo;

import com.cdz360.base.model.iot.type.EssEquipType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(title = "设备(PCS/电表)单日计费数据")
public class EssEquipDailyFee {

    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Schema(title = "日期")
    private LocalDate date;

    @Schema(title = "设备类型")
    private EssEquipType equipType;

    @Schema(title = "设备(PCS/电表)设备编号")
    private String dno;

    @Schema(title = "计费模板ID", description = "日内如果计费有变更,仅记录其中一个,主要供参考")
    @JsonInclude(Include.NON_NULL)
    private Long priceId;

    @Schema(title = "时段详情")
    @JsonInclude(Include.NON_NULL)
    private List<EssFeeItem> items = new ArrayList<>();

    @Schema(title = "充电电量", description = "单位 kwh")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal inKwh;

    @Schema(title = "放电电量", description = "单位 kwh")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal outKwh;

    @Schema(title = "充电金额", description = "单位: 元")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal inFee;

    @Schema(title = "放电金额", description = "单位: 元")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal outFee;

    @Schema(title = "开始充电电量", description = "当日开始时的总充电电量, 单位 kwh")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal totalInKwh;

    @Schema(title = "开始放电电量", description = "当日开始时的总放电电量, 单位 kwh")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal totalOutKwh;

}
