package com.cdz360.base.model.es.dto;

import com.cdz360.base.model.es.type.EssAlarmType;
import com.cdz360.base.model.es.type.WarnDeviceType;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class EssAlarmTinyDto {



    @Schema(description = "告警类型. 1 alert 一次性提醒,不需要结束; 2 alarm 有开始和结束时间")
    private EssAlarmType alarmType;

    @Schema(description = "告警码, XxxErrorCode.name")
    private String code;
    @Schema(description = "告警级别")
    private Integer level;

    @Schema(description = "告警状态", example = "0 未结束; 1 自动结束; 2 手动结束")
    private Integer status;

    @Schema(description = "告警发生时间")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonInclude(Include.NON_NULL)
    private LocalDateTime startTime;



}
