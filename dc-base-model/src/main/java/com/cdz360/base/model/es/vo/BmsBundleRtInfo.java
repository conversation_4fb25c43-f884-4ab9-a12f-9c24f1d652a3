package com.cdz360.base.model.es.vo;

import com.cdz360.base.model.es.type.BmsAlarmCode;
import com.cdz360.base.model.es.type.BmsStackStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 电池蔟信息
 */

@Data
@Schema(title = "电池蔟信息")
@JsonInclude(Include.NON_NULL)
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class BmsBundleRtInfo extends EssBaseRtInfo<BmsStackStatus, BmsAlarmCode> {


    @Schema(title = "电池堆设备序列号")
    @JsonInclude(Include.NON_EMPTY)
    private String stackDno;


    @Schema(title = "BMS设备序列号")
    @JsonInclude(Include.NON_EMPTY)
    private String bmsDno;


    @Schema(title = "电池蔟在电池堆内的序号")
    @JsonInclude(Include.NON_NULL)
    private Integer idx;


    @Schema(title = "是否为告警状态")
    @JsonInclude(Include.NON_NULL)
    private Boolean warnStatus;


    @Schema(title = "是否为故障状态")
    @JsonInclude(Include.NON_NULL)
    private Boolean errorStatus;

    @Schema(title = "充满标志")
    private Boolean full;

    @Schema(title = "放空标志")
    private Boolean empty;

//    @Deprecated
//    @Schema(title = "主正继电器状态", description = "1表示闭合")
//    @JsonInclude(Include.NON_NULL)
//    private Integer positiveRelayStatus;
//
//    @Deprecated
//    @Schema(title = "主负继电器状态", description = "1表示闭合")
//    @JsonInclude(Include.NON_NULL)
//    private Integer negativeRelayStatus;
//
//
//    @Deprecated
//    @Schema(title = "主预充继电器状态", description = "")
//    @JsonInclude(Include.NON_NULL)
//    private Integer preChargeRelayStatus;
//
//
//    @Deprecated
//    @Schema(title = "簇断路器状态", description = "")
//    @JsonInclude(Include.NON_NULL)
//    private Integer bundleBreakerStatus;
//
//
//    @Deprecated
//    @Schema(title = "风扇继电器状态", description = "")
//    @JsonInclude(Include.NON_NULL)
//    private Integer fanRelayStatus;
//
//
//    @Deprecated
//    @Schema(title = "干接点状态", description = "")
//    @JsonInclude(Include.NON_NULL)
//    private Integer dryContactStatus;

//    @Schema(title = "文本值", description = "文本信息")
//    @JsonInclude(Include.NON_NULL)
//    private List<StringVal> textValues;
//
//    @Schema(title = "各种设定值", description = "电池簇级别的设定值")
//    @JsonInclude(Include.NON_NULL)
//    private List<RegisterRwValue> cfgValues;
//
//    @Schema(title = "电池簇信号量", description = "电池簇信号量")
//    @JsonInclude(Include.NON_NULL)
//    private List<SignalVal> signals;
//
//    @Schema(title = "电池蔟故障码列表", description = "")
//    @JsonInclude(Include.NON_NULL)
//    private List<BmsAlarmCode> bmsErrorList;
}
