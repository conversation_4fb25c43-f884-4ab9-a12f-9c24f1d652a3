package com.cdz360.base.model.base.type;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum IotGwCmdType2 implements DcEnum {

    UNKNOWN(0, "未知"),

    /**
     * 微网控制器指令 1~99
     */
    MGC_STATUS(1, "上报mgc状态"),
    MGC_REBOOT_APP(2, "重启mgc程序"),
    MGC_REBOOT_OS(3, "重启操作系统"),
    MGC_UPGRADE_APP(4, "在线升级mgc程序"),
    MGC_UPGRADE_OS(5, "在线升级操作系统"),
    MGC_TUNNEL_START(6, "开启tunnel"),   // 开启网关远程debug通道
    MGC_TUNNEL_STOP(7, "关闭tunnel"),    // 关闭网关远程debug通道

    /**
     * 电表指令从 100 ~ 199
     */
    READ_METER_INFO(101, "获取电表信息"),
    MODIFY_METER_INFO(109, "设置电表配置"),

    /**
     * 光伏指令从 200 ~ 299
     */
    MODIFY_GTI_CFG(201, "设置逆变器配置"), // 设置逆变器配置
    GET_GTI_INFO(202, "获取逆变器信息"),
    UPLOAD_GTI_DATA_FILE(203, "上传逆变器数据文件"),

    /**
     * 充电桩 300 ~ 399
     */
    CE_CHARGE_START(301, "开启充电"),   //
    CE_CHARGE_STOP(302, "停止充电"),    //
    CE_CHARGE_SOC_CTRL(303, "SOC控制"), //
    CE_CHARGE_POWER_CTRL(304, "功率分配"), //
    CE_TRANSFORMER_UPDATE(305, "变压器分配功率更新"), //
    CE_GET_CFG(311, "获取桩配置"),   //
    CE_MODIFY_CFG(312, "更新桩配置"),       //
    CE_UPGRADE(313, "桩远程升级"),   //
    CE_REBOOT(314, "桩远程重启"),    //
    CE_DEBUG(315, "桩远程debug指令"),   //
    CE_GET_MODULE(316, "获取桩模块信息"), //
    CE_LOCK(317, "充电桩远程锁定"),
    CE_COLLECT_STATUS(318, "收集并上报桩状态"),
    CE_GET_TRANSFORMER_INFO(319, "获取变压器(功率分配)信息"),
    //LOCAL_DEBUG, // 本地调试
    CE_OCPP_CLOUD_OPERATIONS(320, "OCPP云端操作"),

    /**
     * 储能指令从 400 ~ 499
     */
    ESS_D_BASE_INFO(401, "ESS基础信息"),
    MODIFY_ESS_CFG(402, "设置ESS配置"),
    UPLOAD_ESS_DATA_FILE(403, "上传储能数据文件"),
    GET_ESS_RT_DATA(404, "获取ESS设备实时数据"),
    GET_ESS_EQUIP(405, "获取ESS设备列表"),
    GET_ESS_CFG(406, "获取ESS设备配置信息"),
    ESS_D_POWER_OP(407, "ESS逆变器电源操作"), // 开机/关机
    ESS_D_UPGRADE_OP(408, "ESS软件升级"),

    EMU_D_CLEAR_ALARM(409, "下发告警清除"), // 云 -> EMU
    EMU_D_MODIFY_CFG(410, "下发EMU配置"), // 云 -> EMU
    EMU_U_MODIFY_CFG_RESULT(411, "EMU配置结果反馈"), // EMU -> 云
    EMU_D_GET_INFO(412, "获取EMU相关设备信息"),
    EMU_D_GET_RT_DATA(413, "获取EMU运行数据"),
    EMU_D_GET_TRADE_LOG(414, "获取EMU通信日志"),
    EMU_D_REBOOT(415, "EMU远程重启"),

    EMU_D_SOFT_START(416, "EMU启用"), // EMU软开机

    EMU_D_SOFT_STOP(417, "EMU停用"),  // EMU软关机

    EMU_MODIFY_EQUIP_CFG_REQ(420, "修改EMU管理的设备设置项,如修改空调设定值等"),
    EMU_MODIFY_EQUIP_CFG_RESULT(421, "修改EMU管理的设备设置项的返回结果");


    @JsonValue
    private final int code;
    private final String desc;

    IotGwCmdType2(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static IotGwCmdType2 valueOf(Object codeIn) {
        if (codeIn == null) {
            return IotGwCmdType2.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof IotGwCmdType2) {
            return (IotGwCmdType2) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (IotGwCmdType2 type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return IotGwCmdType2.UNKNOWN;
    }
}
