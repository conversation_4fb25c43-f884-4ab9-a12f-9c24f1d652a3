package com.cdz360.base.model.es.vo.hi.module;


import com.cdz360.base.model.es.type.hi.BmsProtocol;
import com.cdz360.base.model.es.type.hi.MonitorPvType;
import com.cdz360.base.model.es.type.hi.MonitorPvVendor;
import com.cdz360.base.model.es.type.hi.SinexcelErrorCode;
import com.cdz360.base.model.es.vo.ChargeDisChargePeriods;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "监控设备")
@Data
@Accessors(chain = true)
@JsonInclude(Include.NON_NULL)
public class InverterDevMonitor {

    /*
    @JsonProperty("n")
    @Schema(description = "国家（对应相关的安规认证）")
    private MonitorCertificationNation nation;

    @JsonProperty("vs")
    @Schema(description = "电压制式")
    private MonitorVoltageSystem voltageSystem;

    @JsonProperty("gm")
    @Schema(description = "并离网选择")
    private MonitorGridMode gridMode;

    @JsonProperty("ba")
    @Schema(description = "电池接入；0：禁止；1：使能；")
    private Boolean batteryAccess;
    */

    @JsonProperty("cka")
    @Schema(description = "清除总电量")
    private Integer clearKwhAll;

    @JsonProperty("ckt")
    @Schema(description = "清除当日电量")
    private Integer clearKwhToday;

    @JsonProperty("mcr")
    @Schema(description = "电表CT变比")
    private BigDecimal meterCtRatio;

    @JsonProperty("mm")
    @Schema(description = "电表型号")
    private Integer meterModel;

    @JsonProperty("bpl")
    @Schema(description = "电池协议")
    private List<BmsProtocol> batteryProtocolList;

    /*
    @JsonProperty("pa")
    @Schema(description = "PV接入；0：禁止；1：使能；")
    private Boolean pvAccess;
    */

    @JsonProperty("pt")
    @Schema(description = "PV类型")
    private MonitorPvType pvType;

    @JsonProperty("pv")
    @Schema(description = "PV厂商 0xC00E")
    private MonitorPvVendor pvVendor;

    /*
    @JsonProperty("ps")
    @Schema(description = "并机设置 0：单机；1：并机；")
    private Boolean parallelSetup;
    */

    @JsonProperty("fdr")
    @Schema(description = "恢复出厂设置 0：禁止；1：使能；")
    private Boolean factoryDataReset;

    @Schema(description = "定时充放电时间段 0xC014->0xC042")
    @JsonProperty("periods")
    private List<ChargeDisChargePeriods> periods;

    /*
    @JsonProperty("os")
    @Schema(description = "监控运行状态 0：离线；1：在线；")
    private Boolean opStatus;
    */

    @JsonProperty("al")
    @Schema(description = "监控告警")
    private List<SinexcelErrorCode> alarmList;

    @JsonProperty("rc")
    @Schema(description = "重复周期 0xC0B4")
    private Integer repeatCycle;

    @JsonProperty("est")
    @Schema(description = "生效开始时间(Unix时间戳) 0xC0B6")
    private Long effectiveStartTime;

    @JsonProperty("eet")
    @Schema(description = "生效结束时间(Unix时间戳) 0xC0B8")
    private Long effectiveEndTime;

    @JsonProperty("cpps")
    @Schema(description = "充电功率时段")
    @JsonInclude(Include.NON_EMPTY)
    private List<BigDecimal> chargingPowerPeriods;

    @JsonProperty("dpps")
    @Schema(description = "放电功率时段")
    @JsonInclude(Include.NON_EMPTY)
    private List<BigDecimal> dischargingPowerPeriods;

}
