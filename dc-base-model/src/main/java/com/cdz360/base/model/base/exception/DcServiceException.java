package com.cdz360.base.model.base.exception;

import com.cdz360.base.model.base.constants.DcConstants;
import org.slf4j.event.Level;

public class DcServiceException extends DcException {
    private static final int STATUS = DcConstants.KEY_RES_CODE_SERVICE_ERROR;


    public DcServiceException(int status, String msg) {
        super(status, msg);
    }

    public DcServiceException(String msg) {
        super(STATUS, msg);
    }

    public DcServiceException(String msg, Throwable e) {
        super(STATUS, msg, e);
    }


    public DcServiceException(String msg, Level logLevel) {
        super(STATUS, msg, logLevel);
    }


    public DcServiceException(String msg, Level logLevel, Throwable e) {
        super(STATUS, msg, logLevel, e);
    }


    public DcServiceException(int status, String msg, Level logLevel) {
        super(status, msg, logLevel);
    }
}
