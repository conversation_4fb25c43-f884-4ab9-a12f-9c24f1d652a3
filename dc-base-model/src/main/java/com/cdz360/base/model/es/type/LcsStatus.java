package com.cdz360.base.model.es.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum LcsStatus implements DcEnum {

    SLEEP(0, "休眠"),
    COOLING(1, "制冷"),
    HEATING(2, "制热"),
    ;

    @JsonValue
    private final int code;
    private final String desc;

    LcsStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static LcsStatus valueOf(Object codeIn) {
        if (codeIn == null) {
            return null;
        }
        int code = 0;
        if (codeIn instanceof LcsStatus) {
            return (LcsStatus) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (LcsStatus type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }

}
