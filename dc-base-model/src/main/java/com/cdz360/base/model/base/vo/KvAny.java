package com.cdz360.base.model.base.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class KvAny<T> {

    @JsonProperty("k")
    private String key;


    @JsonProperty("v")
    private T value;





    public static <T> KvAny<T> of(String key, T value) {
        var ret = new KvAny<T>();
        ret.setKey(key);
        ret.setValue(value);
        return ret;
    }


    public static KvString of(String key, String value) {
        var ret = new KvString();
        ret.setKey(key);
        ret.setValue(value);
        return ret;
    }

    public static KvLong of(String key, Long value) {
        var ret = new KvLong();
        ret.setKey(key);
        ret.setValue(value);
        return ret;
    }

    public static KvInteger of(String key, Integer value) {
        var ret = new KvInteger();
        ret.setKey(key);
        ret.setValue(value);
        return ret;
    }


}
