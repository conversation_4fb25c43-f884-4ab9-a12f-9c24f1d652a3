package com.cdz360.base.model.es.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Schema(title = "储能策略配置", description = "防逆流、防超限. 数据以流入母线为正，流出为负")
@Accessors(chain = true)
public class EssStrategyCfgDto {

    @Schema(title = "无功优化使能", description = "true-开启;false-关闭")
    private Boolean qg; // Qg

    @Schema(title = "功率因数", description = "功率因数, -1~1")
    private BigDecimal pf; // power factor

    @Schema(title = "防逆流使能", description = "true:防逆流功能打开;false:不打开,可无限逆流")
    private Boolean fnl;

    @Schema(title = "逆流功率", description = "单位kw, 正数表示预留，负数表示最大可逆功率")
    private BigDecimal pnl;

    @Schema(title = "逆流系数使能", description = "true:允许;false:不允许")
    private Boolean nxs;

    @Schema(title = "防逆流系数", description = "0.6 ~ 0.99")
    private BigDecimal fnxs;

    @Schema(title = "防超限使能", description = "true:允许超限")
    private Boolean fcx;

    @Schema(title = "功率上调系数", description = "单机取1，2个推荐配置2.0~2.1，根据站点实际情况调整")
    private BigDecimal increaseFactor;

    @Schema(title = "功率下调系数", description = "单机取1，2个推荐配置1~1.8，根据站点实际情况调整")
    private BigDecimal decreaseFactor;

    @Schema(title = "协议功率", description = "关口功率, 单位kw, 一般为正数")
    private BigDecimal px;

    @Schema(title = "PCS自动启停开关",
        description = "开启后,当PCS进入不充不放状态时,会下发关机/待机指令给PCS",
        defaultValue = "false")
    private Boolean pcsAutoStop;
    @Schema(title = "PCS待机时长",
        description = "仅当 pcsAutoStop 开启时有效. 在充电达到SOC设定值后等待一定时间,下发关机指令给PCS.单位'秒'",
        defaultValue = "60")
    private Integer pcsIdleTimeout;

    @Schema(title = "SOC回差", description = "充满后SOC下降超过回差值再重新启动充电,建议配置2~3")
    private BigDecimal socBacklash;


    @Schema(title = "涓流充电开关", description = "检查BMS允许的最大充电功率,如果超过BMS的最大允许充电功率,下调充电功率")
    private Boolean trickleCharge;


    @Schema(title = "slave模式", description = "开启slave模式后,本地不取关口表数据,由master发送", defaultValue = "false")
    private Boolean slave;

}
