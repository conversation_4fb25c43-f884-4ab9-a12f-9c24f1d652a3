package com.cdz360.base.model.es.vo;

import com.cdz360.base.model.es.type.BmsAlarmCode;
import com.cdz360.base.model.es.type.BmsStackChargeable;
import com.cdz360.base.model.es.type.BmsStackStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;


@Data
@Accessors(chain = true)
@Schema(title = "电池堆信息")
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class BmsStackRtInfo extends EssBaseRtInfo<BmsStackStatus, BmsAlarmCode> {

    @Schema(title = "BMS设备序列号")
    @JsonInclude(Include.NON_EMPTY)
    private String bmsDno;

    @Schema(title = "是否为告警状态")
    @JsonInclude(Include.NON_NULL)
    private Boolean warnStatus;


    @Schema(title = "是否为故障状态")
    @JsonInclude(Include.NON_NULL)
    private Boolean errorStatus;


    @Schema(title = "是否允许'充电'标志")
    @JsonInclude(Include.NON_NULL)
    private BmsStackChargeable chargeable;

    @Schema(title = "是否允许'放电'标志")
    @JsonInclude(Include.NON_NULL)
    private Boolean dischargeable;


    @Schema(title = "投入使用的电池蔟ID列表")
    @JsonInclude(Include.NON_NULL)
    private List<Integer> bundleIds = new ArrayList<>();


    @Deprecated
    @Schema(title = "充放电完成标志", description = "1 充满; 2 放空； 其他无意义")
    @JsonInclude(Include.NON_NULL)
    private Integer chargeResult;


    @Schema(title = "主正继电器状态", description = "1表示闭合")
    @JsonInclude(Include.NON_NULL)
    private Integer positiveRelayStatus;


    @Schema(title = "主负继电器状态", description = "1表示闭合")
    @JsonInclude(Include.NON_NULL)
    private Integer negativeRelayStatus;


    @Schema(title = "主预充继电器状态", description = "")
    @JsonInclude(Include.NON_NULL)
    private Integer preChargeRelayStatus;


    @Schema(title = "SBAU主断路器状态", description = "")
    @JsonInclude(Include.NON_NULL)
    private Integer sbauBreakerStatus;


    @Schema(title = "风扇继电器状态", description = "")
    @JsonInclude(Include.NON_NULL)
    private Integer fanRelayStatus;


    @Schema(title = "SBAU干接点状态", description = "")
    @JsonInclude(Include.NON_NULL)
    private Integer sbauDryContactStatus;


    @Schema(title = "堆里的电池蔟数量", description = "")
    @JsonInclude(Include.NON_NULL)
    private Integer bundleNum;


    @Schema(title = "蔟里的pack数量", description = "")
    @JsonInclude(Include.NON_NULL)
    private Integer packNum;


    @Schema(title = "pack里的电芯数量", description = "")
    @JsonInclude(Include.NON_NULL)
    private Integer batteryNum;


    @Schema(title = "电池蔟列表", description = "")
    @JsonInclude(Include.NON_NULL)
    private List<BmsBundleRtInfo> bundleList;
}
