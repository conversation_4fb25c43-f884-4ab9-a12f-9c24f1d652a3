package com.cdz360.base.model.es.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Schema(title = "EMU单日计费数据")
public class EmuDailyFeeFull extends EmuDailyFee {

    @Schema(title = "各电表的计费数据", description = "根据电表数据计算的每日充放电量和金额数据")
    @JsonInclude(Include.NON_NULL)
    private List<EssEquipDailyFee> meterDailyList = new ArrayList<>();

    @Schema(title = "各PCS的计费数据")
    @JsonInclude(Include.NON_NULL)
    private List<EssEquipDailyFee> pcsDailyList = new ArrayList<>();


}
