package com.cdz360.base.model.base.param;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "列表查询请求")
@Data
@Accessors(chain = true)
public class BaseListParam {

    @Schema(description = "搜索关键词")
    private String sk = "";

    @Schema(description = "是否有效")
    private Boolean enable;

    @Schema(description = "是否统计总数")
    private Boolean total;

    @Schema(description = "分页开始")
    private Long start;

    @Schema(description = "查询最大数量")
    private Integer size;

    @Schema(description = "排序方式")
    private List<SortParam> sorts;


    @JsonIgnore
    public BaseListParam setStartIfNull(long defVal) {
        if (this.start == null || this.start < 0L) {
            this.start = defVal;
        }
        return this;
    }

    @JsonIgnore
    public BaseListParam setSizeIfNull(int defVal) {
        if (this.size == null || this.size < 0) {
            this.size = defVal;
        }
        return this;
    }

    @JsonIgnore
    public BaseListParam setSizeIfNull(int defVal, int maxVal) {
        if (this.size == null || this.size < 0) {
            this.size = defVal;
        } else if (this.size > maxVal) {
            this.size = maxVal;
        }
        return this;
    }

    /**
     * 如果 size 超过 maxVal, 自动修正 size 的值
     */
    @JsonIgnore
    public BaseListParam setSizeIfMax(int maxVal) {
        if (this.size != null && this.size > maxVal) {
            this.size = maxVal;
        }
        return this;
    }

}
