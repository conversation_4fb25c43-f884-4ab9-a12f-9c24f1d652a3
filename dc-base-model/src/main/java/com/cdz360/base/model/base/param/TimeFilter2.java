package com.cdz360.base.model.base.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class TimeFilter2 {

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(title = "开始时间", description = "开始时间")
    private LocalDateTime startTime;


    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(title = "结束时间", description = "结束时间")
    private LocalDateTime endTime;


    /**
     * @return 开始时间为今日0点，结束为明日0点
     */
    public static TimeFilter2 thisDay() {
        LocalDate now = LocalDate.now();
        TimeFilter2 result = new TimeFilter2();
        result.setStartTime(now.atStartOfDay());
        result.setEndTime(now.plusDays(1).atStartOfDay());
        return result;
    }

    /**
     * @return 开始时间为昨日0点，结束为今日0点
     */
    public static TimeFilter2 lastDay() {
        LocalDate now = LocalDate.now();
        TimeFilter2 result = new TimeFilter2();
        result.setStartTime(now.minusDays(1).atStartOfDay());
        result.setEndTime(now.atStartOfDay());
        return result;
    }


    /**
     * @return 开始时间为前N日的0点，结束为今日0点
     */
    public static TimeFilter2 lastNDay(int nDays) {
        LocalDate now = LocalDate.now();
        TimeFilter2 result = new TimeFilter2();
        result.setStartTime(now.minusDays(nDays).atStartOfDay());
        result.setEndTime(now.atStartOfDay());
        return result;
    }

    /**
     * @return 开始时间为本月1号0点，结束为下月1号0点
     */
    public static TimeFilter2 thisMonth() {
        LocalDate now = LocalDate.now();
        TimeFilter2 result = new TimeFilter2();
        result.setStartTime(now.withDayOfMonth(1).atStartOfDay());
        result.setEndTime(now.withDayOfMonth(1).plusMonths(1).atStartOfDay());
        return result;
    }


    /**
     * @return 开始时间为上月1号0点，结束为本月1号0点
     */
    public static TimeFilter2 lastMonth() {
        LocalDate now = LocalDate.now();
        TimeFilter2 result = new TimeFilter2();
        result.setStartTime(now.withDayOfMonth(1).minusMonths(1).atStartOfDay());
        result.setEndTime(now.withDayOfMonth(1).atStartOfDay());
        return result;
    }

    /**
     * @return 开始时间为前N月的1号0点，结束为本月1号0点
     */
    public static TimeFilter2 lastNMonth(int nMonth) {
        LocalDate now = LocalDate.now();
        TimeFilter2 result = new TimeFilter2();
        result.setStartTime(now.withDayOfMonth(1).minusMonths(nMonth).atStartOfDay());
        result.setEndTime(now.withDayOfMonth(1).atStartOfDay());
        return result;
    }

}