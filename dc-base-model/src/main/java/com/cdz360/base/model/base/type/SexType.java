package com.cdz360.base.model.base.type;

import lombok.Getter;

@Getter
public enum SexType implements DcEnum {
    UNKNOWN(0, "未知"),
    MALE(1, "男"),
    FEMALE(2, "女"),
    ;

    private final int code;
    private final String desc;

    SexType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static SexType valueOf(int code) {
        for (SexType status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return SexType.UNKNOWN;
    }
}
