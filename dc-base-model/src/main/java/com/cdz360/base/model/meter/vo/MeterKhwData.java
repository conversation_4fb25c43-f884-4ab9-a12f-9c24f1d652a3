package com.cdz360.base.model.meter.vo;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class MeterKhwData {

    // 组合有功, 已乘变比  0.01kWh
    private MeterKwhItem combine;

    // 正向有功, 已乘变比 0.01kWh
    private MeterKwhItem positive;

    // 反向有功, 已乘变比 0.01kWh
    private MeterKwhItem negative;

    // 组合无功1, 已乘变比 0.01kvarh
    private MeterKwhItem combineReactive1;

    // 组合无功2, 已乘变比 0.01kvarh
    private MeterKwhItem combineReactive2;

    // 一象限无功, 已乘变比 0.01kvarh
    private MeterKwhItem quadrantReactive1;

    // 二象限无功, 已乘变比 0.01kvarh
    private MeterKwhItem quadrantReactive2;

    // 三象限无功, 已乘变比 0.01kvarh
    private MeterKwhItem quadrantReactive3;

    // 四象限无功, 已乘变比 0.01kvarh
    private MeterKwhItem quadrantReactive4;

    // 正向视在, 已乘变比 0.01kVAh
    private MeterKwhItem positiveApparent;

    // 反向视在, 已乘变比 0.01kVAh
    private MeterKwhItem negativeApparent;
}
