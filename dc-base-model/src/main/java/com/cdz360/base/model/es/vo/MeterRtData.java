package com.cdz360.base.model.es.vo;

import com.cdz360.base.model.meter.vo.MeterAbcData;
import com.cdz360.base.model.meter.vo.MeterKhwData;
import com.cdz360.base.model.meter.vo.MeterTransformationRatio;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 电表设备实时数据
 */
@Data
@Accessors(chain = true)
@Schema(title = "电表实时数据")
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class MeterRtData extends EssBaseRtData {


    // 电量相关数据
    private MeterKhwData kwh;

    // 上一结算日（上月）电量数据
    private MeterKhwData kwhL1;

    // 上上一结算日（上上月）电量数据
    private MeterKhwData kwhL2;

    // ABC项电压、电流、功率相关数据
    private MeterAbcData abc;

    // 电压/电流变比
    private MeterTransformationRatio tr;


}
