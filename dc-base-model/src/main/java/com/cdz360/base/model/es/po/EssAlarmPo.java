package com.cdz360.base.model.es.po;

import com.cdz360.base.model.es.type.EssAlarmType;
import com.cdz360.base.model.es.type.WarnDeviceType;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class EssAlarmPo {


    @Schema(description = "告警ID")
    private Long id;

    @Schema(description = "网关编号,仅用于户储")
    @JsonInclude(Include.NON_EMPTY)
    private String gwno;
    @Schema(description = "所属场站ID,用于工商储")
    @JsonInclude(Include.NON_EMPTY)
    private String siteId;

    @Schema(description = "所属场站名称,用于工商储")
    @JsonInclude(Include.NON_EMPTY)
    private String siteName;

    @Schema(description = "发生告警的ESS编号,用于工商储")
    @JsonInclude(Include.NON_EMPTY)
    private String essDno;

    @Schema(description = "发生告警的ESS名称")
    @JsonInclude(Include.NON_EMPTY)
    private String essName;

    @Schema(description = "子设备类型(告警目标设备)")
    private EssEquipType equipType;

    @Schema(description = "发生告警的子设备编号")
    @JsonInclude(Include.NON_EMPTY)
    private String equipDno;

    @Schema(description = "发生告警的子设备名称")
    @JsonInclude(Include.NON_EMPTY)
    private String equipName;

    @Schema(description = "告警类型. 1 alert 一次性提醒,不需要结束; 2 alarm 有开始和结束时间")
    private EssAlarmType alarmType;

    @Schema(description = "发生告警主体设备归属类型", example = "充电桩/户用储能ESS/商户储能ESS")
    private WarnDeviceType equipCategory;


    @Schema(description = "告警码文字编码, XxxErrorCode.name")
    private String code;

//    @Schema(description = "告警码数字ID, XxxErrorCode.code")
//    private Integer alarmId;

    @Schema(description = "告警级别")
    private Integer level;

    @Schema(description = "告警状态", example = "0 未结束; 1 自动结束; 2 手动结束")
    private Integer status;

    @Schema(description = "告警发生时间,设备本地时间")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonInclude(Include.NON_NULL)
    private LocalDateTime startTime;

    @Schema(description = "告警结束时间,设备本地时间, alert 无结束时间")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonInclude(Include.NON_NULL)
    private LocalDateTime endTime;


    @Schema(description = "告警内容创建时间,服务器时间. 告警持续时间=服务器当前时间 - createTime")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonInclude(Include.NON_NULL)
    private LocalDateTime createTime;
    @Schema(description = "告警内容更新时间,服务器时间.")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonInclude(Include.NON_NULL)
    private LocalDateTime updateTime;


    @Schema(description = "时区")
    @JsonInclude(Include.NON_EMPTY)
    private String tz;

    @Schema(description = "设备关联用户所在国家地区代码(Alpha-3 code), 用于户储",
        externalDocs = @ExternalDocumentation(
            description = "iso-3166 Country Codes",
            url = "https://www.iso.org/obp/ui/#search"), hidden = true)
    private String cc;


}
