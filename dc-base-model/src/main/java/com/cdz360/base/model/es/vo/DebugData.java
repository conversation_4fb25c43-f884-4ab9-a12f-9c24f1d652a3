package com.cdz360.base.model.es.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "调试数据")
@Data
@Accessors(chain = true)
public class DebugData {

    //    0x10DE	4	DCAC_温度1	℃		测量-调试
    //    0x10E0	4	DCAC_温度2	℃		测量-调试
    //    0x10E2	4	DCAC_温度3	℃		测量-调试
    //    0x10E4	4	DCAC_温度4	℃		测量-调试
    //    0x10E6	4	DCAC_温度5	℃		测量-调试
    //    0x10E8	4	DCAC_预留一路温度显示	先不显示 ℃		测量-调试
    //    0x10EA	4	DCAC_预留一路温度显示	先不显示 ℃		测量-调试
    //    0x10EC	4	DCAC_预留一路温度显示	先不显示 ℃		测量-调试
    //    0x10EE	4	正直流母线电压	V		测量-调试
    //    0x10F0	4	负直流母线电压	V		测量-调试
    //    0x10F2	4	年			测量-调试
    //    0x10F4	4	月			测量-调试
    //    0x10F6	4	日			测量-调试
    //    0x10F8	4	时			测量-调试
    //    0x10FA	4	分			测量-调试
    //    0x10FC	4	秒			测量-调试
    //    0x10FE	4	DCAC_调试变量1			测量-调试
    //    0x1100	4	DCAC_调试变量2			测量-调试
    //    0x1102	4	DCAC_调试变量3			测量-调试
    //    0x1104	4	DCAC_调试变量4			测量-调试
    //    0x1106	4	DCAC_调试变量5			测量-调试
    //    0x1108	4	DCAC_调试变量6			测量-调试

    //    0x1220	4	DCDC_温度1	℃		测量-调试
    //    0x1222	4	DCDC_温度2	℃		测量-调试
    //    0x1224	4	DCDC_温度3	℃		测量-调试
    //    0x1226	4	DCDC_温度4	℃		测量-调试
    //    0x1228	4	DCDC_温度5	℃		测量-调试
    //    0x122A	4	DCDC_预留一路温度显示	先不显示 ℃		测量-调试
    //    0x122C	4	DCDC_预留一路温度显示	先不显示 ℃		测量-调试
    //    0x122E	4	DCDC_预留一路温度显示	先不显示 ℃		测量-调试
    //    0x1230	4	正直流母线电压	V		测量-调试
    //    0x1232	4	负直流母线电压	V		测量-调试
    //    0x1234	4	平衡电路电流	A		测量-调试
    //    0x1236	4	风扇档位			测量-调试
}
