package com.cdz360.base.model.base.constants;

/**
 * 基础常量
 */
public interface DcConstants {


    int KEY_RES_CODE_SUCCESS = 0;   // RPC调用成功的响应code
    int KEY_RES_CODE_TOKEN_ERROR = 1000;    // token类失败的响应code
    int KEY_RES_CODE_AUTH_ERROR = 1010;      // 鉴权失败
    int KEY_RES_CODE_SERVICE_ERROR = 2000;  // 通用服务失败的响应code,如业务逻辑错误
    int KEY_RES_CODE_ACCOUNT_ERROR = 2100;  // 通用的账号异常
    int KEY_RES_CODE_BALANCE_ERROR = 2101;  // 余额异常(不足)
    int KEY_RES_CODE_AUTH_FAIL = 2102;  // 鉴权失败(没有充电权限)
    int KEY_RES_CODE_AUTH_FAIL_RESERVED = 2103; // 鉴权失败-枪头有其他预约
    int KEY_RES_CODE_AUTH_FAIL_SITE_STATUS_ABNORMAL = 2104; // 鉴权失败-场站状态异常(维护中)
    int KEY_RES_CODE_AUTH_FAIL_EVSE_UNBOUND = 2105; // 鉴权失败-桩未绑定到场站
    int KEY_RES_CODE_AUTH_FAIL_EVSE_NO_TEMPLATE = 2106; // 鉴权失败-桩未下发计费模板
    int KEY_RES_CODE_AUTH_FAIL_SITE_POWER_LIMIT_REACHED = 2107; // 鉴权失败-场站功率达到上限
    int KEY_RES_CODE_ARGUMENT_ERROR = 3000; // 参数错误的响应code
    int KEY_RES_CODE_NEED_MORE_DATA = 3001; // 等待更多消息的相应code,主要用于网络层做组包等待
    int KEY_RES_CODE_DUPLICATE = 3100;  // 重复请求
    int KEY_RES_CODE_SERVER_ERROR = 5000;   // 服务器异常的响应code,如服务器无响应,数据库访问异常
    int KEY_RES_CODE_NOT_IMPLEMENT = 9998;  // 功能还未实现
    int KEY_RES_CODE_UNKNOWN_ERROR = 9999;  // 未知错误的响应code


    /**
     * 生产环境标识
     */
    String KEY_ENV_PRODUCT = "product";


    /**
     * 场站,设备管理
     */
    String KEY_FEIGN_IOT_DEVICE_MGM = "iot-device-mgm";

    /**
     * 场站,设备管理
     */
    String KEY_FEIGN_IOT_WORKER = "iot-worker";

    /**
     * 场站监控
     */
    String KEY_FEIGN_IOT_CAMERA = "iot-camera";

    /**
     * 电表服务
     */
    String KEY_FEIGN_IOT_METER = "iot-meter";

    /**
     * 停车场道闸服务
     */
    String KEY_FEIGN_IOT_PARK = "iot-park";

    /**
     * 光伏服务
     */
    String KEY_FEIGN_IOT_PV = "iot-pv";

    /**
     * 储能服务
     */
    String KEY_FEIGN_IOT_ESS = "iot-ess";

    /**
     * 设备监控服务
     */
    String KEY_FEIGN_IOT_MONITOR = "iot-monitor";

    /**
     * 鉴权中心
     */
    String KEY_FEIGN_DC_BIZ_AUTH = "dc-biz-auth";


    /**
     * 商户端接口
     */
    String KEY_FEIGN_DC_BIZ_ANT = "dc-biz-ant";

    /**
     * C端API接口
     */
    String KEY_FEIGN_DC_BIZ_APPLET = "dc-biz-applet";

    /**
     * 数据中心
     */
    String KEY_FEIGN_DC_BIZ_DATA_CORE = "dc-biz-data-core";

    /**
     * 场站信息管理
     */
    String KEY_FEIGN_DC_BIZ_DEVICE_REST = "dc-biz-device-rest";

    /**
     * fuul 网关
     */
    String KEY_FEIGN_DC_BIZ_GATEWAY = "dc-biz-gateway";

    /**
     * 定时任务
     */
    String KEY_FEIGN_DC_BIZ_JOB = "dc-biz-job";

    /**
     * 消息中心服务
     */
    String KEY_FEIGN_DC_BIZ_MESSAGE = "dc-biz-message";

    /**
     * 报表接口
     */
    String KEY_FEIGN_DC_BIZ_BI = "dc-biz-bi";

    /**
     * 订单中心
     */
    String KEY_FEIGN_DC_BIZ_TRADING = "dc-biz-trading";

    /**
     * 订单中心 （历史数据)
     */
    String KEY_FEIGN_DC_BIZ_TRADING_HISTORY = "dc-biz-trading-his";

    String KEY_FEIGN_DC_BIZ_HLHT = "dc-biz-hlht";

    String KEY_FEIGN_DC_BIZ_INVOICE = "dc-biz-invoice";

    /**
     * 用户中心
     */
    String KEY_FEIGN_DC_BIZ_USER = "dc-biz-user";

    /**
     * OA流程相关微服务
     */
    String KEY_FEIGN_DC_BIZ_OA = "dc-biz-oa";

    /**
     * 充电站投建相关微服务
     */
    String KEY_FEIGN_DC_BIZ_TJ = "dc-biz-tj";

    /**
     * 账户中心
     */
    String KEY_FEIGN_DC_BIZ_CUS_BALANCE = "dc-biz-cus-balance";

    String KEY_FEIGN_DC_BIZ_PCP = "dc-biz-pcp";

    /**
     * 场站信息管理
     */
//    String KEY_FEIGN_DEVICE_BUSINESS_REST = "device-business-rest";


    /**
     * 设备管理服务
     */
//    String KEY_FEIGN_DEVICE_MANAGER_REST = "device-manager-rest";

//    String KEY_FEIGN_DEVICE_MONITOR_REST = "device-monitor-rest";

    String KEY_FEIGN_OPEN_HLHT = "open-hlht";

    /**
     * 外部运营商SIM服务
     */
    String KEY_FEIGN_OPEN_SIM = "open-sim";

    /**
     * 正向互联互通通用引流服务
     */
    String KEY_FEIGN_OPEN_HLHT_PUSH = "open-hlht-push";

    /**
     * 正向互联互通江苏省平台服务
     */
    String KEY_FEIGN_OPEN_HLHT_PUSH_JIANGSU = "open-hlht-push-jiangsu";

    /**
     * 正向互联互通特殊引流服务
     */
    String KEY_FEIGN_OPEN_HLHT_PUSH_SPECIAL = "open-hlht-push-special";

    /**
     * 正向互联互通监管平台服务
     */
    String KEY_FEIGN_OPEN_HLHT_PUSH_MONITOR = "open-hlht-push-monitor";

    /**
     * 正向互联互通查询服务
     */
    String KEY_FEIGN_OPEN_HLHT_REST = "open-hlht-rest";

    /**
     * 通用(默认)日期时间格式
     */
    String DC_DEFAULT_DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
}
