package com.cdz360.base.model.corp.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
@Schema(description = "企业用户信息同步")
public class CorpUserSyncDto {

    @Schema(description = "企业用户主键ID", example = "123")
    private Long id;

    @Schema(description = "企业ID", example = "123")
    private Long corpId;

    @Schema(description = "用户UID", example = "123")
    private Long uid;

    @Schema(description = "集团商户ID", example = "123")
    private Long topCommId;

    @Schema(description = "企业所属商户ID", example = "123")
    private Long commId;

    @Schema(description = "企业组织id", example = "123")
    private Long corpOrgId;

    @Schema(description = "姓名", example = "张三")
    private String name;


    @Schema(description = "手机号", example = "13012345678")
    private String phone;

    @Schema(description = "创建时间")
    private Date createTime;


    @Schema(description = "最后更新时间")
    private Date updateTime;


    @Schema(description = "是否有效", example = "true")
    private Boolean enable;
}
