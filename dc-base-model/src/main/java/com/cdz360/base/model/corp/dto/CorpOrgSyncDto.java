package com.cdz360.base.model.corp.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
@Schema(description = "企业组织信息同步")
public class CorpOrgSyncDto {
    @Schema(description = "主键 全局唯一")
    private Long id;
    @Schema(description = "企业id")
    private Long corpId;
    @Schema(description = "组织名称")
    private String orgName;
    @Schema(description = "组织级别")
    private Integer orgLevel;
    @Schema(description = "一级组织id")
    private Long l1Id;
    @Schema(description = "二级组织id")
    private Long l2Id;
    private Boolean enable;
    private String account;
    private String password;
    private Date createTime;
    private Date updateTime;
}