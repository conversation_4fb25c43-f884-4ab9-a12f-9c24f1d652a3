package com.cdz360.base.model.es.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;


@Data
@Accessors(chain = true)
@Schema(title = "电池堆数据", description = "")
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@JsonInclude(Include.NON_NULL)
public class BmsStackRtData extends EssBaseRtData {


    @Schema(title = "BMS设备序列号")
    @JsonInclude(Include.NON_EMPTY)
    private String bmsDno;


    @JsonProperty(value = "v")
    @Schema(title = "堆总压", description = "单位v")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal voltage;

    @JsonProperty(value = "i")
    @JsonInclude(Include.NON_NULL)
    @Schema(title = "堆总电流", description = "单位A")
    private BigDecimal current;

    @Schema(title = "堆SOC", description = "")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal soc;

    @Schema(title = "堆SOH", description = "")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal soh;


    @JsonProperty(value = "ipr")
    @Schema(title = "堆绝缘正电阻", description = "单位KΩ")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal isolatePositiveResistance;

    @JsonProperty(value = "inr")
    @Schema(title = "堆绝缘负电阻", description = "单位KΩ")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal isolateNegativeResistance;

    @Schema(title = "最高单体电压", description = "")
    @JsonInclude(Include.NON_NULL)
    private BatterySummary maxBatteryVoltage;

    @Schema(title = "最低单体电压", description = "")
    @JsonInclude(Include.NON_NULL)
    private BatterySummary minBatteryVoltage;


    @Schema(title = "最高单体温度", description = "")
    @JsonInclude(Include.NON_NULL)
    private BatterySummary maxBatteryTemp;


    @Schema(title = "最低单体温度", description = "")
    @JsonInclude(Include.NON_NULL)
    private BatterySummary minBatteryTemp;


    @Schema(title = "最大单体压差", description = "")
    @JsonInclude(Include.NON_NULL)
    private BundleSummary maxBatteryVoltageDiff;


    @Schema(title = "最大温度差值", description = "")
    @JsonInclude(Include.NON_NULL)
    private BundleSummary maxBatteryTempDiff;


    @Schema(title = "最高簇总压", description = "")
    @JsonInclude(Include.NON_NULL)
    private BundleSummary maxVoltageBundle;


    @Schema(title = "最低簇总压", description = "")
    @JsonInclude(Include.NON_NULL)
    private BundleSummary minVoltageBundle;

    @Schema(title = "最大总压差", description = "")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal maxVoltageDiff;

    @Schema(title = "最大簇SOC", description = "")
    @JsonInclude(Include.NON_NULL)
    private BundleSummary maxSocBundle;

    @Schema(title = "最小簇SOC", description = "")
    @JsonInclude(Include.NON_NULL)
    private BundleSummary minSocBundle;

    @Schema(title = "充电最大限制电流", description = "")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal chargeLimitCurrent;

    @Schema(title = "放电最大限制电流", description = "")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal dischargeLimitCurrent;

    @Schema(title = "充放电次数")
    @JsonInclude(Include.NON_NULL)
    private Integer chargeNum;

    @Schema(title = "堆当日充电电量", description = "")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal inKwh;

    @Schema(title = "堆当日放电电量", description = "")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal outKwh;

    @Schema(title = "堆可充电量", description = "")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal expectInKwh;

    @Schema(title = "堆可放电量", description = "")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal expectOutKwh;

    @Schema(title = "堆累计充电电量", description = "")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal totalInKwh;

    @Schema(title = "堆累计放电电量", description = "")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal totalOutKwh;

//    @Schema(title = "各种信号量", description = "信号量")
//    private List<SignalVal> signals;


    @Schema(title = "电池蔟数据", description = "")
    @JsonInclude(Include.NON_NULL)
    private List<BmsBundleRtData> bundleDataList;

    /**
     * 单体电池最高/低 电压、温度
     */
    @Data
    @Accessors(chain = true)
    public static class BatterySummary {

        @Schema(title = "簇号", description = "")
        @JsonInclude(Include.NON_NULL)
        private Integer bundleId;

        @Schema(title = "BMU号", description = "")
        @JsonInclude(Include.NON_NULL)
        private Integer bmuId;

        @Schema(title = "LMU号", description = "")
        @JsonInclude(Include.NON_NULL)
        private Integer lmuId;

        @Schema(title = "箱内序号", description = "")
        @JsonInclude(Include.NON_NULL)
        private Integer inboxId;

        @Schema(title = "值", description = "")
        @JsonInclude(Include.NON_NULL)
        private BigDecimal val;
    }

    /**
     * 蔟 最高/低 总压、soc
     */
    @Data
    @Accessors(chain = true)
    public static class BundleSummary {

        @Schema(title = "簇号", description = "")
        @JsonInclude(Include.NON_NULL)
        private Integer bundleId;

        @Schema(title = "值", description = "")
        @JsonInclude(Include.NON_NULL)
        private BigDecimal val;
    }
}
