package com.cdz360.base.model.iot.vo;

import com.cdz360.base.model.base.annotation.Cache;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@Accessors(chain = true)
public class TransformerVo implements Serializable {

    private static final long serialVersionUID = 6115087052757842456L;


    @Cache
    @Schema(description = "ID")
    private Long id;


    @Cache
    @Schema(description = "场站id", example= "ABCD123")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteId;

    @Cache
    @Schema(description = "枪头总数", example= "123")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer plugNum;

    @Cache
    @Schema(description = "实际(已确认)功率", example= "123")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer actualPower;

    @Cache
    @Schema(description = "分配功率", example= "123")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer assignPower;

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    @Cache
    @Schema(description = "最后更新时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date updateTime;
}
