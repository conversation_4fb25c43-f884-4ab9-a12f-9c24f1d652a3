package com.cdz360.base.model.es.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum EmuKvCode implements DcEnum {
    DI_WATER(90101, "水浸报警"),
    DI_FIRE_ALARM(90110, "消防报警"),
    DI_FIRE_WARN(90111, "消防告警"),
    DI_SPD(90115, "浪涌保护器报警"),
    DI_EMERGENCY_STOP(90120, "急停故障"),
    DI_DOOR_OPEN(90125, "行程开关报警");

    @JsonValue
    final int code;

    final String desc;

    EmuKvCode(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static EmuKvCode valueOf(Object codeIn) {
        if (codeIn == null) {
            return null;
        }
        int code = 0;
        if (codeIn instanceof EmuKvCode) {
            return (EmuKvCode) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (EmuKvCode type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }
}
