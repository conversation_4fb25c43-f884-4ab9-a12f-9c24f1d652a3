package com.cdz360.base.model.bi.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class EvseBi implements Serializable {
    private static final long serialVersionUID = 1391581584457137361L;
    @Schema(description = "总数")
    private Long total;

    @Schema(description = "空闲")
    private Long idle;

    @Schema(description = "使用中(含充电前/后空占)")
    private Long busy;

    @Schema(description = "离线")
    private Long offline;
}
