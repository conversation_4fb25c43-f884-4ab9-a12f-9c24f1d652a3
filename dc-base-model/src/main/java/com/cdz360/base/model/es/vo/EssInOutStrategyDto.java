package com.cdz360.base.model.es.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(title = "充放电时段配置")
public class EssInOutStrategyDto {


    @Schema(title = "ID", description = "配置的唯一ID,策略修改后ID会变")
    private Long id;


    @Schema(title = "策略编号", description = "策略修改后编号不变,用于追踪策略的修改")
    private String sno;


    @Schema(title = "策略名称", description = "策略名称")
    private String name;

    private List<EssInOutStrategyItem> items = new ArrayList<>();


}
