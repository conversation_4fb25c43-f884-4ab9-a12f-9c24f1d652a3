package com.cdz360.base.model.site.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum WeatherType implements DcEnum {

    UNKNOWN(99, "未知", "https://fourth-platform.oss-cn-shanghai.aliyuncs.com/images/xyz/weather/99.png"),
    SUNNY(0, "晴", "https://fourth-platform.oss-cn-shanghai.aliyuncs.com/images/xyz/weather/0.png"),
    CLOUDY(1, "多云", "https://fourth-platform.oss-cn-shanghai.aliyuncs.com/images/xyz/weather/1.png"),
    OVERCAST(2, "阴", "https://fourth-platform.oss-cn-shanghai.aliyuncs.com/images/xyz/weather/2.png"),
    RAIN_SHOWER(3, "阵雨", "https://fourth-platform.oss-cn-shanghai.aliyuncs.com/images/xyz/weather/3.png"),
    THUNDER_SHOWER(4, "雷阵雨", "https://fourth-platform.oss-cn-shanghai.aliyuncs.com/images/xyz/weather/4.png"),
    THUNDER_SHOWER_HAIL(5, "雷阵雨伴有冰雹", "https://fourth-platform.oss-cn-shanghai.aliyuncs.com/images/xyz/weather/5.png"),
    RAIN_SNOW(6, "雨夹雪", "https://fourth-platform.oss-cn-shanghai.aliyuncs.com/images/xyz/weather/6.png"),
    RAIN_SMALL(7, "小雨", "https://fourth-platform.oss-cn-shanghai.aliyuncs.com/images/xyz/weather/7.png"),
    RAIN_NORMAL(8, "中雨", "https://fourth-platform.oss-cn-shanghai.aliyuncs.com/images/xyz/weather/8.png"),
    RAIN_BIG(9, "大雨", "https://fourth-platform.oss-cn-shanghai.aliyuncs.com/images/xyz/weather/9.png"),
    RAIN_HUGE(10, "暴雨", "https://fourth-platform.oss-cn-shanghai.aliyuncs.com/images/xyz/weather/10.png"),
    RAIN_BIG_HUGE(11, "大暴雨", "https://fourth-platform.oss-cn-shanghai.aliyuncs.com/images/xyz/weather/11.png"),
    RAIN_SUPER_HUGE(12, "特大暴雨", "https://fourth-platform.oss-cn-shanghai.aliyuncs.com/images/xyz/weather/12.png"),
    SNOW_SHOWER(13, "阵雪", "https://fourth-platform.oss-cn-shanghai.aliyuncs.com/images/xyz/weather/13.png"),
    SNOW_SMALL(14, "小雪", "https://fourth-platform.oss-cn-shanghai.aliyuncs.com/images/xyz/weather/14.png"),
    SNOW_NORMAL(15, "中雪", "https://fourth-platform.oss-cn-shanghai.aliyuncs.com/images/xyz/weather/15.png"),
    SNOW_BIG(16, "大雪", "https://fourth-platform.oss-cn-shanghai.aliyuncs.com/images/xyz/weather/16.png"),
    SNOW_HUGE(17, "暴雪", "https://fourth-platform.oss-cn-shanghai.aliyuncs.com/images/xyz/weather/17.png"),
    FOG(18, "雾", "https://fourth-platform.oss-cn-shanghai.aliyuncs.com/images/xyz/weather/18.png"),
    FROZEN_RAIN(19, "冻雨", "https://fourth-platform.oss-cn-shanghai.aliyuncs.com/images/xyz/weather/19.png"),
    SANDSTORM(20, "沙尘暴", "https://fourth-platform.oss-cn-shanghai.aliyuncs.com/images/xyz/weather/20.png"),
    RAIN_SMALL_2_NORMAL(21, "小雨-中雨", "https://fourth-platform.oss-cn-shanghai.aliyuncs.com/images/xyz/weather/21.png"),
    RAIN_NORMAL_2_BIG(22, "中雨-大雨", "https://fourth-platform.oss-cn-shanghai.aliyuncs.com/images/xyz/weather/22.png"),
    RAIN_BIG_2_HUGE(23, "大雨-暴雨", "https://fourth-platform.oss-cn-shanghai.aliyuncs.com/images/xyz/weather/23.png"),
    RAIN_HUGE_2_BIG_HUGE(24, "暴雨-大暴雨", "https://fourth-platform.oss-cn-shanghai.aliyuncs.com/images/xyz/weather/24.png"),
    RAIN_BIG_HUGE_2_SUPER_HUGE(25, "大暴雨-特大暴雨", "https://fourth-platform.oss-cn-shanghai.aliyuncs.com/images/xyz/weather/25.png"),
    SNOW_SMALL_2_NORMAL(26, "小雪-中雪", "https://fourth-platform.oss-cn-shanghai.aliyuncs.com/images/xyz/weather/26.png"),
    SNOW_NORMAL_2_BIG(27, "中雪-大雪", "https://fourth-platform.oss-cn-shanghai.aliyuncs.com/images/xyz/weather/27.png"),
    SNOW_BIG_2_HUGE(28, "大雪-暴雪", "https://fourth-platform.oss-cn-shanghai.aliyuncs.com/images/xyz/weather/28.png"),
    ASH_SMALL(29, "浮尘", "https://fourth-platform.oss-cn-shanghai.aliyuncs.com/images/xyz/weather/29.png"),
    ASH_NORMAL(30, "扬沙", "https://fourth-platform.oss-cn-shanghai.aliyuncs.com/images/xyz/weather/30.png"),
    ASH_BIG(31, "强沙尘暴", "https://fourth-platform.oss-cn-shanghai.aliyuncs.com/images/xyz/weather/31.png"),
    FOG_HUGE(32, "浓雾", "https://fourth-platform.oss-cn-shanghai.aliyuncs.com/images/xyz/weather/32.png"),
    FOG_BIG_HUGE(49, "强浓雾", "https://fourth-platform.oss-cn-shanghai.aliyuncs.com/images/xyz/weather/49.png"),
    HAZE(53, "霾", "https://fourth-platform.oss-cn-shanghai.aliyuncs.com/images/xyz/weather/53.png"),
    HAZE_NORMAL(54, "中毒霾", "https://fourth-platform.oss-cn-shanghai.aliyuncs.com/images/xyz/weather/54.png"),
    HAZE_BIG(55, "重度霾", "https://fourth-platform.oss-cn-shanghai.aliyuncs.com/images/xyz/weather/55.png"),
    HAZE_HUGE(56, "严重霾", "https://fourth-platform.oss-cn-shanghai.aliyuncs.com/images/xyz/weather/56.png"),
    FOG_BIG(57, "大雾", "https://fourth-platform.oss-cn-shanghai.aliyuncs.com/images/xyz/weather/57.png"),
    FOG_SUPER_HUGE(58, "特强浓雾", "https://fourth-platform.oss-cn-shanghai.aliyuncs.com/images/xyz/weather/58.png"),
    RAIN_2(301, "雨", "https://fourth-platform.oss-cn-shanghai.aliyuncs.com/images/xyz/weather/301.png"),
    SNOW_2(302, "雪", "https://fourth-platform.oss-cn-shanghai.aliyuncs.com/images/xyz/weather/302.png"),
    ;

    @JsonValue
    private final int code;
    private final String desc;
    private final String icon;

    WeatherType(int code, String desc, String icon) {
        this.code = code;
        this.desc = desc;
        this.icon = icon;
    }

    @JsonCreator
    public static WeatherType valueOf(Object codeIn) {
        if (codeIn == null) {
            return WeatherType.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof WeatherType) {
            return (WeatherType) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Byte) {
            code = ((Byte) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (WeatherType status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return WeatherType.UNKNOWN;
    }

}
