package com.cdz360.base.model.es.vo;

import com.cdz360.base.model.meter.vo.DcRtData;
import com.cdz360.base.model.meter.vo.MeterAbcData;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * PCS实际设备实时数据
 */
@Data
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Schema(title = "PCS实时数据")
@JsonInclude(Include.NON_NULL)
public class PcsRtData extends EssBaseRtData {

//    /**
//     * 废弃，移到 DcRtData
//     */
//    @Deprecated
//    @Schema(title = "母线电压", description = "母线电压")
//    @JsonInclude(Include.NON_NULL)
//    private BigDecimal busVoltage;
//
//    /**
//     * 废弃，移到 DcRtData
//     */
//    @Deprecated
//    @Schema(description = "正母线电压")
//    @JsonInclude(Include.NON_NULL)
//    private BigDecimal positiveBusVoltage;
//
//    /**
//     * 废弃，移到 DcRtData
//     */
//    @Deprecated
//    @Schema(description = "负母线电压")
//    @JsonInclude(Include.NON_NULL)
//    private BigDecimal negativeBusVoltage;

    @Schema(description = "电网侧电流/电压数据")
    @JsonInclude(Include.NON_NULL)
    private GridData gridData;

    @Schema(description = "平衡电流有效值")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal equilibriumCurrent;

    @Schema(title = "交流侧数据", description = "逆变器交流侧电流/电压/功率数据")
    @JsonInclude(Include.NON_NULL)
    private MeterAbcData acData;


    @Schema(title = "直流侧数据", description = "逆变器直流侧数据")
    @JsonInclude(Include.NON_NULL)
    private DcRtData dcData;

    @Schema(title = "充放电数据", description = "充放电数据")
    @JsonInclude(Include.NON_NULL)
    private EssChargeTinySummary chargeData;

//    /**
//     * @deprecated  废弃，使用 sensorValues 替换
//     */
//    @Deprecated
//    @Schema(title = "PCS温度数据", description = "PCS温度数据")
//    @JsonInclude(Include.NON_NULL)
//    private PcsTemperatureData tempData;
//
//    /**
//     * @deprecated  废弃，使用 sensorValues 替换
//     */
//    @Deprecated
//    @Schema(title = "模块温度")
//    @JsonInclude(Include.NON_NULL)
//    private BigDecimal supplyTemp;
//
//
//    /**
//     * @deprecated  废弃，使用 sensorValues 替换
//     */
//    @Deprecated
//    @Schema(title = "散热器温度")
//    @JsonInclude(Include.NON_NULL)
//    private BigDecimal fanTemp;
//
//
//    /**
//     * @deprecated  废弃，使用 sensorValues 替换
//     */
//    @Deprecated
//    @Schema(title = "内部环境温度")
//    @JsonInclude(Include.NON_NULL)
//    private BigDecimal pcsTemp;
//
//    /**
//     * @deprecated  废弃，使用 sensorValues 替换
//     */
//    @Deprecated
//    @Schema(title = "运行时间", description = "单位秒")
//    @JsonInclude(Include.NON_NULL)
//    private Long runningDur;

//    @Schema(title = "传感器数据", description = "各种电流、电压数据")
//    private List<SensorVal> sensorValues;
}
