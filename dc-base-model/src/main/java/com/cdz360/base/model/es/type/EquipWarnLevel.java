package com.cdz360.base.model.es.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 设备故障/告警等级
 */
@Getter
public enum EquipWarnLevel implements DcEnum {
    EMERGENCY(7, "紧急", "紧急"),

    CRITICAL(6, "严重", "严重"),

    FAULT(5, "故障", "故障"),

    ERROR(4, "错误", "错误"),

    WARNING(3, "告警", "告警"),

    NOTIFICATION(2, "提醒", "提醒"),

    INFORMATION(1, "信息", "信息"),

    UNKNOWN(0, "未知", "");

    @JsonValue
    final int code;

    final String msg;

    final String desc;


    EquipWarnLevel(int code, String msg, String desc) {
        this.code = code;
        this.msg = msg;
        this.desc = desc;
    }


    @JsonCreator
    public static EquipWarnLevel valueOf(Object codeIn) {
        if (codeIn == null) {
            return EquipWarnLevel.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof EquipWarnLevel) {
            return (EquipWarnLevel) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (EquipWarnLevel type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return EquipWarnLevel.UNKNOWN;
    }

}
