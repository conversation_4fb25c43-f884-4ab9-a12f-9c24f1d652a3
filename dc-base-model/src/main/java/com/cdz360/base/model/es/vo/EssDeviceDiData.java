package com.cdz360.base.model.es.vo;

import com.cdz360.base.model.es.EmuDiInfo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "储能设备实时数据")
@Data
@Accessors(chain = true)
public class EssDeviceDiData {

    @Schema(description = "储能ESS唯一编号")
    @JsonInclude(Include.NON_EMPTY)
    private String dno;

    @Deprecated(since = "2024-03-19")
    @Schema(description = "DI收集数据", externalDocs = @ExternalDocumentation(
        description = "1-水浸报警\n"
            + "2-消防报警\n"
            + "3-浪涌保护器报警\n"
            + "4-急停故障\n"
            + "5-行程开关报警"
    ))
    private Map<Integer, Object> diDataMap;

    @Schema(description = "DI数据信息")
    @JsonInclude(Include.NON_NULL)
    private List<EmuDiInfo> diDataList;


}
