package com.cdz360.base.model.es.vo;

import com.cdz360.base.model.es.type.UpsStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class UpsRtData extends EssBaseRtData {


    @Schema(title = "输入电压", description = "单位 v")
    @JsonInclude(Include.NON_NULL)
    @JsonProperty("iv")
    private BigDecimal inVoltage;

    @Schema(title = "输出电压", description = "单位 v")
    @JsonInclude(Include.NON_NULL)
    @JsonProperty("ov")
    private BigDecimal outVoltage;

    @Schema(title = "输出负载", description = "单位 %")
    @JsonInclude(Include.NON_NULL)
    @JsonProperty("ol")
    private BigDecimal outLoad;

    @Schema(title = "输入频率", description = "单位 Hz")
    @JsonInclude(Include.NON_NULL)
    @JsonProperty("if")
    private BigDecimal inFrequency;

    @Schema(title = "输出频率", description = "单位 Hz")
    @JsonInclude(Include.NON_NULL)
    @JsonProperty("of")
    private BigDecimal outFrequency;

    @Schema(title = "输出电流", description = "单位 Hz")
    @JsonInclude(Include.NON_NULL)
    @JsonProperty("oc")
    private BigDecimal outCurrent;

    @Schema(title = "正总线电压", description = "单位 V")
    @JsonInclude(Include.NON_NULL)
    @JsonProperty("pbsv")
    private BigDecimal positiveBusVoltage;

    @Schema(title = "负总线电压", description = "单位 V")
    @JsonInclude(Include.NON_NULL)
    @JsonProperty("nbsv")
    private BigDecimal negativeBusVoltage;

    @Schema(title = "正电池电压", description = "单位 V")
    @JsonInclude(Include.NON_NULL)
    @JsonProperty("pbtv")
    private BigDecimal positiveBatteryVoltage;

    @Schema(title = "负电池电压", description = "单位 V")
    @JsonInclude(Include.NON_NULL)
    @JsonProperty("nbtv")
    private BigDecimal negativeBatteryVoltage;

    @Schema(title = "探测点最高温度", description = "单位 ℃")
    @JsonInclude(Include.NON_NULL)
    @JsonProperty("mtodp")
    private BigDecimal maxTempOfDetectingPointers;

    private List<UpsStatus> upsStatusList;


}
