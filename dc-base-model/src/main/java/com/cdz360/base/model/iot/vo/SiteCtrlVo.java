package com.cdz360.base.model.iot.vo;

import com.cdz360.base.model.base.type.SiteCtrlStatusType;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 场站控制器信息
 */
@Data
@Accessors(chain = true)
public class SiteCtrlVo implements Serializable {
    private static final long serialVersionUID = -725258375638683680L;

    private String ctrlNo;

    private SiteCtrlStatusType status;

    /**
     * 控制器负载率
     */
    private Integer loadRatio;

    private Integer pwrTemp;

    /**
     * 告警码
     * 0x00：正常
     * 0x01：负载告警
     * 0x02：配电柜温度告警
     * 0x04：充电桩烟雾告警
     * 0x08：充电桩门禁告警
     * 发生多个告警时将告警码或运算后发送
     */
    private Integer alertCode;

    /**
     * 故障码
     * 0x00：正常
     * 0x01：控制器配置信息异常
     * 0x02：负载异常
     * 0x04：配电柜温度异常
     * 0x08：充电桩功率输出异常
     * 0x10：充电桩离线异常
     * 发生多个异常时将故障码或运算后发送
     */
    private Integer errorCode;

    private String info;

    /**
     * 将相关的控制器上报告警记录连接起来
     * 使用UUID生成
     * 留作备用
     */
    private String linkId;

    private Long siteCommId;
    private String siteId;
    private String siteName;

    private String name;
}