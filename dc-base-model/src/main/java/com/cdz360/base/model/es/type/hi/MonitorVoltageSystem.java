package com.cdz360.base.model.es.type.hi;

import com.cdz360.base.model.base.type.DcEnum;
import com.cdz360.base.model.charge.type.OrderStartType;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Getter
@Schema(description = "电压制式")
public enum MonitorVoltageSystem implements DcEnum {

    UNKNOWN(0, "未知"),
    THREE_PHASE_FOUR_WIRE(1, "三相四线制"),
    THREE_PHASE_THREE_WIRE(2, "三相三线制"),
    ;

    @JsonValue
    private final int code;
    private final String desc;

    MonitorVoltageSystem(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static MonitorVoltageSystem valueOf(Object codeIn) {
        if (codeIn == null) {
            return MonitorVoltageSystem.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof OrderStartType) {
            return (MonitorVoltageSystem) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (MonitorVoltageSystem status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return MonitorVoltageSystem.UNKNOWN;
    }

}