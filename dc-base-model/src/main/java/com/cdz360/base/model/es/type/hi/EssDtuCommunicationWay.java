package com.cdz360.base.model.es.type.hi;

import com.cdz360.base.model.base.type.DcEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Schema(description = "ESS数据传输单元通讯方式")
@Getter
public enum EssDtuCommunicationWay implements DcEnum {
    UNKNOWN(0x00, "未知"),
    ETHERNET_COMMUNICATION(0x01, "Ethernet Communication"),
    WIFI_COMMUNICATION(0x02, "WiFi Communication"),
    GPRS_COMMUNICATION(0x03, "GPRS Communication"),
    SUB_1G(0x04, "Sub 1G"),
    ZIGBEE(0x05, "ZigBee"),
    BLUETOOTH(0x06, "Bluetooth"),
    CAN(0x07, "CAN"),
    RS485(0x08, "RS485");

    private final int code;
    private final String desc;

    EssDtuCommunicationWay(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static EssDtuCommunicationWay valueOf(Object codeIn) {
        if (codeIn == null) {
            return EssDtuCommunicationWay.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof EssDtuCommunicationWay) {
            return (EssDtuCommunicationWay) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (EssDtuCommunicationWay type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return EssDtuCommunicationWay.UNKNOWN;
    }

}
