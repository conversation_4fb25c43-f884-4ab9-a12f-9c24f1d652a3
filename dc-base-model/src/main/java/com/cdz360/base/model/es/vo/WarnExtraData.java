package com.cdz360.base.model.es.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "告警附加信息")
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WarnExtraData {

    @Schema(description = "设备挂在网关的编号")
    private String gwno;

    @Schema(description = "设备挂在网关的名称")
    private String gwName;

    @Schema(description = "设备关联用户所在国家地区代码(Alpha-3 code)",
        externalDocs = @ExternalDocumentation(
            description = "iso-3166 Country Codes",
            url = "https://www.iso.org/obp/ui/#search"), hidden = true)
    private String countryCode;

    @Schema(description = "所属场站ID")
    private String siteId;

    @Schema(description = "所属场站名称")
    private String siteName;

    @Schema(description = "告警等级")
    private Long level;
}
