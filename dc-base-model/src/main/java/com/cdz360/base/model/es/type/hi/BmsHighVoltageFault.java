package com.cdz360.base.model.es.type.hi;

import com.cdz360.base.model.base.type.DcEnum;
import com.cdz360.base.model.charge.type.OrderStartType;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * BMS高压故障
 */
@Getter
public enum BmsHighVoltageFault implements DcEnum {

    UNKNOWN(0, "未知"),
    VOLTAGE_SENSOR_FAULT(1, "电压传感器故障"),
    TEMP_SENSOR_FAULT(2, "温度传感器故障"),
    INTERNAL_COMMUNICATION_FAULT(3, "内部通信故障"),
    INPUT_OVERVOLTAGE_FAULTY(4, "输入过压故障"),
    INPUT_REVERSE_FAULTY(5, "输入反接故障"),
    RELAY_DETECTION_FAULT(6, "继电器检测故障"),
    BATTERY_FAILURE(7, "电池损坏故障"),
    OTHER_FAULTS(8, "其他故障"),


    CHARGING_CURRENT_FAULTY(201, "充电电流大故障"),
    DISCHARGING_CURRENT_FAULTY(202, "放电电流大故障"),
    PRIMARY_AND_SECONDARY_COMMUNICATION_FAULT(207, "主从板通讯故障"),
    MAIN_COMMUNICATION_FAULT(208, "主通讯故障"),
    HIGH_TEMPERATURE_FAULT(209, "温度高故障"),
    LOW_TEMPERATURE_FAULT(210, "温度低故障"),
    LARGE_TEMPERATURE_DIFFERENCE(211, "温差大故障"),
    TOTAL_PRESSURE_HIGH_FAULT(212, "总压高故障"),
    TOTAL_PRESSURE_LOW_FAULT(213, "总压低故障"),
    HIGH_CELL_VOLTAGE(214, "单体电压高故障"),
    LOW_CELL_VOLTAGE(215, "单体电压低故障"),
    CELL_PRESSURE_DIFFERENCE_LARGE(216, "单体压差大故障"),
    ;

    @JsonValue
    private final int code;
    private final String desc;

    BmsHighVoltageFault(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static BmsHighVoltageFault valueOf(Object codeIn) {
        if (codeIn == null) {
            return BmsHighVoltageFault.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof OrderStartType) {
            return (BmsHighVoltageFault) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (BmsHighVoltageFault status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return BmsHighVoltageFault.UNKNOWN;
    }

}