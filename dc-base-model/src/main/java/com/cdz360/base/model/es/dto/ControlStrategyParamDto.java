package com.cdz360.base.model.es.dto;

import com.cdz360.base.model.es.vo.EssStrategyCfgDto;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "控制策略参数(有攻防逆流与无功优化)")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ControlStrategyParamDto extends EssStrategyCfgDto {

    @Schema(description = "PCS额定功率")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal pcsPn;

}
