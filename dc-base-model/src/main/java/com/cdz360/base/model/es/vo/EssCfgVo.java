package com.cdz360.base.model.es.vo;

import com.cdz360.base.model.es.dto.UpdateEssCfgDto;
import com.cdz360.base.model.es.type.EquipCfgStatus;
import com.cdz360.base.model.es.type.EssCfgStrategy;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "ESS配置信息")
@Data
@Accessors(chain = true)
public class EssCfgVo {

    @Schema(description = "设备编号", hidden = true)
    @JsonInclude(Include.NON_EMPTY)
    private String dno;

    @Schema(description = "策略类型: SELF_USE(自发自用); PEAK_VALLEY_ARBITRAGE(峰谷套利); "
        + "PEAK_SHARE(削峰填谷); TIMING_CHARGING_DISCHARGING(定时充放电); DISASTER_SPARE(灾备)")
    @JsonInclude(Include.NON_NULL)
    private EssCfgStrategy strategy;

    @Schema(description = "其他时段工作模式")
    private EssCfgStrategy otherStrategy;

    @Schema(description = "重复周期: 当前协议支持周天~周六，使用位来存在对应信息")
    private Integer repeatCycle;

    @Schema(description = "生效开始时间(Unix时间戳)")
    private Long effectiveStartTime;

    @Schema(description = "生效结束时间(Unix时间戳)")
    private Long effectiveEndTime;

    @Schema(description = "最大功率值是否支持时段设置")
    private Boolean supportDivision;

    @Schema(description = "已经生效的配置ID")
    @JsonInclude(Include.NON_NULL)
    private Long cfgId;

    @Schema(description = "下发中的配置ID")
    @JsonInclude(Include.NON_NULL)
    private Long deliverCfgId;

    @Schema(description = "策略类型: SELF_USE(自发自用); PEAK_VALLEY_ARBITRAGE(峰谷套利); "
        + "PEAK_SHARE(削峰填谷); TIMING_CHARGING_DISCHARGING(定时充放电); DISASTER_SPARE(灾备)")
    @JsonInclude(Include.NON_NULL)
    private EssCfgStrategy deliverStrategy;

    @Schema(description = "(下发)其他时段工作模式")
    private EssCfgStrategy deliverOtherStrategy;

    @Schema(description = "(下发)重复周期: 当前协议支持周天~周六，使用位来存在对应信息")
    private Integer deliverRepeatCycle;

    @Schema(description = "(下发)生效开始时间(Unix时间戳)")
    private Long deliverEffectiveStartTime;

    @Schema(description = "(下发)生效结束时间(Unix时间戳)")
    private Long deliverEffectiveEndTime;

    @Schema(description = "配置信息状态: 0,未知;1,已下发;2,下发中;3,下发失败")
    @JsonInclude(Include.NON_NULL)
    private EquipCfgStatus cfgStatus;

    @Schema(description = "已经生效的充放电时段")
    @JsonInclude(Include.NON_NULL)
    private List<EssInOutStrategyItem> inOutItems;

    @Schema(description = "下发中的充放电时段")
    @JsonInclude(Include.NON_NULL)
    private List<EssInOutStrategyItem> deliverInOutItems;

    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(title = "下发时间")
    @JsonInclude(Include.NON_NULL)
    private LocalDateTime deliverCreateTime;

    @Schema(description = "设备生效配置")
    @JsonInclude(Include.NON_NULL)
    private UpdateEssCfgDto activeCfg;
}
