package com.cdz360.base.model.es.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 空调、除湿字段编码
 */
@Getter
public enum DehKvCode implements DcEnum {
    UNKNOWN(140000, "未知"),

    /**
     * 静态信息
     */
    HW_SN(140001, "硬件序号"),
    HW_MODEL(140005, "硬件型号"),
    SW_VER(140010, "软件版本"),

    /**
     * 设备状态信息
     */
    DEH_STATUS(140101, "整机运行状态"),
    FAN_INSIDE_STATUS(140110, "内风机运行状态"),
    FAN_OUTSIDE_STATUS(140115, "外风机运行状态"),
    FAN_EMERGENCY_STATUS(140120, "应急风机运行状态"),
    COMPRESSOR_STATUS(140125, "压缩机运行状态"),
    ELEC_HEATER_STATUS(140130, "电加热运行状态"),
    WATER_PUMP_STATUS(140135, "水泵状态"),

    TEMPERATURE_CTRL_STATUS(140140, "温度控制状态"),
    HUMIDITY_CTRL_STATUS(140141, "湿度控制状态"),
    /**
     * 传感器数据
     */
    TEMPERATURE_DEH(140200, "控制器内部温度"),
    TEMPERATURE_PIPE(140201, "盘管温度"),
    TEMPERATURE_OUTSIDE(140202, "室外温度"),
    TEMPERATURE_INSIDE(140203, "室内温度"),
    TEMPERATURE_FREEZING(140204, "冷凝温度"),
    TEMPERATURE_VENT(140205, "排气温度"),
    TEMPERATURE_RETURN_AIR(140206, "回风温度"),
    TEMPERATURE_EVAPORATOR(140207, "蒸发器温度"),
    TEMPERATURE_BLOWING(140208, "送风温度"),
    FAN_INSIDE_SPEED(140215, "内风机转速"),
    FAN_OUTSIDE_SPEED(140216, "外风机转速"),
    HUMIDITY(140221, "湿度"),
    HUMIDITY_RETURN_AIR(140222, "回风湿度"),
    CURRENT_AC(140231, "交流电流"),
    CURRENT_DC(140232, "直流电流"),
    VOLTAGE_AC(140233, "交流电压"),
    VOLTAGE_DC(140234, "直流电压"),
    DEH_RUNNING_TIME(140250, "整机运行时间"),
    COMPRESSOR_RUNNING_TIME(140251, "压缩机运行时间"),
    FAN_INSIDE_RUNNING_TIME(140252, "内风机运行时间"),

    COMPRESSOR_OP_TIMES(140260, "压缩机动作次数"),

    /**
     * 设定参数
     */
    CFG_SERIAL_RATE(140301, "通讯速率"),
    CFG_MODBUS_ADDR(140302, "通讯地址"),
    CFG_WORK_MODE(140310, "工作模式"),
    CFG_COLLING_TEMPERATURE(140311, "制冷点设定值"),
    CFG_COLLING_BACKLASH(140312, "制冷回差设定值"),
    CFG_HEATING_TEMPERATURE(140315, "加热点设定值"),
    CFG_HEATING_BACKLASH(140316, "加热回差设定值"),
    CFG_TEMPERATURE_HIGH(140317, "高温点设定值"),
    CFG_COOLING_BACKLASH(140318, "降温回差设定值"),
    CFG_TEMPERATURE_LOW(140319, "低温点设定值"),
    CFG_HUMIDITY_MODE(140325, "湿度控制方式"),
    CFG_HUMIDITY_HIGH(140326, "高湿点设定值"),
    CFG_HUMIDITY_BACKLASH(140327, "除湿回差设定值"),
    CFG_DC_VOLTAGE_OVER_LOAD(140341, "直流过压告警"),
    CFG_DC_VOLTAGE_LEAK(14042, "直流欠压告警"),
    CFG_DC_VOLTAGE_DISCONNECT(140343, "直流下电电压"),
    CFG_AC_VOLTAGE_OVER_LOAD(140345, "交流过压告警"),
    CFG_AC_VOLTAGE_LEAK(140346, "交流欠压告警"),
    CFG_STOP_INSIDE_FAN(140351, "内风机停止点设定值"),
    CFG_RESET(140361, "复位"),
    CFG_REMOTE_OPEN_CTRL(140362, "远程开关机"),
    CFG_MONIT_OPEN_MODE(140363, "监控开关机设定"),

    /**
     * 告警、故障
     */
    HIGH_TEMPERATURE_WARN(140401, "高温告警"),
    LOW_TEMPERATURE_WARN(140402, "低温告警"),
    HIGH_HUMIDITY_WARN(140403, "高湿告警"),
    LOW_HUMIDITY_WARN(140404, "低湿告警"),
    PIPE_FROZEN_WARN(140405, "盘管防冻"),
    VENT_HIGH_TEMPERATURE_WARN(140406, "排气高温"),
    EVAPORATOR_FROZEN_WARN(140407, "蒸发器冻结"),
    PIPE_SENSOR_LOST_WARN(140420, "盘管温感失效"),
    FROZEN_SENSOR_LOST_WARN(140421, "冷凝温感失效"),
    OUTSIDE_SENSOR_LOST_WARN(140422, "室外温感失效"),
    INSIDE_SENSOR_LOST_WARN(140423, "内温感失效"),
    VENT_SENSOR_LOST_WARN(140424, "排气温感失效"),
    RETURN_AIR_SENSOR_LOST_WARN(140425, "回风温感失效"),
    HUMIDITY_SENSOR_LOST_WARN(140426, "湿感失效"),
    EVAPORATOR_SENSOR_LOST_WARN(140427, "蒸发器温感失效"),
    INSIDE_FAN_ERROR(140440, "内风机故障"),
    OUTSIDE_FAN_ERROR(140441, "外风机故障"),
    COMPRESSOR_ERROR(140442, "压缩机故障"),
    ELEC_HEATING_ERROR(140443, "电加热故障"),
    EMERGENCY_FAN_ERROR(140444, "应急风机故障"),
    HIGH_VOLTAGE_WARN(140450, "高压告警"),
    LOW_VOLTAGE_WARN(140451, "低压告警"),
    HIGH_VOLTAGE_LOCK(140452, "高压锁定"),
    LOW_VOLTAGE_LOCK(140453, "低压锁定"),
    AC_VOLTAGE_OVER_LOAD(140454, "交流过压"),
    AC_VOLTAGE_LEAK(140455, "交流欠压"),
    AC_LOST(140456, "交流掉电"),
    AC_PHASE_LOST(140457, "缺相"),
    AC_RATE_ERROR(140458, "频率异常"),
    AC_PHASE_REVERSED_ERROR(140459, "逆相"),
    DC_VOLTAGE_OVER_LOAD(140460, "直流过压"),
    DC_VOLTAGE_LEAK(140461, "直流欠压"),
    WATER_WARN(140470, "水浸告警"),
    SMOKE_WARN(140471, "烟感告警"),
    DOOR_OPEN_WARN(140472, "门禁告警"),
    VENT_LOCK(140473, "排气锁定"),
    DEH_FAULT(140480, "严重告警总状态"),
    DEH_WARN(140481, "通用告警状态"),
    SYS_OVER_LOAD(140482, "系统过载"),
    SYS_CONTINUAL_OVER_LOAD(140483, "频繁高压力告警"),
    ;

    @JsonValue
    final int code;


    final String desc;


    DehKvCode(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    @JsonCreator
    public static DehKvCode valueOf(Object codeIn) {
        if (codeIn == null) {
            return DehKvCode.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof DehKvCode) {
            return (DehKvCode) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (DehKvCode type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return DehKvCode.UNKNOWN;
    }
}
