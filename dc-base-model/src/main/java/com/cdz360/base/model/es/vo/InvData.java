package com.cdz360.base.model.es.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "逆变器信息")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class InvData extends ThreePhaseData {

    //    0x1092	4	逆变A相电压	V		测量-逆变
    //    0x1094	4	逆变B相电压	V		测量-逆变
    //    0x1096	4	逆变C相电压	V		测量-逆变
    //    0x1098	4	逆变AB线电压	V		测量-逆变
    //    0x109A	4	逆变BC线电压	V		测量-逆变
    //    0x109C	4	逆变CA线电压	V		测量-逆变
    //    0x109E	4	逆变A相THDU	%		测量-逆变
    //    0x10A0	4	逆变B相THDU	%		测量-逆变
    //    0x10A2	4	负载C相THDU	%		测量-逆变
    //    0x10A4	4	逆变电压频率	Hz		测量-逆变
    //    0x10A6	4	逆变A相电流有效值	A		测量-逆变
    //    0x10A8	4	逆变B相电流有效值	A		测量-逆变
    //    0x10AA	4	逆变C相电流有效值	A		测量-逆变
    //    0x10AC	4	逆变N线电流有效值	A		测量-逆变
    //    0x10AE	4	逆变A相电流THD	%		测量-逆变
    //    0x10B0	4	逆变B相电流THD	%		测量-逆变
    //    0x10B2	4	逆变C相电流THD	%		测量-逆变
    //    0x10B4	4	逆变A相电流峰值比	%		测量-逆变
    //    0x10B6	4	逆变B相电流峰值比	%		测量-逆变
    //    0x10B8	4	逆变C相电流峰值比	%		测量-逆变
    //    0x10BA	4	逆变A相视在功率	kVA		测量-逆变
    //    0x10BC	4	逆变B相视在功率	kVA		测量-逆变
    //    0x10BE	4	逆变C相视在功率	kVA		测量-逆变
    //    0x10C0	4	逆变A相有功功率	kW		测量-逆变
    //    0x10C2	4	逆变B相有功功率	kW		测量-逆变
    //    0x10C4	4	逆变C相有功功率	kW		测量-逆变
    //    0x10C6	4	逆变A相无功功率	kVar		测量-逆变
    //    0x10C8	4	逆变B相无功功率	kVar		测量-逆变
    //    0x10CA	4	逆变C相无功功率	kVar		测量-逆变
    //    0x10CC	4	逆变A相基波功率因数			测量-逆变
    //    0x10CE	4	逆变B相基波功率因数			测量-逆变
    //    0x10D0	4	逆变C相基波功率因数			测量-逆变
    //    0x10D2	4	逆变A相功率因数			测量-逆变
    //    0x10D4	4	逆变B相功率因数			测量-逆变
    //    0x10D6	4	逆变C相功率因数			测量-逆变
    //    0x10D8	4	逆变A相负载率	%		测量-逆变
    //    0x10DA	4	逆变B相负载率	%		测量-逆变
    //    0x10DC	4	逆变C相负载率	%		测量-逆变
    @Schema(description = "负载A相负载率，单位: %")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal phaseALoadRatio;
    @Schema(description = "负载B相负载率，单位: %")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal phaseBLoadRatio;
    @Schema(description = "负载C相负载率，单位: %")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal phaseCLoadRatio;
}
