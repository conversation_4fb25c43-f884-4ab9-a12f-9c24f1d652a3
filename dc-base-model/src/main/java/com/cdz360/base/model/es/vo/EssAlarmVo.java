package com.cdz360.base.model.es.vo;

import com.cdz360.base.model.es.type.DeviceWarnType;
import com.cdz360.base.model.es.type.WarnDeviceType;
import com.cdz360.base.model.es.type.WarnSubDeviceType;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "ESS告警信息")
@Data
@Accessors(chain = true)
public class EssAlarmVo {

    @Schema(description = "告警编号")
    private Long warningId;

    @Schema(description = "发生告警的设备编号")
    private String dno;

    @Schema(description = "发生告警的设备名称")
    private String deviceName;

    @Schema(description = "发生告警主体设备归属类型", example = "充电桩/户用储能ESS/商户储能ESS")
    private WarnDeviceType deviceType;

    @Schema(description = "告警发生时间")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private LocalDateTime happenTime;

    @Schema(description = "告警结束时间")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private LocalDateTime stopTime;

    @Schema(description = "告警内容更新时间")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "告警目标设备上属告警类型: 逆变器/整流器")
    private DeviceWarnType warnType;

    @Schema(description = "子设备类型(告警目标设备)")
    private WarnSubDeviceType subDeviceType;

    @Schema(description = "子设备名称(告警目标设备)")
    private String subDeviceName;

    @Schema(description = "子设备编号(告警目标设备)")
    private String subDeviceDno;

    @Schema(description = "告警名称")
    private String warnName;

    @Schema(description = "告警码")
    private Long warnCode;

    @Schema(description = "告警码, XxxErrorCode.name")
    private String code;

    @Schema(description = "告警状态", example = "未结束/自动结束")
    private Integer warnStatus;

    @Schema(description = "告警说明（备注）")
    private String warnNote;

    @Schema(description = "时区")
    private String tz;

    @Schema(description = "告警附加信息")
    private WarnExtraData extraData;

}
