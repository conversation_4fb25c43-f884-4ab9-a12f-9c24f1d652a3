package com.cdz360.base.model.charge.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum OrderStopCode implements DcEnum {

    C00(0, "正常停充"),
    C01(1, "充电机绝缘故障"),
    C02(2, "BMS绝缘"),
    C03(3, "充电机泄放电路故障"),
    C04(4, "漏电故障"),
    C05(5, "急停故障"),
    C06(6, ""),
    C07(7, ""),
    C08(8, ""),
    C09(9, ""),
    C10(10, "连接器故障(导引电路检测到故障)-枪过温"),
    C11(11, "连接器故障(导引电路检测到故障)-枪锁反馈"),
    C12(12, "连接器故障(导引电路检测到故障)-CC1信号"),
    C13(13, "BRO异常"),
    C14(14, "电子锁锁定错误"),
    C15(15, "K1K2外侧电压检测失败1"),
    C16(16, "K1K2外侧电压检测失败2"),
    C17(17, "预充电电压调整失败"),
    C18(18, "输出接触器故障检测"),
    C19(19, "电池反接"),
    C20(20, "充电机过温-直流模块过温/或者通讯故障"),
    C21(21, "充电机内部过温-箱体温度"),
    C22(22, "充电机电量不能传送"),
    C23(23, "充电机检测到充电电流不匹配"),
    C24(24, "充电机检测到充电电压异常"),
    C25(25, "输出短路"),
    C26(26, "主从机通讯故障"),
    C27(27, "桩体风扇故障"),
    C28(28, "K1K2 前后电压不一致"),
    C29(29, "绝缘检测母线电压反接"),
    C30(30, "输出连接器过温"),
    C31(31, "输出连接器过温-BMS元件"),
    C32(32, "连接器故障(导引电路检测到故障，充电连接器)-BMS"),
    C33(33, "电池组温度过高"),
    C34(34, "车辆连接器粘连(高压继电器)"),
    C35(35, "CC2电压检测故障"),
    C36(36, "BST报告其他故障"),
    C37(37, "BMS检测到充电电流过大"),
    C38(38, "BMS检测到充电电压异常"),
    C39(39, "电池单体电压过高"),
    C40(40, "电池单体电压过低"),
    C41(41, "SOC过高"),
    C42(42, "SOC过低"),
    C43(43, "蓄电池输出连接器状态"),
    C44(44, "BMS电池过温(BMS)"),
    C45(45, "BMS过流"),
    C46(46, "BMS绝缘"),
    C47(47, "BMS主动停充"),
    C48(48, ""),
    C49(49, ""),
    C50(50, "接收超时BRM"),
    C51(51, "接收超时BCP"),
    C52(52, "接收超时BRO"),
    C53(53, "接收超时BCS"),
    C54(54, "接收超时BCL"),
    C55(55, "接收超时BST"),
    C56(56, "接收超时BSD"),
    C57(57, "接收超时BHM/BRM"),
    C58(58, ""),
    C59(59, ""),
    C60(60, "接收超时CRM_00"),
    C61(61, "接收超时CRM_AA"),
    C62(62, "接收超时CTS，CML"),
    C63(63, "接收超时CRO"),
    C64(64, "接收超时CCS"),
    C65(65, "接收超时CST"),
    C66(66, "接收超时CSD"),
    C67(67, "其他"),
    C68(68, ""),
    C69(69, ""),
    C70(70, "烟雾报警"),
    C71(71, "水浸检测"),
    C72(72, "倾斜检测"),
    C73(73, "开门检测"),
    C74(74, "后台通讯"),
    C75(75, "屏通讯"),
    C76(76, "读卡器"),
    C77(77, "输入接触器检测"),
    C78(78, "功率分配接触器故障"),
    C79(79, "(模块)输入过欠压"),
    C80(80, "多抢，模块输入过欠压，不均流"),
    C81(81, "UI-安全管理板通讯中断"),
    C82(82, "安全管理板-UI通讯中断"),
    C83(83, "安全管理板-接触板通讯中断"),
    C84(84, "防雷器故障"),
    C85(85, "UI板或安全管理板5V跌落"),
    C86(86, "充电中桩掉电"),
    C87(87, "UI-充电控制板通讯中断"),
    C88(88, "充电控制板-UI通讯中断"),
    C89(89, "安全管理板-充电控制板通讯中断"),
    C90(90, "充电控制板-安全管理板通讯中断"),
    C91(91, "直流功率表通讯故障"),
    C92(92, "绝缘检测表通讯故障"),
    C93(93, "电池充电安全检测"),
    C94(94, "泄放电阻温度"),
    C95(95, "泄放电路驱动故障"),
    C96(96, "与BMS通讯中断(3次超时)"),
    C97(97, "充电前功率组未就绪"),
    C98(98, "BMS辅组电源电压故障"),
    C99(99, "VIN码未找到"),

    C100(100, "内存不足故障"),
    C101(101, "交流进线断电"),
    C102(102, "电表电量异常"),
    C103(103, "SD卡格式错误"),
    C104(104, "FTP模式配置失败"),
    C105(105, "RFID通信超时"),
    C106(106, "压力传感器通信超时"),
    C107(107, "摄像头通信超时"),
    C108(108, "绝缘模块检测电压失败"),
    C109(109, "电表电量数据异常"),
    C110(110, "急停处理泄放超时"),
    C111(111, "急停处理解锁超时"),
    C112(112, "正常停止处理泄放超时"),
    C113(113, "正常停止处理解锁超时"),
    C114(114, "绝缘检测电压"),
    C115(115, "绝缘检测数据"),
    C116(116, "绝缘检测报警"),
    C117(117, "防盗检测"),
    C119(119, "单枪，模块输出过欠压，不均流"),

    C130(130, "安全管理板内存不足"),
    C131(131, "PDU通信超时故障"),
    C132(132, "PDU控制命令错误"),
    C133(133, "调整功率组输出电压超时"),
    C134(134, "CTT通道执行断开操作超时"),
    C135(135, "CTT通道执行闭合操作超时"),
    C136(136, "CTT通道粘连"),
    C137(137, "CTT通道驱动失效"),
    C138(138, "CTT通道其他故障"),
    C139(139, "PDU故障"),
    C140(140, "UI板写订单记录故障"),
    C141(141, "主动充电安全故障"),
    C142(142, "熔断器故障"),
    C143(143, "压力过小故障"),
    C144(144, "压力过大故障"),
    C145(145, "CP 采样板通讯故障"),
    C146(146, "电动推杆通讯故障"),
    C147(147, "电动推杆运动控制故障"),

    C148(148, "BRM 报文数据异常"),
    C149(149, "BCP 报文数据异常"),
    C150(150, "接收超时 BRO_00"),
    C151(151, "接收超时 BRO_AA"),
    C157(157, "充电中有电压无电流"),
    C158(158, "车辆最高允许充电电压低于充电机最低输出电压"),
    C159(159, "电池电压过低"),
    C160(160, "电池电压过高"),
    C161(161, "K1K2后端电压大于车辆最高允许电压"),
    C162(162, "电表电量异常"),

    C1001(1001, "网关离线"),
    C1101(1101, "桩离线"),
    C1111(1111, "下发校时指令桩端返回失败"),
    C1112(1112, "电价下发桩端返回失败"),
    C1501(1501, "桩端返回启动充电失败"),
    C1502(1502, "桩端返回停止充电失败"),
    C1503(1503, "桩端返回预约充电失败"),
    C1504(1504, "充电控制板-UI通讯中断"),
    C1505(1505, "安全管理板-充电控制板通讯中断"),
    C1506(1506, "桩端刷卡(离线卡)失败"),
    C1901(1901, "下行指令发送超时"),
    C1002(1002, "新网关MAC地址重复"),
    C1003(1003, "新网关MAC地址为空\t"),
    C1120(1120, "桩端（心跳）返回故障码"),
    C1130(1130, "桩端（心跳）返回告警码"),

    C1005(1005, "交流-急停故障"),
    C1010(1010, "交流-枪高温故障"),
    C1012(1012, "交流-枪连接异常"),
    C1014(1014, "交流-枪锁异常"),
    C1018(1018, "交流-交流接触器异常"),
    C1021(1021, "交流-桩高温故障"),
    C1023(1023, "交流-枪过流故障"),
    C1029(1029, "交流-充电电流为0"),
    C1058(1058, "交流-枪低温故障"),
    C1059(1059, "交流-桩温度传感器异常"),
    C1068(1068, "交流-枪温度传感器异常"),
    C1069(1069, "交流-S2开关异常"),
    C1073(1073, "交流-开门检测"),
    C1074(1074, "交流-后台通讯"),
    C1075(1075, "交流-屏通讯故障"),
    C1076(1076, "交流-读卡器通讯"),
    C1079(1079, "交流-交流欠压"),
    C1080(1080, "交流-交流过压"),
    C1083(1083, "交流-电表通讯"),
    C1084(1084, "交流-CP电压采样异常"),
    C1086(1086, "交流-系统掉电"),
    C1096(1096, "交流-CP通讯故障"),
    C1099(1099, "交流-桩端异常"),

    C2000(2000, "SOC限制"),
    UNKNOWN(999999, "未知");

    @JsonValue
    private final int code;
    private final String desc;

    OrderStopCode(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static OrderStopCode valueOf(Object codeIn) {
        if (codeIn == null) {
            return OrderStopCode.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof OrderStopCode) {
            return (OrderStopCode) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (OrderStopCode status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return OrderStopCode.UNKNOWN;
    }
}
