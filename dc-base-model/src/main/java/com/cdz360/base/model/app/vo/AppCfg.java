package com.cdz360.base.model.app.vo;

import com.cdz360.base.model.app.type.AppClientType;
import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;


@Data
@Accessors(chain = true)
@Schema(description = "APP配置信息")
public class AppCfg {
    @Schema(description = "APP唯一标识", example = "wx12345678")
    private String appId;

    @Schema(description = "APP类型", example = "WX_LITE")
    private AppClientType appType;

    @Schema(description = "APP显示名称", example = "任我充")
    private String appName;

    @Schema(description = "APP所属的集团商户ID", example = "34474")
    private Long appCommId;

    @Schema(description = "最小启动充电金额,单位'元'", example = "5.01")
    private BigDecimal minChargeAmount;

    @Schema(description = "即充即退的最小充值金额,单位'元'", example = "100.01")
    private BigDecimal minPrepayAmount;

    @Schema(description = "客服电话. 空表示不显示", example = "12345678")
    private String serviceTel;

    @Schema(description = "发票菜单URL. 空表示不显示", example = "https://aaa.bbb.ccc/invoince.html")
    private String invoiceUrl;

    @Schema(description = "APP的最低支持版本, 低于该版本需要强制升级. 空表示不适用", example = "1")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer minAppVer;

    @Schema(description = "APP的最新版本号, 低于该版本建议升级. 空表示不适用", example = "3")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer appVer;

    @Schema(description = "APP安装包下载地址. 空表示不适用", example = "https://aaa.bbb.cc/aa.apk")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String appPkgUrl;

    @Schema(description = "APP更新说明, 文案会含有HTML标签. 空表示不适用", example = "1, bla bla.<br />2, bla bla")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String releaseNote;

    @Schema(description = "发票说明. 空表示不适用", example = "1, bla bla.<br />2, bla bla")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String invoiceDesc;

    @Schema(description = "模板消息配置")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<MsgTemplate> msgTemplateList;
}
