package com.cdz360.base.model.es.type.hi;

import com.cdz360.base.model.base.type.DcEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Schema(description = "ESS数据传输单元类型")
@Getter
public enum EssDtuType implements DcEnum {
    UNKNOWN(0x00, "未知"),
    ETHERNET_GATEWAY(0x01, "Ethernet Gateway"),
    WIFI_GATEWAY(0x02, "WiFi Gateway"),
    MULTI_GATEWAY(0x03, "Multi - function Gateway"),
    GPRS_GATEWAY(0x04, "GPRS Gateway"),
    NB_GATEWAY(0x05, "NB Gateway"),
    G3_GATEWAY(0x06, "3G Gateway"),
    G4_GATEWAY(0x07, "4G Gateway");

    private final int code;
    private final String desc;

    EssDtuType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static EssDtuType valueOf(Object codeIn) {
        if (codeIn == null) {
            return EssDtuType.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof EssDtuType) {
            return (EssDtuType) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (EssDtuType type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return EssDtuType.UNKNOWN;
    }

}
