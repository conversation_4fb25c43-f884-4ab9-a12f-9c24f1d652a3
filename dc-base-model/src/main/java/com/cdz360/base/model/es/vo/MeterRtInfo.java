package com.cdz360.base.model.es.vo;

import com.cdz360.base.model.es.type.MeterAlarmCode;
import com.cdz360.base.model.iot.type.EquipStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 电表实时信息
 */
@Data
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class MeterRtInfo extends EssBaseRtInfo<EquipStatus, MeterAlarmCode> {

    //
    @Schema(title = "机器型号")
    @JsonInclude(Include.NON_EMPTY)
    private String deviceModel;
}
