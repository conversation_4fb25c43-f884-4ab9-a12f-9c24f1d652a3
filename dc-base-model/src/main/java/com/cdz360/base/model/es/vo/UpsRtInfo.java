package com.cdz360.base.model.es.vo;

import com.cdz360.base.model.es.type.UpsAlarmCode;
import com.cdz360.base.model.es.type.UpsWorkMode;
import com.cdz360.base.model.iot.type.EquipStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * UPS 实时信息
 */
@Data
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class UpsRtInfo extends EssBaseRtInfo<EquipStatus, UpsAlarmCode> {


    private UpsWorkMode workStatus;

    private Long faultCode;


}
