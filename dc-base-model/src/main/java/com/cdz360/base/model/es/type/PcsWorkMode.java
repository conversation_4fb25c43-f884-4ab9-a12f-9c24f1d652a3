package com.cdz360.base.model.es.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 逆变器工作模式
 */
@Getter
public enum PcsWorkMode implements DcEnum {
    UNKNOWN(-1, "未知"),
    SELF_USE(0, "自发自用"),
    TIMED_CHARGE_DISCHARGE(1, "定时充放电"),
    TIME_OF_USE(2, "分时电价"),
    EMERGENCY_BACKUP(3, "灾备"),
    MANUAL_CONTROL(4, "手动控制"),
    PV_PRIORITY_CHARGING(5, "光伏优先充电"),
    ;
    @JsonValue
    final int code;

    final String desc;

    PcsWorkMode(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static PcsWorkMode valueOf(Object codeIn) {
        if (codeIn == null) {
            return PcsWorkMode.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof PcsWorkMode) {
            return (PcsWorkMode) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (PcsWorkMode type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return PcsWorkMode.UNKNOWN;
    }

}
