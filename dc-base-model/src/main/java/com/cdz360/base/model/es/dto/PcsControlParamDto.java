package com.cdz360.base.model.es.dto;

import com.cdz360.base.model.es.type.DevicePowerOpType;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "PCS控制参数传递")
@Data
@Accessors(chain = true)
public class PcsControlParamDto {

    @Schema(description = "PCS开关机")
    @JsonInclude(Include.NON_NULL)
    private DevicePowerOpType powerOp;

}
