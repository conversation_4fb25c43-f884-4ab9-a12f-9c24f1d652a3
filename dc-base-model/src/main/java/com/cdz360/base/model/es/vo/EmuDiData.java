package com.cdz360.base.model.es.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(title = "EMU中DI数据", description = "EMU中DI数据")
public class EmuDiData {

    @Schema(title = "EMU设备号", description = "EMU设备号")
    @JsonInclude(Include.NON_EMPTY)
    private String dno;

    @Schema(title = "时间戳", description = "数据采集的unix时间戳")
    @JsonInclude(Include.NON_NULL)
    private Long ts;

    @Schema(title = "时区", description = "设备本地的时区", example = "+8")
    @JsonInclude(Include.NON_EMPTY)
    private String tz;

    @Schema(description = "DI采集数据集合")
    private List<Integer> idxList;
}
