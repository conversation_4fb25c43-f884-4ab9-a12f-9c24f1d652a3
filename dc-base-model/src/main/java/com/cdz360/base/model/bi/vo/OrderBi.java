package com.cdz360.base.model.bi.vo;



import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class OrderBi implements Serializable {
    private static final long serialVersionUID = -4765142067144370785L;
    @Schema(description = "全部订单数量")
    private Long total;

    @Schema(description = "充电中订单数量")
    private Long charging;

    @Schema(description = "待支付订单数量")
    private Long unpaid;

    @Schema(description = "已结算订单数量")
    private Long paid;

    //private long
}
