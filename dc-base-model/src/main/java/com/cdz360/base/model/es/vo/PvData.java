package com.cdz360.base.model.es.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "光伏信息")
@Data
@Accessors(chain = true)
public class PvData {

    @Schema(description = "标号")
    private int idx;

    //    0x1200	4	光伏电压1	V		测量-光伏
    //    0x1202	4	光伏电流1	A		测量-光伏
    //    0x1204	4	光伏功率1	kW		测量-光伏
    @Schema(description = "光伏电压,单位: V")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal voltage;

    @Schema(description = "光伏电流,单位: A")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal current;

    @Schema(description = "光伏功率,单位: kW")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal power;

    //    0x1244	4	光伏1负载率	%		测量-光伏
    @Schema(description = "光伏1负载率,单位: %")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal loadRatio;
}
