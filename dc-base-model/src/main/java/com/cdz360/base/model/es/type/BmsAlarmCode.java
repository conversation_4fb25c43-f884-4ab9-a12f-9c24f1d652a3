package com.cdz360.base.model.es.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.Setter;

@Getter
public enum BmsAlarmCode implements DcEnum, EssBaseAlarmCode {
    UNKNOWN(0, "未知错误", ""),

    OFFLINE(1, "离线", "BMS设备离线"),

    DISCHARGE_TEMP_HIGH(1050, "放电温度高", "放电温度高"),
    //    DISCHARGE_TEMP_HIGH_L1(1051, "1级放电温度高", "1级放电温度高"),
//    DISCHARGE_TEMP_HIGH_L2(1052, "2级放电温度高", "2级放电温度高"),
//    DISCHARGE_TEMP_HIGH_L3(1053, "3级放电温度高", "3级放电温度高"),
    DISCHARGE_TEMP_LOW(1060, "放电温度低", "放电温度低"),
    //    DISCHARGE_TEMP_LOW_L1(1061, "1级放电温度低", "1级放电温度低"),
//    DISCHARGE_TEMP_LOW_L2(1062, "2级放电温度低", "2级放电温度低"),
//    DISCHARGE_TEMP_LOW_L3(1063, "3级放电温度低", "3级放电温度低"),
    BATTERY_VOLTAGE_HIGH(1070, "单体电压高", "单体电压高"),
    //    BATTERY_VOLTAGE_HIGH_L1(1071, "1级单体电压高", "1级单体电压高"),
//    BATTERY_VOLTAGE_HIGH_L2(1072, "2级单体电压高", "2级单体电压高"),
//    BATTERY_VOLTAGE_HIGH_L3(1073, "3级单体电压高", "3级单体电压高"),
    BATTERY_VOLTAGE_LOW(1080, "单体电压低", "单体电压低"),
    //    BATTERY_VOLTAGE_LOW_L1(1081, "1级单体电压低", "1级单体电压低"),
//    BATTERY_VOLTAGE_LOW_L2(1082, "2级单体电压低", "2级单体电压低"),
//    BATTERY_VOLTAGE_LOW_L3(1083, "3级单体电压低", "3级单体电压低"),
    BATTERY_VOLTAGE_DIFF(1090, "单体压差大", "单体压差大"),
    //    BATTERY_VOLTAGE_DIFF_L1(1091, "1级单体压差大", "1级单体压差大"),
//    BATTERY_VOLTAGE_DIFF_L2(1092, "2级单体压差大", "2级单体压差大"),
//    BATTERY_VOLTAGE_DIFF_L3(1093, "3级单体压差大", "3级单体压差大"),
    SOC_LOW(1100, "SOC低", "SOC低"),
    //    SOC_LOW_L1(1101, "1级SOC低", "1级SOC低"),
//    SOC_LOW_L2(1102, "2级SOC低", "2级SOC低"),
//    SOC_LOW_L3(1103, "3级SOC低", "3级SOC低"),
    SOC_HIGH(1110, "SOC高", "SOC高"),
    //    SOC_HIGH_L1(1111, "1级SOC高", "1级SOC高"),
//    SOC_HIGH_L2(1112, "2级SOC高", "2级SOC高"),
//    SOC_HIGH_L3(1113, "3级SOC高", "3级SOC高"),
    POLE_HIGH_TEMP(1120, "极柱温度高", "极柱温度高"),
    //    POLE_HIGH_TEMP_L1(1121, "1级极柱温度高", "1级极柱温度高"),
//    POLE_HIGH_TEMP_L2(1122, "2级极柱温度高", "2级极柱温度高"),
//    POLE_HIGH_TEMP_L3(1123, "3级极柱温度高", "3级极柱温度高"),
    CHARGE_CURRENT(1130, "充电电流大", "充电电流大"),
    //    CHARGE_CURRENT_L1(1131, "1级充电电流大", "1级充电电流大"),
//    CHARGE_CURRENT_L2(1132, "2级充电电流大", "2级充电电流大"),
//    CHARGE_CURRENT_L3(1133, "3级充电电流大", "3级充电电流大"),
    DISCHARGE_CURRENT(1140, "放电电流大", "放电电流大"),
    //    DISCHARGE_CURRENT_L1(1141, "1级放电电流大", "1级放电电流大"),
//    DISCHARGE_CURRENT_L2(1142, "2级放电电流大", "2级放电电流大"),
//    DISCHARGE_CURRENT_L3(1143, "3级放电电流大", "3级放电电流大"),
    TEMP_DIFF(1150, "温差大", "温差大"),
    //    TEMP_DIFF_L1(1151, "1级温差大", "1级温差大"),
//    TEMP_DIFF_L2(1152, "2级温差大", "2级温差大"),
//    TEMP_DIFF_L3(1153, "3级温差大", "3级温差大"),
    ISOLATION_LOW(1160, "绝缘低", "绝缘低"),
    //    ISOLATION_LOW_L1(1161, "1级绝缘低", "1级绝缘低"),
//    ISOLATION_LOW_L2(1162, "2级绝缘低", "2级绝缘低"),
//    ISOLATION_LOW_L3(1163, "3级绝缘低", "3级绝缘低"),
    TOTAL_VOLTAGE_HIGH(1170, "总压高", "总压高"),
    //    TOTAL_VOLTAGE_HIGH_L1(1171, "1级总压高", "1级总压高"),
//    TOTAL_VOLTAGE_HIGH_L2(1172, "2级总压高", "2级总压高"),
//    TOTAL_VOLTAGE_HIGH_L3(1173, "3级总压高", "3级总压高"),
    TOTAL_VOLTAGE_LOW(1180, "总压低", "总压低"),
    //    TOTAL_VOLTAGE_LOW_L1(1181, "1级总压低", "1级总压低"),
//    TOTAL_VOLTAGE_LOW_L2(1182, "2级总压低", "2级总压低"),
//    TOTAL_VOLTAGE_LOW_L3(1183, "3级总压低", "3级总压低"),
    CHARGE_TEMP_HIGH(1190, "充电温度高", "充电温度高"),
    //    CHARGE_TEMP_HIGH_L1(1191, "1级充电温度高", "1级充电温度高"),
//    CHARGE_TEMP_HIGH_L2(1192, "2级充电温度高", "2级充电温度高"),
//    CHARGE_TEMP_HIGH_L3(1193, "3级充电温度高", "3级充电温度高"),
    CHARGE_TEMP_LOW(1200, "充电温度低", "充电温度低"),
    //    CHARGE_TEMP_LOW_L1(1201, "1级充电温度低", "1级充电温度低"),
//    CHARGE_TEMP_LOW_L2(1202, "2级充电温度低", "2级充电温度低"),
//    CHARGE_TEMP_LOW_L3(1203, "3级充电温度低", "3级充电温度低"),
    TEMP_FAST_INCREASE(1516, "温升快", "温升快"),
    BUNDLE_VOLTAGE_DIFF(1517, "簇间压差大", "簇间压差大"),
    COMMUNICATION_CTRL_ERROR(1518, "与主控通讯异常", "与主控通讯异常"),
    COMMUNICATION_EMS_ERROR(1519, "与EMS通讯失败", "与EMS通讯失败"),
    PCS_CTRL_RELAY(1520, "EMS/PCS强控继电器", "EMS/PCS强控继电器"),
    COMMUNICATION_PCS_ERROR(1521, "PCS通讯超时", "PCS通讯超时"),
    COMMUNICATION_LCD_ERROR(1522, "显示屏通讯故障", "显示屏通讯故障"),
    FAN_ERROR(1523, "风扇故障", "风扇故障"),
    BUNDLE_SHORTAGE(1524, "电池簇就位数量不足", "电池簇就位数量不足"),
    BUNDLE_EXCEED(1525, "电池簇就位数量超出", "电池簇就位数量超出"),
    SBAU_BREAKER_ERROR(1526, "SBAU断路器故障", "SBAU断路器故障"),
    INIT_FAIL(1527, "初始化阻塞", "初始化阻塞"),
    MAINTAIN_CHARGE(1528, "维护充电", "维护充电"),
    PRECOMB_TEMP_ERROR(1529, "高压箱温度线故障", "高压箱温度线故障"),
    PASSIVE_BALANCE_FAULT(1530, "被动均衡故障", "被动均衡故障"),
    ACTIVE_BALANCE_FAULT(1531, "主动均衡故障", "主动均衡故障"),
    SBCU_QF_BREAKER_ERROR(1532, "SBCU-QF断路器故障", "SBCU-QF断路器故障"),
    SBMU_ERROR(1533, "SBMU硬件故障", "SBMU硬件故障"),
    BATTERY_CONNECT_ERROR(1534, "电池接线故障", "电池接线故障"),
    SBMU_COMMUNICATION_ERROR(1535, "SBMU通讯故障", "SBMU通讯故障"),
    TEMP_SENSOR_ERROR(1536, "温敏线故障", "温敏线故障"),
    MASTER_NEGATIVE_RELAY_ERROR(1537, "主负继电器故障", "主负继电器故障"),
    MASTER_POSITIVE_RELAY_ERROR(1538, "主正继电器故障", "主正继电器故障"),
    PRE_CHARGE_ERROR(1539, "预充故障", "预充故障"),
    FUSE_FAULT(1540, "保险丝故障", "保险丝故障"),
    EMERGENCY_STOP(1541, "急停", "急停"),
    CURRENT_LOOP_FAULT(1542, "电流环故障", "电流环故障"),
    TOTAL_VOLTAGE_SENSOR_ERROR(1543, "总压采集无效", "总压采集无效"),
    BATTERY_VOLTAGE_FAST_INCREASE(1544, "单体电压异常升高", "单体电压异常升高"),
    POLE_HIGH(1545, "单体极高", "单体极高"),
    POLE_LOW(1546, "单体极低", "单体极低"),
    TEMP_HIGH(1547, "温度极高", "温度极高"),
    RELAY_CTRL_ERROR(1548, "继电器强制控制", "继电器强制控制"),
    FIRE_ALARM(1549, "火警报警", "火警报警"),
    FIRE_SYS_ERROR(1550, "消防设备故障", "消防设备故障"),
    FIRE_SYS_OPERATE(1551, "消防设备动作", "消防设备动作"),
    AIR_SYS_ERROR(1552, "空调故障", "空调故障"),
    DC_LIGHTING_PROTECTION_ERROR(1553, "DC防雷故障", "DC防雷故障"),
    AC_LIGHTING_PROTECTION_ERROR(1554, "AC防雷故障", "AC防雷故障"),
    WATER_ERROR(1555, "水浸告警", "水浸告警"),
    UPS_LOW(1556, "UPS放空", "UPS放空"),
    UPS_ERROR(1557, "UPS故障", "UPS故障"),

    COMMUNICATION_SBAU_ERROR(1559, "与SBAU通讯异常", "与SBAU通讯异常"),
    PRECOMB_BREAK_ERROR(1560, "高压箱断路器故障", "高压箱断路器故障"),
    BUNDLE_REMOVE_ERROR(1565, "簇切除状态", "簇切除状态"),
    CELL_TEMP_HIGH(1580, "电池单体温度高", "电池单体温度高"),
    LMU_COMMUNICATION_ERROR(1585, "LMU通讯故障", "LMU通讯故障"),
    PRECOMB_COMMUNICATION_ERROR(1586, "高压箱通讯故障", "高压箱通讯故障"),
    ISO_COMMUNICATION_ERROR(1587, "ISO通讯故障", "ISO通讯故障"),
    WIRE_ERROR(1590, "线束故障", "线束故障"),
    RELAY_ERROR(1595, "继电器故障", "继电器故障"),
    LMU_SN_ERROR(1601, "LMU SN重复", "LMU SN重复"),
    LMU_ID_ERROR(1602, "LMU ID重复", "LMU ID重复"),
    LMU_ID_LOST_ERROR(1603, "LMU ID不连续", "LMU ID不连续"),
    LMU_LOST_ERROR(1604, "无LMU故障", "无LMU故障"),
    CT_LOST_ERROR(1610, "电流传感器失效", "电流传感器失效"),
    ;


    @JsonValue
    private final int code;

    @Setter
    private String msg;
    @Setter
    private String desc;
    @Setter
    private Integer level;


    BmsAlarmCode(int code, String msg, String desc) {
        this.code = code;
        this.msg = msg;
        this.desc = desc;
    }


    @JsonCreator
    public static BmsAlarmCode valueOf(Object codeIn) {
        if (codeIn == null) {
            return BmsAlarmCode.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof BmsAlarmCode) {
            return (BmsAlarmCode) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (BmsAlarmCode type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return BmsAlarmCode.UNKNOWN;
    }

}
