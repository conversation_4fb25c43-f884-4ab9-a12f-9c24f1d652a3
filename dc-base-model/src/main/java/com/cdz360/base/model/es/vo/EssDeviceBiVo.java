package com.cdz360.base.model.es.vo;

import com.cdz360.base.model.iot.type.EssEquipType;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "ESS挂在设备数据")
@Data
@Accessors(chain = true)
public class EssDeviceBiVo {

    @Schema(description = "设备类型", required = true)
    @JsonInclude(Include.NON_NULL)
    private EssEquipType equipType;

    @Schema(description = "设备数量", required = true)
    @JsonInclude(Include.NON_NULL)
    private Long count;
}
