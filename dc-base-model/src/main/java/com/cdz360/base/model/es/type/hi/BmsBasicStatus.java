package com.cdz360.base.model.es.type.hi;

import com.cdz360.base.model.base.type.DcEnum;
import com.cdz360.base.model.charge.type.OrderStartType;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * BMS基本状态
 */
@Getter
public enum BmsBasicStatus implements DcEnum {

    UNKNOWN(0, "未知"),
    SLEEP(1, "休眠"),
    CHARGING(2, "充电"),
    DISCHARGING(3, "放电"),
    LAYDOWN(4, "搁置"),
    FAULT(5, "故障"),
    REQUEST_STRONG_CHARGE(10, "请求强充"),
    REQUEST_EQUAL_CHARGE(11, "请求均充"),
    CHARGE_PROHIBITION(12, "禁止充电"),
    DISCHARGE_PROHIBITION(13, "禁止放电"),
    ;

    @JsonValue
    private final int code;
    private final String desc;

    BmsBasicStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static BmsBasicStatus valueOf(Object codeIn) {
        if (codeIn == null) {
            return BmsBasicStatus.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof OrderStartType) {
            return (BmsBasicStatus) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (BmsBasicStatus status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return BmsBasicStatus.UNKNOWN;
    }

}
