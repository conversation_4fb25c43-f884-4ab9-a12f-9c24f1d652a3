package com.cdz360.base.model.es.type.hi;

import com.cdz360.base.model.base.type.DcEnum;
import com.cdz360.base.model.charge.type.OrderStartType;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * BMS低压告警
 */
@Getter
public enum BmsLowVoltageAlarm implements DcEnum {

    UNKNOWN(0, "未知"),
    //    reserve(1, "保留"),
    HIGH_VOLTAGE_ALARM(2, "高压告警"),
    LOW_DISCHARGE_ALARM(3, "低压告警"),
    HIGH_TEMPERATURE_ALARM(4, "高温告警"),
    LOW_TEMPERATURE_ALARM(5, "低温告警"),
    //    reserve(6, "保留"),
    //    reserve(7, "保留"),
    DISCHARGE_HIGH_CURRENT_ALARM(8, "放电大电流告警"),
    CHARGE_HIGH_CURRENT_ALARM(9, "充电大电流告警"),
    //    reserve(10, "保留"),
    //    reserve(11, "保留"),
    SLAVE_MACHINE_CABINET_DISCONNECTED(12, "从机/柜掉线"),
    FIRE_ALARM(32, "消防告警"),
    ;

    @JsonValue
    private final int code;
    private final String desc;

    BmsLowVoltageAlarm(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static BmsLowVoltageAlarm valueOf(Object codeIn) {
        if (codeIn == null) {
            return BmsLowVoltageAlarm.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof OrderStartType) {
            return (BmsLowVoltageAlarm) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (BmsLowVoltageAlarm status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return BmsLowVoltageAlarm.UNKNOWN;
    }

}