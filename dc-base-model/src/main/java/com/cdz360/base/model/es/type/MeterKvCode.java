package com.cdz360.base.model.es.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum MeterKvCode implements DcEnum {
    UNKNOWN(120000, "未知"),

    /**
     * 当前电量数据
     */
    TOTAL_COMBINE_KWH(120001, "当前组合有功总电能"),
    TOTAL_COMBINE_JIAN_KWH(120002, "当前组合有功尖电能"),
    TOTAL_COMBINE_FENG_KWH(120003, "当前组合有功峰电能"),
    TOTAL_COMBINE_PING_KWH(120004, "当前组合有功平电能"),
    TOTAL_COMBINE_GU_KWH(120005, "当前组合有功谷电能"),
    TOTAL_POSITIVE_KWH(120011, "当前正向总有功电能"),
    TOTAL_POSITIVE_JIAN_KWH(120012, "当前正向有功尖电能"),
    TOTAL_POSITIVE_FENG_KWH(120013, "当前正向有功峰电能"),
    TOTAL_POSITIVE_PING_KWH(120014, "当前正向有功平电能"),
    TOTAL_POSITIVE_GU_KWH(120015, "当前正向有功谷电能"),
    TOTAL_NEGATIVE_KWH(120021, "当前反向总有功电能"),
    TOTAL_NEGATIVE_JIAN_KWH(120022, "当前反向有功尖电能"),
    TOTAL_NEGATIVE_FENG_KWH(120023, "当前反向有功峰电能"),
    TOTAL_NEGATIVE_PING_KWH(120024, "当前反向有功平电能"),
    TOTAL_NEGATIVE_GU_KWH(120025, "当前反向有功谷电能"),
    TOTAL_COMBINE_REACTIVE_KWH(120031, "当前组合无功总电能"),
    TOTAL_COMBINE_REACTIVE_JIAN_KWH(120032, "当前组合无功尖电能"),
    TOTAL_COMBINE_REACTIVE_FENG_KWH(120033, "当前组合无功峰电能"),
    TOTAL_COMBINE_REACTIVE_PING_KWH(120034, "当前组合无功平电能"),
    TOTAL_COMBINE_REACTIVE_GU_KWH(120035, "当前组合无功谷电能"),
    TOTAL_POSITIVE_REACTIVE_KWH(120041, "当前正向总无功电能"),
    TOTAL_POSITIVE_REACTIVE_JIAN_KWH(120042, "当前正向无功尖电能"),
    TOTAL_POSITIVE_REACTIVE_FENG_KWH(120043, "当前正向无功峰电能"),
    TOTAL_POSITIVE_REACTIVE_PING_KWH(120044, "当前正向无功平电能"),
    TOTAL_POSITIVE_REACTIVE_GU_KWH(120045, "当前正向无功谷电能"),
    TOTAL_NEGATIVE_REACTIVE_KWH(120051, "当前反向总无功电能"),
    TOTAL_NEGATIVE_REACTIVE_JIAN_KWH(120052, "当前反向无功尖电能"),
    TOTAL_NEGATIVE_REACTIVE_FENG_KWH(120053, "当前反向无功峰电能"),
    TOTAL_NEGATIVE_REACTIVE_PING_KWH(120054, "当前反向无功平电能"),
    TOTAL_NEGATIVE_REACTIVE_GU_KWH(120055, "当前反向无功谷电能"),

    /**
     * 实时电流、电压、功率数据
     */
    VOLTAGE_A(120201, "A 相电压"),
    VOLTAGE_B(120202, "B 相电压"),
    VOLTAGE_C(120203, "C 相电压"),
    VOLTAGE_AB(120205, "AB 线电压"),
    VOLTAGE_BC(120206, "BC 线电压"),
    VOLTAGE_CA(120207, "CA 线电压"),
    CURRENT_A(120211, "A 相电流"),
    CURRENT_B(120212, "B 相电流"),
    CURRENT_C(120213, "C 相电流"),
    ACTIVE_POWER(120221, "总有功功率"),
    ACTIVE_POWER_A(120222, "A 相有功功率"),
    ACTIVE_POWER_B(120223, "B 相有功功率"),
    ACTIVE_POWER_C(120224, "C 相有功功率"),

    MAX_DEMAND_RT(120225, "当前有功需量"),
    MAX_DEMAND_LAST(120226, "最大有功需量"),
    MAX_DEMAND_LAST_TIME(120227, "上次最大需量发生时间"),

    REACTIVE_POWER(120231, "总无功功率"),
    REACTIVE_POWER_A(120232, "A 相无功功率"),
    REACTIVE_POWER_B(120233, "B 相无功功率"),
    REACTIVE_POWER_C(120234, "C 相无功功率"),
    APPARENT_POWER(120241, "总视在功率"),
    APPARENT_POWER_A(120242, "A 相视在功率"),
    APPARENT_POWER_B(120243, "B 相视在功率"),
    APPARENT_POWER_C(120244, "C 相视在功率"),
    PF(120251, "总功率因数"),
    PF_A(120252, "A 相功率因数"),
    PF_B(120253, "B 相功率因数"),
    PF_C(120254, "C 相功率因数"),
//    CURRENT_COMBINE_TOTAL_KWH(120001, "xxxxxxxxx"),

    ;


    @JsonValue
    final int code;


    final String desc;


    MeterKvCode(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    @JsonCreator
    public static MeterKvCode valueOf(Object codeIn) {
        if (codeIn == null) {
            return MeterKvCode.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof MeterKvCode) {
            return (MeterKvCode) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (MeterKvCode type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return MeterKvCode.UNKNOWN;
    }

}
