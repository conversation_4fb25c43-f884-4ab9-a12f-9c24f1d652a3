package com.cdz360.base.model.es.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 寄存器读写数据
 */
@Accessors(chain = true)
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class RegisterRwValue extends EssBaseVal<BigDecimal> {

    @Schema(title = "可输入的最大值", description = "")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal max;
    @Schema(title = "可输入的最小值", description = "")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal min;

//    @Schema(title = "编码", description = "XxxKvCode.code")
//    private int code;
//
//    @Schema(title = "寄存器地址", description = "寄存器地址")
//    private Integer addr;
//
//    @Schema(title = "寄存器名称", description = "寄存器名称")
//    @JsonInclude(Include.NON_EMPTY)
//    private String name;
    @Schema(title = "小数位数", description = "默认0")
    @JsonInclude(Include.NON_NULL)
    private Integer decimal;
    @Schema(title = "显示的单位", description = "℃、%、V、A")
    @JsonInclude(Include.NON_EMPTY)
    private String unit;

    public RegisterRwValue(int code, Integer addr, String name, BigDecimal v, String desc,
        BigDecimal max,
        BigDecimal min,
        Integer decimal,
        String unit) {
        super(code, addr, name, v, desc);
        this.max = max;
        this.min = min;
        this.decimal = decimal;
        this.unit = unit;
    }

    public RegisterRwValue() {
        super();
    }

    @JsonInclude(Include.ALWAYS)
    @Override
    public BigDecimal getV() {
        return super.getV();
    }

//    @JsonInclude(Include.NON_EMPTY)
//    private String desc;
}
