package com.cdz360.base.model.es.type;

import lombok.Getter;

@Getter
public enum EssCfgStrategy {

    UNKNOWN("未知"),
    SELF_USE("自发自用"),
    PEAK_VALLEY_ARBITRAGE("峰谷套利"),
    PEAK_SHARE("削峰填谷"),
    TIMING_CHARGING_DISCHARGING("定时充放电"),
    TIME_SHARING_ELECTRICITY_PRICE("分时电价"),
    DISASTER_SPARE("灾备"),
    STANDBY("不充不放");

    private final String desc;

    EssCfgStrategy(String desc) {
        this.desc = desc;
    }

}
