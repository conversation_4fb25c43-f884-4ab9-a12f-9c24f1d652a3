package com.cdz360.base.model.es.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum BmsStatus implements DcEnum {





    UNKNOWN(-1),

    NORMAL(0),

    WARNING(20),    // 一般错误
    ERROR(21),  // 严重错误
    OFFLINE(22);

    @JsonValue
    final int code;

    BmsStatus(int code) {
        this.code = code;
    }

    @JsonCreator
    public static BmsStatus valueOf(Object codeIn) {
        if (codeIn == null) {
            return BmsStatus.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof BmsStatus) {
            return (BmsStatus) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (BmsStatus type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return BmsStatus.UNKNOWN;
    }
}
