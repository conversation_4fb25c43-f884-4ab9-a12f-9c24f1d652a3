package com.cdz360.base.model.charge.dto;

import com.cdz360.base.model.charge.type.BatteryType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Accessors(chain = true)
@Schema(description = "电池静态信息")
public class BatteryDto {

    @Schema(description = "电池类型", example = "1")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BatteryType type;

    @Schema(description = "整车电池容量. 单位: Ah", example = "11.22")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal capacity;

    @Schema(description = "整车电池标称总能量. 单位: kWh", example = "11.22")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal power;

    @Schema(description = "整车额定总电压. 单位: V", example = "11.22")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal voltage;

    @Schema(description = "电池厂商名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String vendor;


    @Schema(description = "电池组序号", example = "12")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long seqNo;

    @Schema(description = "电池生产日期", example = "2025-10-24")
    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date produceDate;

    @Schema(description = "电池使用次数", example = "12")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long usageCount;

    @Schema(description = "电池组产权标识. 0,租赁; 1,车自有")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer owner;

    @Schema(description = "电池单体最高允许充电电压. 单位: V", example = "11.22")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal unitMaxVoltage;
}
