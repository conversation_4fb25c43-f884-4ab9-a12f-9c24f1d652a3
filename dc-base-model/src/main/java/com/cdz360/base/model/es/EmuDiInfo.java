package com.cdz360.base.model.es;

import com.cdz360.base.model.es.type.EmuKvCode;
import com.cdz360.base.model.es.vo.EssBaseVal;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Schema(description = "EMU设备中DI数据信息")
@Data
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class EmuDiInfo extends EssBaseVal<Boolean> {

    @Schema(description = "DI数据类型值")
    @JsonInclude(Include.NON_NULL)
    private EmuKvCode type;

    @Deprecated
    @Schema(description = "当前状态: 0(告警);1(正常)")
    @JsonInclude(Include.NON_NULL)
    private Integer status;

    @Schema(description = "上次触发告警时间")
    @JsonInclude(Include.NON_NULL)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastAlarm;


    @Schema(description = "状态, true 正常, false 异常, null 未知")
    @Override
    public Boolean getV() {
        return super.getV();
    }
}
