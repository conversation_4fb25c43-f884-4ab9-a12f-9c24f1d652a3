package com.cdz360.base.model.es.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "报警参数信息")
public class BmsAlarmItemVo {

    @Schema(description = "1级报警值")
    @JsonProperty("l1av")
    private BigDecimal level1AlarmValue;

    @Schema(description = "2级报警值")
    @JsonProperty("l2av")
    private BigDecimal level2AlarmValue;

    @Schema(description = "3级报警值")
    @JsonProperty("l3av")
    private BigDecimal level3AlarmValue;

    @Schema(description = "报警回差值")
    @JsonProperty("ard")
    private BigDecimal alarmReturnDifference;

}
