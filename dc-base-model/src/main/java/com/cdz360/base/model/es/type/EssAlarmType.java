package com.cdz360.base.model.es.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum EssAlarmType implements DcEnum {
//    UNKNOWN(0, "未知"),

    ALERT(1, "Alert", "一次性提醒,不需要结束"),

    ALARM(2, "故障", "有开始和结束时间");

    @JsonValue
    final int code;

    final String msg;

    final String desc;


    EssAlarmType(int code, String msg, String desc) {
        this.code = code;
        this.msg = msg;
        this.desc = desc;
    }


    @JsonCreator
    public static EssAlarmType valueOf(Object codeIn) {
        if (codeIn == null) {
            return EssAlarmType.ALERT;
        }
        int code = 0;
        if (codeIn instanceof EssAlarmType) {
            return (EssAlarmType) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (EssAlarmType type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return EssAlarmType.ALERT;
    }

}
