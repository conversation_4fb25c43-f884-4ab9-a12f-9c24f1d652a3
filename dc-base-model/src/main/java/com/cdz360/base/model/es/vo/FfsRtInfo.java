package com.cdz360.base.model.es.vo;

import com.cdz360.base.model.es.type.FfsAlarmCode;
import com.cdz360.base.model.iot.type.EquipStatus;
import com.cdz360.base.model.iot.type.PowerSupply;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 消防系统(fire fighting system)实时信息
 */
@Data
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class FfsRtInfo extends EssBaseRtInfo<EquipStatus, FfsAlarmCode> {


    @Schema(title = "是否有火灾报警信息")
    @JsonInclude(Include.NON_NULL)
    @Deprecated
    private Boolean fireAlarmStatus;

    @Schema(title = "是否有故障")
    @JsonInclude(Include.NON_NULL)
    @Deprecated
    private Boolean errorStatus;

    @Schema(title = "电源供应")
    @JsonInclude(Include.NON_NULL)
    @Deprecated
    private PowerSupply powerSupply;

    @Schema(title = "启动控制", description = "1：系统存在启动控制信号；0：默认状态；")
    @JsonInclude(Include.NON_NULL)
    @Deprecated
    private Boolean startControl;

    @Schema(title = "启动喷洒", description = "延时结束后，发出启动喷洒信号")
    @JsonInclude(Include.NON_NULL)
    @Deprecated
    private Boolean startStatus;

    @Schema(title = "是否在喷洒")
    @JsonInclude(Include.NON_NULL)
    @Deprecated
    private Boolean workStatus;

}
