package com.cdz360.base.model.es.dto;

import com.cdz360.base.model.es.type.InverterGridMode;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "PCS配置参数传递")
@Data
@Accessors(chain = true)
public class PcsConfigParamDto {

    @Schema(description = "PCS设备时间(true-将系统时间同步到PCS;false-不需要将系统时间同步到PCS)")
    @JsonInclude(Include.NON_NULL)
    private Boolean syncSysTime2Pcs;

    @Schema(description = "逆变器有功功率设定")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal inverterActivePower;

    @Schema(description = "逆变器无功功率设定")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal inverterReactivePower;

    @Schema(description = "逆变器运行模式")
    @JsonInclude(Include.NON_NULL)
    private InverterGridMode inverterRuntimeMode;

    @Schema(description = "PCS额定功率")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal pcsRatedPower;

}
