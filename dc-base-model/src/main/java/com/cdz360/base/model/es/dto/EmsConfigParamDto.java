package com.cdz360.base.model.es.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "EMS配置参数传递")
@Data
@Accessors(chain = true)
public class EmsConfigParamDto {

    @Schema(description = "设备信息上报时间间隔,单位 秒")
    @JsonInclude(Include.NON_NULL)
    private Integer uploadDeviceInfoDuration;

    @Schema(description = "上报设备实时数据时间间隔，单位 秒")
    @JsonInclude(Include.NON_NULL)
    private Integer uploadRtDataDuration;

}
