package com.cdz360.base.model.base.type;

import lombok.Getter;

/**
 * 分时电价尖峰平谷的标签
 */
@Getter
public enum ChargePriceCategory implements DcEnum {

    PRICE_TAG_OTHER(0, "其他"),

    PRICE_TAG_JIAN(1, "尖"),

    PRICE_TAG_FENG(2, "峰"),

    PRICE_TAG_PING(3, "平"),

    PRICE_TAG_GU(4, "谷"),
    ;

    private final int code;
    private final String desc;

    ChargePriceCategory(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public static ChargePriceCategory valueOf(Object codeIn) {
        if (codeIn == null) {
            return ChargePriceCategory.PRICE_TAG_OTHER;
        }
        int code = 0;
        if (codeIn instanceof ChargePriceCategory) {
            return (ChargePriceCategory) codeIn;
        } else if (codeIn instanceof Integer) {
            code = (Integer) codeIn;
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (ChargePriceCategory status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return ChargePriceCategory.PRICE_TAG_OTHER;
    }
}
