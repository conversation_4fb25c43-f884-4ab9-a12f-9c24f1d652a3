package com.cdz360.base.model.es.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 信号量
 */
@Accessors(chain = true)
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class DiVal extends SignalVal {

    @Schema(title = "时间戳", description = "数据采集的unix时间戳")
    @JsonInclude(Include.NON_NULL)
    private Long ts;

    @Schema(title = "时区", description = "设备本地的时区", example = "+8")
    @JsonInclude(Include.NON_EMPTY)
    private String tz;

    public DiVal(int code, Integer addr, String name, Integer v, String desc, Long ts, String tz) {
        super(code, addr, name, v, desc);
        this.ts = ts;
        this.tz = tz;
    }

    public DiVal() {
        super();
    }

//    @Schema(title = "编码", description = "XxxKvCode.code")
//    private int code;
//
//    @Schema(title = "寄存器地址", description = "寄存器地址")
//    private Integer addr;
//
//    @Schema(title = "寄存器名称", description = "寄存器名称")
//    @JsonInclude(Include.NON_EMPTY)
//    private String name;
//
//    private Integer v;
//
//    @JsonInclude(Include.NON_EMPTY)
//    private String desc;
}
