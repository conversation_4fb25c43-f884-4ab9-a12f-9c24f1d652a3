package com.cdz360.base.model.es.type.hi;

import com.cdz360.base.model.charge.type.OrderStartType;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 光伏运行状态
 */
@Getter
public enum PvOpStatus {

    UNKNOWN(999, "未知"),
    STANDBY(0, "待机"),
    running(1, "就绪"),
    ;

    @JsonValue
    private final int code;
    private final String desc;

    PvOpStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static PvOpStatus valueOf(Object codeIn) {
        if (codeIn == null) {
            return PvOpStatus.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof OrderStartType) {
            return (PvOpStatus) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (PvOpStatus status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return PvOpStatus.UNKNOWN;
    }

}
