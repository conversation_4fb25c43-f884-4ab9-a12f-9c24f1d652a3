package com.cdz360.base.model.es.type.hi;

import com.cdz360.base.model.base.type.DcEnum;
import com.cdz360.base.model.charge.type.OrderStartType;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Getter
@Schema(description = "安规认证国家")
public enum MonitorCertificationNation implements DcEnum {

    UNKNOWN(0, "未知"),
    GERMANY(1, "德国"),
    UK(2, "英国"),
    EUROPEAN_STANDARD(3, "欧标"),
    SOUTH_AFRICA(4, "南非"),
    POLAND(5, "波兰"),
    AUSTRIA(6, "奥地利"),
    ITALY(7, "意大利"),
    ;

    @JsonValue
    private final int code;
    private final String desc;

    MonitorCertificationNation(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static MonitorCertificationNation valueOf(Object codeIn) {
        if (codeIn == null) {
            return MonitorCertificationNation.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof OrderStartType) {
            return (MonitorCertificationNation) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (MonitorCertificationNation status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return MonitorCertificationNation.UNKNOWN;
    }

}