package com.cdz360.base.model.cus.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "客户信息同步")
public class CusSyncDto {

    @Schema(description = "用户ID", example = "123")
    private Long id;

    @Schema(description = "集团商户Id", example = "123")
    private Long topCommId;

    @Schema(description = "用户名(昵称)")
    private String username;

    @Schema(description = "用户姓名")
    private String name;

    @Schema(description = "性别. 1.男,2.女", example = "1")
    private Integer sex;

    @Schema(description = "用户状态（10000-删除，10001-正常,10002-加入黑名单）???", example = "123")
    private Integer status;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "城市编码")
    private String cityCode;

    @Schema(description = "默认扣款账户ID. 现金账户/权益账户时为商户ID; 授信账户时为授信账户ID", example = "123")
    private Long balanceId;

    @Schema(description = "1, 基本账户(t_balance); 2, 集团授权账户(t_r_bloc_user); 3, 权益(商户)账户;", example = "1")
    private Integer defaultPayType;

    @Schema(description = "是否有效", example = "true")
    private Boolean enable;

    @Schema(description = "操作人ID（非必填）")
    private Long opId;

    @Schema(description = "操作人姓名（非必填）")
    private String opName;

}
