package com.cdz360.base.model.es.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 设备开关机配置类型
 */
@Getter
public enum EquipOpenModeType implements DcEnum {

    UNKNOWN(-1, "未知"),
    OFF(0, "关机"),
    ON(1, "开机"),
    STANDBY(2, "待机");
    @JsonValue
    final int code;


    final String desc;


    EquipOpenModeType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    @JsonCreator
    public static EquipOpenModeType valueOf(Object codeIn) {
        if (codeIn == null) {
            return EquipOpenModeType.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof EquipOpenModeType) {
            return (EquipOpenModeType) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (EquipOpenModeType type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return EquipOpenModeType.UNKNOWN;
    }
}
