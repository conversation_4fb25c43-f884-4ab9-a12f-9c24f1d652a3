package com.cdz360.base.model.es.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "电池信息")
@Data
@Accessors(chain = true)
public class BatData {

    @Schema(description = "标号")
    private int idx;
    //    0x120C	4	电池电压1	V		测量-电池
    //    0x120E	4	电池电流1	A		测量-电池
    //    0x1210	4	电池功率1	kW		测量-电池
    @Schema(description = "电池电压,单位: V")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal voltage;

    @Schema(description = "电池电流,单位: A")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal current;

    @Schema(description = "电池功率,单位: kW")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal power;
    //    0x1212	4	电池满容量百分比1	%		测量-电池
    @Schema(description = "电池满容量百分比,单位: %")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal soc;
    //    0x1214	4	电池满载支撑时间1	h		测量-电池
    @Schema(description = "电池满载支撑时间,单位: h")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal fullLoadSupportTime;
    //    0x1238	4	电池1充电次数			测量-电池
    @Schema(description = "电池充电次数")
    private long chargeNum;
    //    0x123A	4	电池1放电次数			测量-电池
    @Schema(description = "电池放电次数")
    private long dischargeNum;
}
