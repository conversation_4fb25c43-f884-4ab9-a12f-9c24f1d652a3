package com.cdz360.base.model.iot.dto;

import com.cdz360.base.model.iot.vo.PlugVo;
import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.springframework.beans.BeanUtils;

/**
 * 枪头队列消息
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PlugMqDto extends PlugVo {

    @Schema(description = "电桩型号, 如: G4-001")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String modelName;


    @Schema(description = "桩固件(软件)版本")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String firmwareVer;


    public PlugMqDto() {
        // do nothing
    }

    public PlugMqDto(PlugVo vo) {
        BeanUtils.copyProperties(vo, this);
    }
}
