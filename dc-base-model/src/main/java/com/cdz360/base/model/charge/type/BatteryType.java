package com.cdz360.base.model.charge.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum BatteryType implements DcEnum {

    UNKNOWN(0, "未知"),

    // 铅酸电池
    QIAN_SUAN(1, "铅酸电池"),

    // 镍氢电池
    NIE_QING(2, "镍氢电池"),

    // 磷酸铁锂电池
    LIN_SUAN_TIE_LI(3, "磷酸铁锂电池"),

    // 锰酸锂电池
    MENG_SUAN_LI(4, "锰酸锂电池"),

    // 钴酸锂电池
    GU_SUAN_LI(5, "钴酸锂电池"),

    // 三元材料电池
    SAN_YUAN_CAI_LIAO(6, "三元材料电池"),

    // 聚合物锂离子电池
    JU_HE_WU_LI_LI_ZI(7, "聚合物锂离子电池"),

    // 钛酸锂电池
    TAI_SUAN_LI(8, "钛酸锂电池"),

    // 其他电池
    OTHER(255, "其他电池"),

    ;


    @JsonValue
    private final int code;
    private final String desc;

    BatteryType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static BatteryType valueOf(Object codeIn) {
        if (codeIn == null) {
            return BatteryType.UNKNOWN;
        }
        int code = 0;
        if(codeIn instanceof BatteryType) {
            return (BatteryType)codeIn;
        }
        else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (BatteryType status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return BatteryType.UNKNOWN;
    }


}
