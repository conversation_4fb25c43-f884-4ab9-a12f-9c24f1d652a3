package com.cdz360.base.model.es.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * UPS字段编码
 */
@Getter
public enum UpsKvCode implements DcEnum {
    UNKNOWN(170000, "未知"),

    /**
     * 静态信息
     */
//    HW_SN(170001, "硬件序号"),
//    HW_MODEL(170005, "硬件型号"),
//    SW_VER(170010, "软件版本"),

    /**
     * 设备状态信息
     */
    UPS_STATUS(170101, "设备状态"),
    UPS_STATUS_BAT_TEST_OK(170110, "BAT_TEST_OK"),
    UPS_STATUS_BAT_TEST_FAIL(170111, "BAT_TEST_FAIL"),
    UPS_STATUS_BAT_SILENCE(170112, "BAT_SILENCE"),
    UPS_STATUS_SHUTDOWN_ACTIVE(170113, "SHUTDOWN_ACTIVE"),
    UPS_STATUS_TEST_IN_PROGRESS(170114, "TEST_IN_PROGRESS"),
    UPS_STATUS_EPO(170115, "SPD"),
    UPS_STATUS_UPS_FAILED(170116, "UPS_FAILED"),
    UPS_STATUS_BYPASS_BOOST_ACTIVE(170117, "BYPASS_BOOST_ACTIVE"),
    UPS_STATUS_BATTERY_LOW(170118, "BATTERY_LOW"),
    UPS_STATUS_UTILITY_FAIL(170119, "UTILITY_FAIL"),
    UPS_STATUS_STANDBY(170120, "STANDBY"),
    UPS_STATUS_LINE_INTERACTIVE(170121, "LINE_INTERACTIVE"),
    UPS_STATUS_ONLINE(170122, "ONLINE"),
    UPS_RUN_MODE(170130, "工作模式"),



//    TMS_K2_RELAY_STATUS(170131, "TMS_K2接触器状态"),
//    PTC_STATUS(170132, "PTC状态"),
//    WATER_PUMP_STATUS(170133, "水泵状态"),
//    MSD_STATUS(170134, "MSD状态"),
//    TMS_COMM_STATUS(170135, "TMS通讯状态"),

    /**
     * 传感器数据
     */
    VOLTAGE_IN(170200, "输入电压"),
    VOLTAGE_OUT(170201, "输出电压"),

    POSITIVE_BUS_VOLTAGE(170210, "正总线电压"),
    NEGATIVE_BUS_VOLTAGE(170211, "负总线电压"),


    POSITIVE_BAT_VOLTAGE(170220, "正电池电压"),
    NEGATIVE_BAT_VOLTAGE(170221, "负电池电压"),
    CURRENT_OUT(170230, "输出电流"),
    LOAD_OUT(170235, "输出负载"),
    FREQUENCY_IN(170240, "输入频率"),
    FREQUENCY_OUT(170241, "输出频率"),


    TEMP_MAX(170270, "探测点最高温度"),

    /**
     * 设定参数
     */
//    CFG_STOP_RELAY(170301, "断继电器命令"),

    /**
     * 告警、故障
     */
    UPS_FAULT_CODE(170411, "故障码"),
//    TMS_FAULT_GRADE(170415, "液冷机组故障等级"),

    ;

    @JsonValue
    final int code;


    final String desc;


    UpsKvCode(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    @JsonCreator
    public static UpsKvCode valueOf(Object codeIn) {
        if (codeIn == null) {
            return UpsKvCode.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof UpsKvCode) {
            return (UpsKvCode) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (UpsKvCode type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return UpsKvCode.UNKNOWN;
    }
}
