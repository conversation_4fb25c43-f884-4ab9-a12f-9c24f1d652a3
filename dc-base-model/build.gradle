plugins {
    id 'java'
}

jar {
    enabled = true
}


dependencies {


    implementation("org.slf4j:slf4j-api:${slf4jVersion}")
//    implementation("io.springfox:springfox-swagger2:${swaggerVersion}")

    implementation("org.springdoc:springdoc-openapi-ui:${springdocVersion}")

    implementation("com.fasterxml.jackson.core:jackson-annotations:${jacksonVersion}")
    implementation("com.fasterxml.jackson.core:jackson-databind:${jacksonVersion}")
}