
plugins {
    id 'java'
}

jar{
    enabled = true
}


dependencies {

    implementation project(':dc-base-model')
    implementation project(':dc-base-utils')

    implementation("com.fasterxml.jackson.core:jackson-core:${jacksonVersion}")
    implementation("com.fasterxml.jackson.core:jackson-annotations:${jacksonVersion}")

    testRuntimeOnly('org.springframework.cloud:spring-cloud-starter-config')
}