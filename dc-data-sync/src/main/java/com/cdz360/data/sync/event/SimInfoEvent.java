package com.cdz360.data.sync.event;

import com.cdz360.base.model.base.type.IotEvent;
import com.cdz360.base.model.iot.vo.PvGtiVo;

public class SimInfoEvent extends IotBaseQEvent<PvGtiVo> {

    private static final long serialVersionUID = 307988413110341560L;

    private IotEvent eventType;

    public SimInfoEvent() {
    }

    public SimInfoEvent(IotEvent eventType, PvGtiVo sim) {
        super(MqEventSubType.MQ_SIM, sim);
        this.eventType = eventType;
    }

    public IotEvent getEventType() {
        return eventType;
    }

}
