package com.cdz360.data.sync.event.ess;

import com.cdz360.base.model.base.type.DcEnum;
import lombok.Getter;

@Getter
public enum EssPushEventType implements DcEnum {
    STATUS_ALARM(1, "设备状态与告警"),
    COLLECT_DATA(2, "设备模拟量"),
    FACTORY_INFO(3, "设备厂家信息"),
    COMPONENT_DATA(4, "设备组件数据")
    ;

    private final int code;
    private final String desc;

    EssPushEventType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static EssPushEventType valueOf(int code) {
        for (EssPushEventType source : values()) {
            if (source.code == code) {
                return source;
            }
        }
        return null;
    }

}
