package com.cdz360.data.sync.event;

import com.cdz360.base.model.base.type.IotEvent;
import com.cdz360.base.model.es.dto.EssAlarmNotify;
import com.cdz360.base.model.es.dto.EssAlarms;

public class EssAlarmEvent2 extends IotBaseQEvent<EssAlarms> {


    private static final long serialVersionUID = 7240161137142876088L;
    private IotEvent eventType;

    public EssAlarmEvent2() {
    }

    public EssAlarmEvent2(IotEvent eventType, EssAlarms alarms) {
        super(MqEventSubType.MQ_ESS_ALARM2, alarms);
        this.eventType = eventType;
    }

    public IotEvent getEventType() {
        return eventType;
    }

}
