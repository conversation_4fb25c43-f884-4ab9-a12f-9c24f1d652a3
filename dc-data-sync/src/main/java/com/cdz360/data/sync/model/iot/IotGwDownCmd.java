package com.cdz360.data.sync.model.iot;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 网关下行指令
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class IotGwDownCmd extends IotGwUpCmd {
    private static final long serialVersionUID = 3983606547626208461L;

    private int ttl;

    private String msg;
}
