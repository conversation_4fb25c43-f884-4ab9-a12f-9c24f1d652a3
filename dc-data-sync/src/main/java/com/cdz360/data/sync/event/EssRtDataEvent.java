package com.cdz360.data.sync.event;

import com.cdz360.base.model.base.type.IotEvent;
import com.cdz360.base.model.es.vo.EmuRtData;

public class EssRtDataEvent extends IotBaseQEvent<EmuRtData> {

    private static final long serialVersionUID = -15871143125858273L;

    private IotEvent eventType;

    public EssRtDataEvent() {
    }

    public EssRtDataEvent(IotEvent eventType, EmuRtData data) {
        super(MqEventSubType.MQ_ESS_RT_DATA, data);
        this.eventType = eventType;
    }

    public IotEvent getEventType() {
        return eventType;
    }

}
