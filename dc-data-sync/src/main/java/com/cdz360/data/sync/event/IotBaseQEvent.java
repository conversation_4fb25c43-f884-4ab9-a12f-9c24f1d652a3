package com.cdz360.data.sync.event;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * IoT MQ基类
 */
public abstract class IotBaseQEvent<T> extends DcBaseQEvent<T> {

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String linkId;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String ctrlNo;

    public IotBaseQEvent() {
        //super(source, originService, destinationService);
        //id = ID.getAndSet(1L);
        //seq = UUID.randomUUID().toString();
    }

    public IotBaseQEvent(T data) {
        //super(source, originService, destinationService);
        super(data);
    }

    public IotBaseQEvent(MqEventSubType mqSubType, T data) {
        //super(source, originService, destinationService);
        super(mqSubType, data);
    }

    public void setLinkId(String linkId) {
        this.linkId = linkId;
    }

    public String getLinkId() {
        return linkId;
    }

    public String getCtrlNo() {
        return ctrlNo;
    }

    public void setCtrlNo(String ctrlNo) {
        this.ctrlNo = ctrlNo;
    }
}