package com.cdz360.data.sync.service;

import com.cdz360.base.model.base.type.IotEvent;
import com.cdz360.base.model.es.dto.EssAlarmNotify;
import com.cdz360.base.model.es.dto.EssAlarms;
import com.cdz360.base.model.es.dto.EssFactoryDto;
import com.cdz360.base.model.es.vo.EmuRtData;
import com.cdz360.base.model.es.vo.hi.InverterRtData;
import com.cdz360.base.model.es.vo.hi.InverterRtInfo;
import com.cdz360.base.model.iot.vo.EssVo;
import com.cdz360.data.sync.event.MqEventSubType;

public interface EssEventPublisher {

//    /**
//     * 储能ESS告警推送
//     *
//     * @param alarms 告警信息
//     */
//    void publishEssAlarm(EssAlarms alarms);

//    /**
//     * 户用储能ESS运行数据变更通知
//     *
//     * @param rtData 运行数据
//     */
//    void publishEssRtData(EmuRtData rtData);
//
//    /**
//     * 发送同步场站ESS信息
//     *
//     * @param eventType 触发推送事件的类型
//     * @param ess       储能信息
//     */
//    void publishEssInfo(IotEvent eventType, EssVo ess);

    /**
     * 推送设备工厂信息
     *
     * @param serialNo 设备序列号
     * @param data 设备工厂信息
     */
    void publishEssFactoryInfo(String serialNo, EssFactoryDto data);

    /**
     * 推送设备状态和告警量
     *
     * @param serialNo 设备序列号
     * @param essStatus 储能设备状态和告警量信息
     */
    void publishEssStatus(String serialNo, InverterRtInfo essStatus);


    /**
     * 推送设备数据
     *
     * @param serialNo 设备序列号
     * @param essData 储能设备数据
     */
    void publishEssData(String serialNo, InverterRtData essData);


    /**
     * 推送设备子组件数据信息
     *
     * @param serialNo 设备序列号
     * @param type 组件消息类型
     * @param deviceData 储能设备子组件数据信息(json字符串)
     */
    void publishEssSubDeviceData(String serialNo, MqEventSubType type, String deviceData);


}
