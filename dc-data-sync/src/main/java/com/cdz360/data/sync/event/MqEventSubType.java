package com.cdz360.data.sync.event;

import com.cdz360.base.model.base.type.DcEnum;
import lombok.Getter;

@Getter
public enum MqEventSubType implements DcEnum {


    MQ_UNKNOWN(0, MqEventType.UNKNOWN, "不识别的消息"),

    MQ_EVSE(2, MqEventType.MQ_IOT, "桩消息"),

    MQ_PLUG(3, MqEventType.MQ_IOT, "枪头消息"),

    MQ_CTRL(4, MqEventType.MQ_IOT, "场站控制盒消息"),
    MQ_PV_GTI(5, MqEventType.MQ_IOT, "光伏逆变器消息"),
    MQ_ESS(7, MqEventType.MQ_IOT, "储能ESS消息"),
    MQ_SRS(8, MqEventType.MQ_IOT, "辐射仪消息"),
    MQ_SIM(9, MqEventType.MQ_IOT, "SIM卡消息"),

    MQ_CHARGE_ORDER(6, MqEventType.MQ_IOT, "充电订单消息"),

    MQ_GW_DOWN_CMD(11, MqEventType.MQ_GW_CMD_DOWN, "网关下行消息"),

    MQ_GW_UP_CMD(12, MqEventType.MQ_GW_CMD_UP, "网关上行相应消息"),

    MQ_GW_CMD_TIMEOUT(13, MqEventType.MQ_GW_CMD_UP, "网关指令超时"),

    MQ_COMMERCIAL(21, MqEventType.MQ_OPERATE_INFO, "商户信息"),

    MQ_SITE(22, MqEventType.MQ_OPERATE_INFO, "场站信息"),
    MQ_SITE_GROUP(40, MqEventType.MQ_OPERATE_INFO, "场站组信息"),

    MQ_CUS_INFO(23, MqEventType.MQ_OPERATE_INFO, "用户信息"),

    MQ_CORP(25, MqEventType.MQ_OPERATE_INFO, "企业信息"),

    MQ_CORP_ORG(26, MqEventType.MQ_OPERATE_INFO, "企业组织信息"),

    MQ_CORP_USER(27, MqEventType.MQ_OPERATE_INFO, "企业个人信息"),

    MQ_SMS_MSG(31, MqEventType.MQ_PUSH_MSG, "短信"),

    MQ_WX_LITE_MSG(32, MqEventType.MQ_PUSH_MSG, "微信小程序推送消息"),

    MQ_ALI_LITE_MSG(33, MqEventType.MQ_PUSH_MSG, "支付宝小程序推送消息"),

    MQ_IOS_MSG(34, MqEventType.MQ_PUSH_MSG, "iOS APP推送消息"),

    MQ_ANDROID_MSG(35, MqEventType.MQ_PUSH_MSG, "安卓APP推送消息"),
    MQ_CORP_WX_MSG(36, MqEventType.MQ_PUSH_MSG, "企业微信推送消息"),

    MQ_ESS_AMMETER_COMPONENT_MSG(50, MqEventType.MQ_PUSH_MSG, "ESS组件电表设备数据"),
    MQ_ESS_BMS_COMPONENT_MSG(51, MqEventType.MQ_PUSH_MSG, "ESS组件BMS设备数据"),
    MQ_ESS_OTHER_COMPONENT_MSG(52, MqEventType.MQ_PUSH_MSG, "ESS组件其他设备数据"),

    MQ_ESS_ALARM(70, MqEventType.MQ_IOT, "储能ESS告警消息"),
    MQ_ESS_ALARM2(71, MqEventType.MQ_IOT, "储能ESS告警消息"),
    MQ_ESS_RT_DATA(75, MqEventType.MQ_IOT, "储能ESS运行时数据"),
    ;

    private final int code;
    private final MqEventType type;
    private final String desc;


    MqEventSubType(int code, MqEventType type, String desc) {
        this.code = code;
        this.type = type;
        this.desc = desc;
    }


    public static MqEventSubType codeOf(int code) {
        for (MqEventSubType t : MqEventSubType.values()) {
            if (t.code == code) {
                return t;
            }
        }
        return MqEventSubType.MQ_UNKNOWN;
    }
}
