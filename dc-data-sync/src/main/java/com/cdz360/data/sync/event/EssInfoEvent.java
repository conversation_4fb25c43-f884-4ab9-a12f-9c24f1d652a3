package com.cdz360.data.sync.event;

import com.cdz360.base.model.base.type.IotEvent;
import com.cdz360.base.model.iot.vo.EssVo;
import com.cdz360.base.model.iot.vo.PvGtiVo;

public class EssInfoEvent extends IotBaseQEvent<EssVo> {

    private static final long serialVersionUID = 4098222007829990567L;

    private IotEvent eventType;

    public EssInfoEvent() {
    }

    public EssInfoEvent(IotEvent eventType, EssVo ess) {
        super(MqEventSubType.MQ_ESS, ess);
        this.eventType = eventType;
    }

    public IotEvent getEventType() {
        return eventType;
    }

}
