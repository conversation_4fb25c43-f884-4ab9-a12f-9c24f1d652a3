package com.cdz360.data.sync.event.ess;

import com.cdz360.data.sync.event.DcBaseQEvent;

public abstract class EssBaseQEvent<T> extends DcBaseQEvent<T> {

    // 通讯设备序列号
    private String serialNo;

    // 推送数据类型
    private EssPushEventType type;

    public EssBaseQEvent(EssPushEventType type) {
        this.type = type;
    }

    public EssBaseQEvent(String serialNo, EssPushEventType type) {
        this.serialNo = serialNo;
        this.type = type;
    }

    public String getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(String serialNo) {
        this.serialNo = serialNo;
    }

    public EssPushEventType getType() {
        return type;
    }

    public void setType(EssPushEventType type) {
        this.type = type;
    }
}
