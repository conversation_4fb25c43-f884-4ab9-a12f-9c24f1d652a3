package com.cdz360.data.sync.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class SiteGroup {

    // 场站组唯一ID
    @JsonInclude(Include.NON_EMPTY)
    private String gid;

    // 场站ID列表
    @JsonInclude(Include.NON_NULL)
    private List<String> siteIdList;

}
