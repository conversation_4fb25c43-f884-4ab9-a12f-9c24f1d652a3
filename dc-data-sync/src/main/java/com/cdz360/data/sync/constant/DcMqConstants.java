package com.cdz360.data.sync.constant;

public interface DcMqConstants {
    /**
     * 储能的 rabbitmq exchange 名字
     */
    String MQ_EXCHANGE_NAME_ESS = "exchangeEss";

    /**
     * 基础设施的 rabbitmq exchange 名字
     */
    String MQ_EXCHANGE_NAME_INFRASTRUCTURE = "exchangeInfrastructure";

    /**
     * 信息同步的 exchange 名字
     */
    String MQ_EXCHANGE_NAME_SYNC = "exchangeSync";

    /**
     * 客户信息的 rabbitmq exchange 名字
     */
    String MQ_EXCHANGE_NAME_CUS = "exchangeCus";

    /**
     * iot 的 rabbitmq exchange 名字
     */
    String MQ_EXCHANGE_NAME_IOT = "exchangeIot";

    /**
     * iot 网关 下行指令 exchange 名字
     */
    String MQ_EXCHANGE_NAME_IOT_GW = "exchangeIotGw";

    /**
     * iot 的 cmd exchange
     */
    String MQ_EXCHANGE_NAME_IOT_CMD = "exchangeIotCmd";

    /**
     * 充电订单的 rabbitmq exchange 名字
     */
    String MQ_EXCHANGE_NAME_CHARGE_ORDER = "exchangeChargeOrder";

    /**
     * 消息推送相关的 rabbitmq exchange 名字
     */
    String MQ_EXCHANGE_NAME_PUSH_MSG = "exchangePushMsg";

    /**
     * 系统操作日志的 rabbitmq exchange 名字
     */
    String MQ_EXCHANGE_NAME_SYS_USER_LOG = "exchangeSysUserLog";

    /**
     * 桩信息的rabbitmq 队列 exchange 路由的key
     */
//    @Deprecated(since = "将在6月版本发布后去掉")
//    String MQ_ROUTING_KEY_EVSE = "evse";

    /**
     * 枪头信息的rabbitmq 队列 exchange 路由的key
     */
//    @Deprecated(since = "将在6月版本发布后去掉")
//    String MQ_ROUTING_KEY_PLUG = "plug";

    /**
     * 枪头, 桩, 场站控制器 信息
     */
    String MQ_ROUTING_KEY_IOT = "iot";

    /**
     * 网关上行指令的 exchange 路由
     */
    String MQ_ROUTING_KEY_GW_CMD_UP = "gwCmdUp";

    /**
     * 网关下行指令的 exchange 路由
     */
    String MQ_ROUTING_KEY_GW_CMD_DOWN = "gwCmdDown";

    /**
     * 网关下行指令响应超时的 exchange 路由
     */
    String MQ_ROUTING_KEY_GW_CMD_TIMEOUT = "gwCmdTimeout";

    String MQ_ROUTING_KEY_SYNC = "sync";

    /**
     * 场站信息的rabbitmq 队列 exchange 路由的key
     */
    String MQ_ROUTING_KEY_SITE = "site";

    /**
     * 户储设备信息的rabbitmq 队列 exchange 路由的key
     */
    String MQ_ROUTING_KEY_ESS = "ess";

    /**
     * 场站组信息的 rabbitmq 队列 exchange 路由的key
     */
    String MQ_ROUTING_KEY_SITE_GROUP = "siteGroup";

    /**
     * 商户信息的 rabbitmq 队列 exchange 路由的key
     */
    String MQ_ROUTING_KEY_COMMERCIAL = "commercial";

    /**
     * 客户信息的 rabbitmq 队列 exchange 路由的key
     */
    String MQ_ROUTING_KEY_CUS_INFO = "cusInfo";

    /**
     * 企业信息的 rabbitmq 队列 exchange 路由的key
     */
    String MQ_ROUTING_KEY_CORP_INFO = "corpInfo";

    /**
     * 企业充电用户信息的 rabbitmq 队列 exchange 路由的key
     */
    String MQ_ROUTING_KEY_CORP_USER_INFO = "corpUserInfo";

    /**
     * 企业组织信息的 rabbitmq 队列 exchange 路由的key
     */
    String MQ_ROUTING_KEY_CORP_ORG_INFO = "corpOrgInfo";

    /**
     * 充电订单的 rabbitmq 队列 exchange 路由的key
     */
    String MQ_ROUTING_KEY_CHARGE_ORDER = "chargeOrder";

    /**
     * 推送消息的 rabbitmq 队列的路由 key
     */
    String MQ_ROUTING_KEY_PUSH_MSG = "pushMsg";


    /**
     * 系统操作日志的 rabbitmq 队列的路由 key
     */
    String MQ_ROUTING_KEY_SYS_USER_LOG = "sysUserLog";
}
