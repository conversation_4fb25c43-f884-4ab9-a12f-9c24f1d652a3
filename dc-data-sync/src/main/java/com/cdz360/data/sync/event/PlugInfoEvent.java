package com.cdz360.data.sync.event;

import com.cdz360.base.model.base.type.IotEvent;
import com.cdz360.base.model.iot.dto.PlugMqDto;
import com.cdz360.base.model.iot.vo.PlugVo;

/**
 * 同步枪头信息的事件
 */
public class PlugInfoEvent extends IotBaseQEvent<PlugMqDto> {

    private static final long serialVersionUID = 6633055408006839797L;

    private IotEvent eventType;

    public PlugInfoEvent() {
    }

    public PlugInfoEvent(IotEvent eventType, PlugMqDto plug) {
        super(MqEventSubType.MQ_PLUG, plug);
        this.eventType = eventType;
    }

    public IotEvent getEventType() {
        return eventType;
    }

}
