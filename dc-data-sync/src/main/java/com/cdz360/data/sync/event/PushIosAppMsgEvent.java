package com.cdz360.data.sync.event;

import com.cdz360.base.utils.JsonUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class PushIosAppMsgEvent extends DcBaseQEvent<String> {

    private static final long serialVersionUID = -2642218873052115941L;

    public PushIosAppMsgEvent() {
    }

    public PushIosAppMsgEvent(String msg) {
        super(MqEventSubType.MQ_IOS_MSG, msg);
    }


    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
