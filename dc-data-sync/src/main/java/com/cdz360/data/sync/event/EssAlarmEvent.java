package com.cdz360.data.sync.event;

import com.cdz360.base.model.base.type.IotEvent;
import com.cdz360.base.model.es.dto.EssAlarmNotify;

public class EssAlarmEvent extends IotBaseQEvent<EssAlarmNotify> {

    private static final long serialVersionUID = 6019523933652972064L;

    private IotEvent eventType;

    public EssAlarmEvent() {
    }

    public EssAlarmEvent(IotEvent eventType, EssAlarmNotify ess) {
        super(MqEventSubType.MQ_ESS_ALARM, ess);
        this.eventType = eventType;
    }

    public IotEvent getEventType() {
        return eventType;
    }

}
