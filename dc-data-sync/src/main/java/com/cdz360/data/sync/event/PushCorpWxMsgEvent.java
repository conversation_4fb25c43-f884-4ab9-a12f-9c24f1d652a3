package com.cdz360.data.sync.event;

import com.cdz360.base.utils.JsonUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class PushCorpWxMsgEvent extends DcBaseQEvent<String> {


    private static final long serialVersionUID = 6027465159188920314L;

    public PushCorpWxMsgEvent() {
    }

    public PushCorpWxMsgEvent(String msg) {
        super(MqEventSubType.MQ_CORP_WX_MSG, msg);
    }


    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
