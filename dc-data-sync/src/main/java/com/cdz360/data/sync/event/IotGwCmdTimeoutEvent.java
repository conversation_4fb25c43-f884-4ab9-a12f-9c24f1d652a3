package com.cdz360.data.sync.event;

import com.cdz360.data.sync.model.iot.IotGwCmdTimeout;

/**
 * 网关下行指令响应超时事件
 */
public class IotGwCmdTimeoutEvent extends DcBaseQEvent<IotGwCmdTimeout> {


    private static final long serialVersionUID = -4011048274529143932L;

    public IotGwCmdTimeoutEvent() {
    }

    public IotGwCmdTimeoutEvent(IotGwCmdTimeout cmd) {
        super(MqEventSubType.MQ_GW_CMD_TIMEOUT, cmd);
    }

}
