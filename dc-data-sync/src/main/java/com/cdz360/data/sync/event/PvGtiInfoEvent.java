package com.cdz360.data.sync.event;

import com.cdz360.base.model.base.type.IotEvent;
import com.cdz360.base.model.iot.vo.PvGtiVo;

public class PvGtiInfoEvent extends IotBaseQEvent<PvGtiVo> {

    private static final long serialVersionUID = 807988461310342190L;

    private IotEvent eventType;

    public PvGtiInfoEvent() {
    }

    public PvGtiInfoEvent(IotEvent eventType, PvGtiVo gti) {
        super(MqEventSubType.MQ_PV_GTI, gti);
        this.eventType = eventType;
    }

    public IotEvent getEventType() {
        return eventType;
    }

}
