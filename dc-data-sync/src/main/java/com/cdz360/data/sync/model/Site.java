package com.cdz360.data.sync.model;

import com.cdz360.base.model.base.vo.BaseObject;
import com.cdz360.base.model.charge.type.SiteDynamicPowerType;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.annotation.CheckForSigned;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class Site extends BaseObject {
    /**
     * 场站ID
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteId;

    /**
     * 场站编号
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteNo;
    /**
     * 场站名
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String name;

    /**
     * 场站的集团商户ID
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long topCommId;

    /**
     * 场站的商户ID
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long commId;

    /**
     * 商户联系电话
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String commPhone;

    /**
     * 运营属性
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer bizType;

    /**
     * 场站类型. 见字典表 siteType
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer siteType;

    /**
     * 场站类型(文案). 见字典表 siteType
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteTypeDesc;
    /**
     * 省编码
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String province;
    /**
     * 城市编码
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String city;
    /**
     * 地区编码
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String area;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String areaName;

    /**
     * 详细地址
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String address;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer acPlugNum;//交流枪数

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer dcPlugNum;//直流枪数

    /**
     * 0,未知; 1,停车免费; 2,停车收费；
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer parkFeeType;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String parkFeeDesc;//停车费描述

    /**
     * 营业时间描述
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String openHourDesc;

    /**
     * 场站联系电话
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String phone;
    /**
     * 场站状态
     * 0: 已下线;
     * 1: 待上线;
     * 2: 已上线;
     * 3: 维护中
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private int status;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal lon;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal lat;

    /**
     * 时区, 如: +8
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String timeZone;

    /**
     * 计费模板ID
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long priceCode;


    /**
     * 场站功率分配模式(有序充电)
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private SiteDynamicPowerType dyPow;
}
