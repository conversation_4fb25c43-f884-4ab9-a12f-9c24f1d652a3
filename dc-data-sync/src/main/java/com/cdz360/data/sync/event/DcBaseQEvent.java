package com.cdz360.data.sync.event;

import com.cdz360.base.utils.JsonUtils;

import java.io.Serializable;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicLong;

//import org.springframework.cloud.bus.event.RemoteApplicationEvent;

public abstract class DcBaseQEvent<T> //extends RemoteApplicationEvent
        implements Serializable {

    public static final String FIELD_MQ_SUB_TYPE = "mqSubType";

    protected MqEventSubType mqSubType;
    protected T data;
    private static AtomicLong ID = new AtomicLong(1L);
    private String seq;
    private long id;

    public DcBaseQEvent() {
        //super(source, originService, destinationService);
        //id = ID.getAndSet(1L);
        //seq = UUID.randomUUID().toString();
    }

    public DcBaseQEvent(T data) {
        //super(source, originService, destinationService);
        id = ID.getAndAdd(1L);
        seq = UUID.randomUUID().toString();
        this.data = data;
    }

    public DcBaseQEvent(MqEventSubType mqSubType, T data) {
        //super(source, originService, destinationService);
        id = ID.getAndAdd(1L);
        seq = UUID.randomUUID().toString();
        this.mqSubType = mqSubType;
        this.data = data;
    }

    public T getData() {
        return this.data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public String getSeq() {
        return seq;
    }

    public DcBaseQEvent<T> setSeq(String seq) {
        this.seq = seq;
        return this;
    }

    public long getId() {
        return id;
    }

    public DcBaseQEvent<T> setId(long id) {
        this.id = id;
        return this;
    }

    public MqEventSubType getMqSubType() {
        return this.mqSubType;
    }

    public void setMqSubType(MqEventSubType mqSubType) {
        this.mqSubType = mqSubType;
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
