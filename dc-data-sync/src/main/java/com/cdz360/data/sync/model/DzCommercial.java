package com.cdz360.data.sync.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 东正商户信息
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class DzCommercial implements Serializable {
    private static final long serialVersionUID = 5043504062568371727L;
    /**
     * 编号
     **/
    private Long id;

    /**
     * 集团商户ID
     */
    private Long topCommId;

    /**
     * 上级商户ID
     */
    private Long pid;

    /**
     * 商户级别
     **/
    private Integer commLevel;

    /**
     * 商户号
     **/
    private String merchants;

    /**
     * 商户类型(1:个人、2:企业)
     **/
    private Integer commType;

    /**
     * 商户名称
     **/
    private String commName;

    /**
     * 简称
     **/
    private String shortName;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 商户ID链
     */
    private String idChain;
}
