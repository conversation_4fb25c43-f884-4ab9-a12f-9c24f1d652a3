package com.cdz360.data.sync.event;

import com.cdz360.base.model.base.type.IotEvent;
import com.cdz360.base.model.iot.vo.EvseVo;

public class EvseInfoEvent extends IotBaseQEvent<EvseVo> {


    private static final long serialVersionUID = -4508996409663064105L;

    private IotEvent eventType;

    public EvseInfoEvent() {
    }

    public EvseInfoEvent(IotEvent eventType, EvseVo evse) {
        super(MqEventSubType.MQ_EVSE, evse);
        this.eventType = eventType;
    }

    public IotEvent getEventType() {
        return eventType;
    }

}

