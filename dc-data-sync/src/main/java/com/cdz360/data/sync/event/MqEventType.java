package com.cdz360.data.sync.event;

import com.cdz360.base.model.base.type.DcEnum;
import lombok.Getter;

@Getter
public enum MqEventType implements DcEnum {


    UNKNOWN(0, "不识别的消息"),

    MQ_IOT(1, "设备/订单消息"),

    MQ_GW(2, "网关下行消息"),

    MQ_GW_CMD_DOWN(3, "网关下行消息监控"),

    MQ_GW_CMD_UP(4, "网关上行消息监控"),

    MQ_OPERATE_INFO(5, "运营基础数据"),

    MQ_PUSH_MSG(6, "C端推送消息");


    private final int code;
    private final String desc;


    MqEventType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public static MqEventType codeOf(int code) {
        for (MqEventType t : MqEventType.values()) {
            if (t.code == code) {
                return t;
            }
        }
        return MqEventType.UNKNOWN;
    }
}
