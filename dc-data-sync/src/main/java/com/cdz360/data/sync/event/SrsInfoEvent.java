package com.cdz360.data.sync.event;

import com.cdz360.base.model.base.type.IotEvent;
import com.cdz360.base.model.iot.vo.PvGtiVo;

public class SrsInfoEvent extends IotBaseQEvent<PvGtiVo> {

    private static final long serialVersionUID = 307988413110341560L;

    private IotEvent eventType;

    public SrsInfoEvent() {
    }

    public SrsInfoEvent(IotEvent eventType, PvGtiVo gti) {
        super(MqEventSubType.MQ_SRS, gti);
        this.eventType = eventType;
    }

    public IotEvent getEventType() {
        return eventType;
    }

}
