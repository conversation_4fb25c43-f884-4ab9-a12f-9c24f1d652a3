package com.cdz360.data.sync.event;

import com.cdz360.base.model.base.type.IotEvent;
import com.cdz360.base.model.iot.vo.SiteCtrlVo;

/**
 * 同步场站控制盒信息的事件
 */
public class IotCtrlInfoEvent extends IotBaseQEvent<SiteCtrlVo> {

    private static final long serialVersionUID = 6633055408006839797L;

    private IotEvent eventType;

    public IotCtrlInfoEvent() {
    }

    public IotCtrlInfoEvent(IotEvent eventType, SiteCtrlVo ctrl) {
        super(MqEventSubType.MQ_CTRL, ctrl);
        this.eventType = eventType;
    }

    public IotEvent getEventType() {
        return eventType;
    }

}
