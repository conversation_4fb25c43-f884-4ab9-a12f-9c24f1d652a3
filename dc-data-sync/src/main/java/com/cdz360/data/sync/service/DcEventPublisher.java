package com.cdz360.data.sync.service;

import com.cdz360.base.model.base.type.IotEvent;
import com.cdz360.base.model.charge.vo.ChargeOrderVo;
import com.cdz360.base.model.corp.dto.CorpOrgSyncDto;
import com.cdz360.base.model.corp.dto.CorpSyncDto;
import com.cdz360.base.model.corp.dto.CorpUserSyncDto;
import com.cdz360.base.model.cus.dto.CusSyncDto;
import com.cdz360.base.model.es.dto.EssAlarmNotify;
import com.cdz360.base.model.es.dto.EssAlarms;
import com.cdz360.base.model.es.vo.EmuRtData;
import com.cdz360.base.model.iot.dto.PlugMqDto;
import com.cdz360.base.model.iot.vo.EssVo;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.model.iot.vo.PvGtiVo;
import com.cdz360.base.model.iot.vo.SiteCtrlVo;
import com.cdz360.data.sync.model.DzCommercial;
import com.cdz360.data.sync.model.Site;
import com.cdz360.data.sync.model.SiteGroup;
import com.cdz360.data.sync.model.iot.IotGwCmdTimeout;
import com.cdz360.data.sync.model.iot.IotGwDownCmd;
import com.cdz360.data.sync.model.iot.IotGwUpCmd;

public interface DcEventPublisher {

    /**
     * 发送同步场站信息的mq消息
     *
     * @param siteInfo 场站信息
     */
    void publishSiteInfo(Site siteInfo);

    /**
     * 发送同步场站组信息的mq消息
     *
     * @param siteGroup 场站组信息
     */
    void publishSiteGroupInfo(SiteGroup siteGroup);

    /**
     * 发送同步商户信息的mq消息
     *
     * @param commercial 商户信息
     */
    void publishDzCommercialInfo(DzCommercial commercial);

    /**
     * 发送同步客户信息的mq消息
     *
     * @param cusInfo 客户信息
     */
    void publishCusInfo(CusSyncDto cusInfo);

    /**
     * 发送同步企业信息的mq消息
     *
     * @param corpInfo 企业信息
     */
    void publishCorpInfo(CorpSyncDto corpInfo);

    /**
     * 发送同步企业充电用户信息的mq消息
     *
     * @param corpUserInfo 企业充电用户信息
     */
    void publishCorpUserInfo(CorpUserSyncDto corpUserInfo);

    /**
     * 发送同步企业组织信息的mq消息
     *
     * @param corpOrgInfo 企业组织信息
     */
    void publishCorpOrgInfo(CorpOrgSyncDto corpOrgInfo);

    /**
     * 发送同步枪头信息的mq消息
     *
     * @param eventType 触发推送事件的类型
     * @param plug      枪头信息
     */
    void publishPlugInfo(IotEvent eventType, PlugMqDto plug);

    void publishPlugInfo(IotEvent eventType, PlugMqDto plug, String linkId, String ctrlNo);

    /**
     * 发送同步桩信息的mq消息
     *
     * @param eventType 触发推送事件的类型
     * @param evse      桩信息
     */
    void publishEvseInfo(IotEvent eventType, EvseVo evse);

    void publishEvseInfo(IotEvent eventType, EvseVo evse, String linkId, String ctrlNo);

    /**
     * 发送同步场站控制器信息
     *
     * @param eventType 触发推送事件的类型
     * @param ctrl      控制盒
     */
    void publishSiteCtrlInfo(IotEvent eventType, SiteCtrlVo ctrl);

    /**
     * 发送同步场站逆变器信息
     *
     * @param eventType 触发推送事件的类型
     * @param gti       逆变器信息
     */
    void publishPvGtiInfo(IotEvent eventType, PvGtiVo gti);

    /**
     * 发送同步场站辐射仪信息
     *
     * @param eventType 触发推送事件的类型
     * @param srs       辐射仪信息
     */
    void publishSrsInfo(IotEvent eventType, PvGtiVo srs);

    /**
     * 发送同步SIM卡信息
     *
     * @param eventType 触发推送事件的类型
     * @param sim       SIM卡信息
     */
    void publishSimInfo(IotEvent eventType, PvGtiVo sim);

    /**
     * 户用储能ESS状态信息
     *
     * @param notify 告警信息
     * @deprecated
     */
    @Deprecated
    void publishEssAlarm(EssAlarmNotify notify);
    /**
     * 储能ESS告警推送
     *
     * @param alarms 告警信息
     */
    void publishEssAlarm(EssAlarms alarms);

    /**
     * 户用储能ESS运行数据变更通知
     *
     * @param rtData 运行数据
     */
    void publishEssRtData(EmuRtData rtData);

    /**
     * 发送同步场站ESS信息
     *
     * @param eventType 触发推送事件的类型
     * @param ess       储能信息
     */
    void publishEssInfo(IotEvent eventType, EssVo ess);

    /**
     * 网关上行指令
     *
     * @param cmd 指令
     */
    void publishIotGwUpCmd(IotGwUpCmd cmd);

    /**
     * 网关下行指令
     *
     * @param cmd 指令
     */
    void publishIotGwDownCmd(IotGwDownCmd cmd);

    /**
     * 网关下行指令响应超时的推送消息
     *
     * @param cmd 超时的指令
     */
    void publishIotGwCmdTimeout(IotGwCmdTimeout cmd);

    /**
     * 发送充电订单的mq消息
     *
     * @param chargeOrder 充电订单
     */
    void publishChargeOrder(ChargeOrderVo chargeOrder);


    /**
     * 发送网关下行指令
     *
     * @param gwno 网关编号
     * @param msg  发送给网关的下行指令
     */
    void publishIotGwCmd(String gwno, String msg);


    /**
     * 发送短信推送消息
     *
     * @param msg 推送消息内容
     */
    void publishSmsMsg(String msg);

    /**
     * 发送微信小程序推送消息
     *
     * @param msg 推送消息内容
     */
    void publishWxLiteMsg(String msg);

    /**
     * 支付宝小程序推送
     *
     * @param msg 推送消息内容
     */
    void publishAliLiteMsg(String msg);


    /**
     * iOS APP推送消息
     *
     * @param msg 推送消息内容
     */
    void publishIosAppMsg(String msg);


    /**
     * 安卓APP推送消息
     *
     * @param msg 推送消息内容
     */
    void publishAndroidAppMsg(String msg);

    /**
     * 企业微信推送消息
     *
     * @param msg 推送消息内容
     */
    void publishCorpWxMsg(String msg);

    /**
     * 系统操作日志
     *
     * @param msg 推送消息内容
     */
    void publishSysUserLog(String msg);
}
