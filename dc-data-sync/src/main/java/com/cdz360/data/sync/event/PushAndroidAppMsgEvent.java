package com.cdz360.data.sync.event;

import com.cdz360.base.utils.JsonUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class PushAndroidAppMsgEvent extends DcBaseQEvent<String> {


    private static final long serialVersionUID = -8734532193785792519L;


    public PushAndroidAppMsgEvent() {
    }

    public PushAndroidAppMsgEvent( String msg) {
        super(MqEventSubType.MQ_ANDROID_MSG, msg);
    }


    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
