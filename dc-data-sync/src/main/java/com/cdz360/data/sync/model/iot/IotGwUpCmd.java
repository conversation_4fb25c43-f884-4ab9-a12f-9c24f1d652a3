package com.cdz360.data.sync.model.iot;

import com.cdz360.base.model.base.type.IotGwCmdType2;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class IotGwUpCmd implements Serializable {

    private static final long serialVersionUID = -6568057447839128758L;

    @JsonProperty(value = "n")
    private String gwno;

    @JsonProperty(value = "v")
    private Integer ver;

    private String seq;

    private IotGwCmdType2 cmd;
}
