package com.cdz360.ds.type;

import com.cdz360.base.model.base.type.DcEnum;
import java.lang.reflect.Method;
import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

@Slf4j
public class EnumTypeHandler<E extends DcEnum> extends BaseTypeHandler<E> {

    private final Class<E> type;
    private final E[] enums;

    public EnumTypeHandler(Class<E> type) {
        if (type == null) {
            throw new IllegalArgumentException("Type argument cannot be null");
        }
        this.type = type;
        this.enums = type.getEnumConstants();
        if (this.enums == null) {
            throw new IllegalArgumentException(
                type.getSimpleName() + " does not represent an enum type.");
        }
    }

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, E parameter, JdbcType jdbcType)
        throws SQLException {
        ps.setInt(i, parameter.getCode());
    }

    @Override
    public E getNullableResult(ResultSet rs, String columnName) throws SQLException {
        Object ordinal = rs.getObject(columnName);
        if (ordinal == null) {
            return null;
        } else if (ordinal instanceof Integer) {
            return toEnum((Integer) ordinal);
        } else if (ordinal instanceof Long) {
            return toEnum(((Long) ordinal).intValue());
        } else if (ordinal instanceof String) {
            return toEnum((String) ordinal);
        } else {
            log.error("不识别的类型. {}/{}", ordinal, ordinal.getClass());
            return null;
        }

//        Integer val = rs.getInt(columnName);
////        if (rs.wasNull()) {
////            val = null;
////        }
//        return toEnum(val);
    }

    @Override
    public E getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        Object ordinal = rs.getObject(columnIndex);
        if (ordinal == null) {
            return null;
        } else if (ordinal instanceof Integer) {
            return toEnum((Integer) ordinal);
        } else if (ordinal instanceof Long) {
            return toEnum(((Long) ordinal).intValue());
        } else if (ordinal instanceof String) {
            return toEnum((String) ordinal);
        } else {
            log.error("不识别的类型. {}/{}", ordinal, ordinal.getClass());
            return null;
        }
    }

    @Override
    public E getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        Object ordinal = cs.getObject(columnIndex);
        if (ordinal == null) {
            return null;
        } else if (ordinal instanceof Integer) {
            return toEnum((Integer) ordinal);
        } else if (ordinal instanceof Long) {
            return toEnum(((Long) ordinal).intValue());
        } else if (ordinal instanceof String) {
            return toEnum((String) ordinal);
        } else {
            log.error("不识别的类型. {}/{}", ordinal, ordinal.getClass());
            return null;
        }
//        Integer ordinal = cs.getInt(columnIndex);
////        if (cs.wasNull()) {
////            ordinal = 0;
////        }
//        return toEnum(ordinal);
    }

    private E toEnum(Integer code) {
        if (code == null) {
            return null;
        }
        try {
            Method m = type.getMethod("getCode");
            for (E e : enums) {
                if (code == (int) m.invoke(e)) {
                    return e;
                }
            }

            return null;
        } catch (Exception ex) {
            throw new IllegalArgumentException(
                "Cannot convert " + code + " to " + type.getSimpleName()
                    + " by ordinal value.", ex);
        }
    }

    private E toEnum(String name) {
        if (name == null) {
            return null;
        }
        try {
            Method m = type.getMethod("name");
            for (E e : enums) {
                if (name.equals((String) m.invoke(e))) {
                    return e;
                }
            }

            return null;
        } catch (Exception ex) {
            throw new IllegalArgumentException(
                "Cannot convert " + name + " to " + type.getSimpleName()
                    + " by ordinal value.", ex);
        }
    }
}
