plugins {
    id 'java'
    id 'org.springframework.boot' version '3.2.9'   // springBoot版本号
    id 'io.spring.dependency-management' version '1.1.6'
    id "org.sonarqube" version "3.5.0.2730"
    id 'jacoco'
    id 'maven-publish'
    //id "application"
}

apply plugin: 'idea'

//java {
//    toolchain {
//        languageVersion = JavaLanguageVersion.of(17)
//    }
//}

ext {
    springCloudVersion = "2023.0.3"
    lombokVersion = '1.18.34'
    slf4jVersion = '2.0.16'
    springdocVersion = '1.6.15'
//    swaggerVersion = '3.0.0'
    mybatisSpringVersion = '3.0.3'  // org.mybatis.spring.boot:mybatis-spring-boot-starter
    jacksonVersion = "2.17.2"
    commonsPoolVersion = '2.11.1'
    //apache commons pool. redis pool 需要 https://commons.apache.org/proper/commons-pool/
    junitVersion = '5.9.1'
}


allprojects {
    group = 'com.cdz360.cloud'
    version = "###!!!DC_BASE_VERSION!!!###"
//    version = "20241019-SNAPSHOT"
    apply plugin: 'java'
    apply plugin: 'org.springframework.boot'
    apply plugin: 'io.spring.dependency-management'
    sourceCompatibility = JavaVersion.VERSION_17
    targetCompatibility = JavaVersion.VERSION_17

    compileJava {
        options.encoding = "UTF-8"
    }
    javadoc {
        options.encoding = 'UTF-8'
    }

    repositories {
        maven {
            url 'https://repo.iot.renwochong.com/repository/maven-snapshots/'
            mavenContent {
                snapshotsOnly()
            }
        }
        maven { url 'https://maven.aliyun.com/repository/central' }
        //maven { url 'https://maven.aliyun.com/repository/jcenter' }
        mavenCentral()
    }


    dependencyManagement {
        imports {
            mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
        }
    }

    javadoc {
        options.charSet = 'UTF-8'
    }
}

sonarqube {
    properties {
        property "sonar.sourceEncoding", "UTF-8"
    }
}


subprojects {

    apply plugin: 'jacoco'
    apply plugin: 'maven-publish'


    task javadocJar(type: Jar) {
        archiveClassifier = 'javadoc'
        from javadoc
    }

    task sourcesJar(type: Jar) {
        archiveClassifier = 'sources'
        from sourceSets.main.allSource
    }

    artifacts {
        archives javadocJar, sourcesJar
    }


    jacocoTestReport {
        reports {
            xml.required = true
        }
    }

    test.finalizedBy jacocoTestReport

    test {
        useJUnitPlatform()
    }


    dependencies {

        compileOnly group: 'com.google.code.findbugs', name: 'jsr305', version: '3.0.1'

        compileOnly("org.projectlombok:lombok:${lombokVersion}")
        annotationProcessor("org.projectlombok:lombok:${lombokVersion}")
        testAnnotationProcessor("org.projectlombok:lombok:${lombokVersion}")
        testCompileOnly("org.projectlombok:lombok:${lombokVersion}")

        testImplementation("org.junit.jupiter:junit-jupiter:${junitVersion}")

        testImplementation("org.springframework.boot:spring-boot-starter-test") {
            //exclude group: 'junit'
        }
    }


    sonarqube {
        properties {
            property "sonar.sources", "src"
            property "sonar.exclusions", "**/test/**/*.java"
            property "sonar.projectKey", "g4-cloud-base"
            property "sonar.projectName", "g4-cloud-base"
        }
    }


    publishing {
        publications {
            library(MavenPublication) {
                from components.java
            }
        }
        repositories {
            maven {
                credentials {
                    username = "jenkins"
                    password = "dingchongjenkins"
                }

                url = "https://repo.iot.renwochong.com/repository/maven-snapshots/"
            }

        }
    }


}


dependencies {

}
