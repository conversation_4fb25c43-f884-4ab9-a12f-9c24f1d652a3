
pluginManagement {
    repositories {

        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
        gradlePluginPortal()

    }
}

rootProject.name = 'g4-cloud-base'
include 'dc-base-model'
include 'dc-base-utils'
include 'dc-base-ds'
include 'dc-cloud-oam'
include 'dc-cloud-eureka'
include 'dc-cloud-zipkin'
include 'dc-data-sync'
include 'dc-data-sync-publisher'
include 'dc-data-cache'
include 'dc-data-cache-reader'
include 'dc-data-cache-writer'
include 'dc-mgc-utils'
include 'ess-sync-publisher'

