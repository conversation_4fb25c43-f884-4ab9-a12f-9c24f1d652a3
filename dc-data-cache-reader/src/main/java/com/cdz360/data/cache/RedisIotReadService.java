package com.cdz360.data.cache;

import com.cdz360.base.model.base.type.EvseStatus;
import com.cdz360.base.model.base.type.PlugStatus;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.model.iot.vo.PlugVo;
import com.cdz360.base.model.iot.vo.TransformerVo;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.SerializeUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.data.cache.model.IotGwCmdCacheVo;
import com.cdz360.data.cache.utils.RedisCacheUtils;
import com.cdz360.data.cache.utils.RedisKeyGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
@Service
public class RedisIotReadService {

    @Autowired
    protected StringRedisTemplate redisTemplate;

    /**
     * 根据场站ID获取绑定的桩编号
     *
     * @param siteId 场站ID
     * @return 返回桩编号Set
     */
    public Set<String> getSiteEvses(String siteId) {
        String key = RedisKeyGenerator.genSiteEvseRedisKey(siteId);
        return redisTemplate.opsForSet().members(key);
    }

    /**
     * 根据场站ID获取桩信息
     *
     * @param siteId 场站ID
     * @return 桩缓存信息数组
     */
    public List<EvseVo> listEvseBySiteId(String siteId) {
        List<String> plugNoList = new ArrayList<>();
        plugNoList.addAll(this.getSiteEvses(siteId));
        return this.getEvseList(plugNoList);
    }

    /**
     * 根据场站ID获取绑定的枪头编号
     *
     * @param siteId 场站ID
     * @return 返回枪头编号Set
     */
    public Set<String> getSitePlugs(String siteId) {
        String key = RedisKeyGenerator.genSitePlugRedisKey(siteId);
        return redisTemplate.opsForSet().members(key);
    }

    /**
     * 根据场站id获取枪头信息列表
     *
     * @param siteId 场站ID
     * @return 枪头信息数组
     */
    public List<PlugVo> listPlugBySiteId(String siteId) {
        List<String> plugNoList = new ArrayList<>();
        plugNoList.addAll(this.getSitePlugs(siteId));
        return this.getPlugList(plugNoList);
    }

    /**
     * 从redis获取变压器信息
     *
     * @param tfmId 变压器ID
     * @return 返回桩的缓存信息. 如果没有, 返回null
     */
    public TransformerVo getTransformerRedisCache(Long tfmId) {
        String key = RedisKeyGenerator.genTransformerRedisKey(tfmId);
        return RedisCacheUtils.getHashFromRedis(redisTemplate, key, TransformerVo.class);
    }


    /**
     * 从redis获取桩信息
     *
     * @param evseNo 桩号
     * @return 返回桩的缓存信息. 如果没有, 返回null
     */
    public EvseVo getEvseRedisCache(String evseNo) {
        String key = RedisKeyGenerator.genEvseRedisKey(evseNo);
        return RedisCacheUtils.getHashFromRedis(redisTemplate, key, EvseVo.class);
    }

    /**
     * 根据桩号数组获取桩缓存信息
     *
     * @param evseNoList 桩号数组
     * @return 返回桩信息数组
     */
    public List<EvseVo> getEvseList(List<String> evseNoList) {
        List<Object> result = this.redisTemplate.executePipelined(new RedisCallback<Object>() {
            @Override
            public Object doInRedis(RedisConnection connection) throws DataAccessException {
                for (String evseNo : evseNoList) {
                    String key = RedisKeyGenerator.genEvseRedisKey(evseNo);
                    connection.hGetAll(key.getBytes());
                }
                return null;
            }
        });
        log.debug("result = {}", JsonUtils.toJsonString(result));
        List<EvseVo> list = new ArrayList<>();
        for (int i = 0; i < result.size(); i++) {
            Map<String, String> map = (Map<String, String>) result.get(i);
            if (map.isEmpty()) {
                EvseVo vo = new EvseVo();
                vo.setEvseNo(evseNoList.get(i))
                        .setStatus(EvseStatus.OFFLINE);
                list.add(vo);
            } else {
                EvseVo plug = SerializeUtils.mapToObject(EvseVo.class, map);
                list.add(plug);
            }
        }
        return list;

    }

    /**
     * 根据枪头编号获取枪缓存信息
     *
     * @param plugNoList 枪头编号数组
     * @return 返回枪头信息数组
     */
    public List<PlugVo> getPlugList(List<String> plugNoList) {

        List<Object> result = this.redisTemplate.executePipelined(new RedisCallback<Object>() {
            @Override
            public Object doInRedis(RedisConnection connection) throws DataAccessException {
                for (String plugNo : plugNoList) {
                    String key = RedisKeyGenerator.genPlugRedisKey(plugNo);
                    connection.hGetAll(key.getBytes());
                }
                return null;
            }
        });
        log.debug("result = {}", JsonUtils.toJsonString(result));
        List<PlugVo> list = new ArrayList<>();
        for (int i = 0; i < result.size(); i++) {
            Map<String, String> map = (Map<String, String>) result.get(i);
            if (map.isEmpty()) {
                PlugVo vo = new PlugVo();
                vo.setPlugNo(plugNoList.get(i))
                        .setStatus(PlugStatus.OFFLINE);
                list.add(vo);
            } else {
                PlugVo plug = SerializeUtils.mapToObject(PlugVo.class, map);
                list.add(plug);
            }
        }
        return list;
    }

    public PlugVo getPlugRedisCache(String plugNo) {
        String key = RedisKeyGenerator.genPlugRedisKey(plugNo);
        return RedisCacheUtils.getHashFromRedis(redisTemplate, key, PlugVo.class);
    }

    /**
     * 从redis获取枪头信息
     *
     * @param evseNo 桩号
     * @param idx    枪头序号, 从1开始
     * @return 返回枪头的缓存信息. 如果没有, 返回null
     */
    public PlugVo getPlugRedisCache(String evseNo, int idx) {
        String key = RedisKeyGenerator.genPlugRedisKey(evseNo, idx);
        return RedisCacheUtils.getHashFromRedis(redisTemplate, key, PlugVo.class);
    }

    /**
     * 获取网关下行指令的缓存数据
     *
     * @param gwno 网关编号
     * @param seq  下行指令序列号
     * @return 网关下行指令的缓存数据
     */
    public IotGwCmdCacheVo getIotGwCmd(String gwno, String seq) {
        String key = RedisKeyGenerator.genIotGwCmdKye(gwno, seq);
        String str = redisTemplate.opsForValue().get(key);
        return str == null ? null : JsonUtils.fromJson(str, IotGwCmdCacheVo.class);
    }

    /**
     * 返回创建时间 小于timestamp 的seq数据
     *
     * @param timestamp 过滤的时间戳
     * @return 返回创建时间早于 timestamp 的 seq 列表
     */
    public List<String> getGwSeqs(long timestamp) {
        Set<String> keys = redisTemplate.keys(CacheConstants.REDIS_KEY_IOT_GW_CMD_SEQ + "*");
        List<String> seqs = new ArrayList<>();
        keys.stream().forEach(key -> {
            Map<Object, Object> map = redisTemplate.boundHashOps(key).entries();
            //log.info("key = {}, map = {}", key, map);
            //Map<String, Long> ret = new HashMap<>();
            map.forEach((k, v) -> {
                long t = NumberUtils.parseLong((String) v, 0L);
                if (t < timestamp) seqs.add((String) k);
            });
        });
        return seqs;
    }

//    public void getPlugList(List<String> evseNoList) {
//        redisTemplate.opsForValue().multiGet(evseNoList);
//    }


    private <T> T getFromRedis(String key, Class<T> clazz) {
        String strVal = redisTemplate.opsForValue().get(key);
        if (StringUtils.isBlank(strVal)) {
            return null;
        }
        try {
            T result = JsonUtils.fromJson(strVal, clazz);
            return result;
        } catch (Exception e) {
            log.warn("json反序列化失败. error = {}, strVal = {}", e.getMessage(), strVal, e);
            return null;
        }
    }


}
