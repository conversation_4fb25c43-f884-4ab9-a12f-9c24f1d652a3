package com.cdz360.data.cache;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.cus.vo.CusInfoVo;
import com.cdz360.base.model.cus.vo.LoginSaltVo;
import com.cdz360.data.cache.utils.RedisCacheUtils;
import com.cdz360.data.cache.utils.RedisKeyGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class RedisCusReadService {

    @Autowired
    protected StringRedisTemplate redisTemplate;

    /**
     * 获取账号登录的 salt
     */
    public LoginSaltVo getLoginSalt(long topCommId, String username) {
        String key = RedisKeyGenerator.genLoginSaltKey(topCommId, username);
        return RedisCacheUtils.getHashFromRedis(redisTemplate, key, LoginSaltVo.class);
    }

    /**
     * 获取登录账号信息
     */
    public CusInfoVo getUserInfoCache(long topCommId, long cusId) {
        String key = RedisKeyGenerator.genUserInfoKey(topCommId, cusId);
        CusInfoVo cusInfo = RedisCacheUtils.getHashFromRedis(redisTemplate, key, CusInfoVo.class);
        if (cusInfo != null) {
            cusInfo.setClients(CusInfoVo.parseClientIds(cusInfo.getClientIds()));
        }
        return cusInfo;
    }

    /**
     * 未来逐步废弃,使用 getUserInfoCache 替换
     */
    public CusInfoVo getCustomerInfoCache(AppClientType appType, long cusId) {
        String key = RedisKeyGenerator.genCustomerKey(appType, cusId);
        return RedisCacheUtils.getHashFromRedis(redisTemplate, key, CusInfoVo.class);
    }
}
