package com.cdz360.data.cache;

import com.cdz360.base.model.app.vo.AppCfg;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.data.cache.utils.RedisKeyGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class RedisAppReadService {

    @Autowired
    protected StringRedisTemplate redisTemplate;

    public AppCfg getAppCfg(String appId) {
        String key = RedisKeyGenerator.genAppCfgKey();
        Object obj = redisTemplate.opsForHash().get(key, appId);
        if (obj == null) {
            log.warn("APP 配置信息不存在. appId = {}, key = {}", appId, key);
            return null;
        }
        try {
            AppCfg appCfg = JsonUtils.fromJson((String) obj, AppCfg.class);
            return appCfg;
        } catch (Exception e) {
            log.warn("can't de-serialize. obj = {}", obj);
            return null;
        }
    }
}
