package com.cdz360.data.cache;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.data.cache.utils.RedisKeyGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.BoundListOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RedisChargeOrderReadService {

    @Autowired
    protected StringRedisTemplate redisTemplate;

    public String getOrderStopCodeString(String orderNo) {

        if (StringUtils.isBlank(orderNo)) {
            throw new DcArgumentException("参数错误, 订单号不能为空");
        }
        String code = redisTemplate.opsForValue().get(RedisKeyGenerator.genChargerOrderStopCodeKey(orderNo));
        if (StringUtils.isBlank(code)) {
            return null;
        }
        return code;
    }


    public <T> T getOrder(String orderNo, Class<T> clazz) {
        String strVal = redisTemplate.opsForValue().get(RedisKeyGenerator.genChargerOrderInfoKey(orderNo));
        if (StringUtils.isBlank(strVal)) {
            return null;
        }
        try {
            T result = JsonUtils.fromJson(strVal, clazz);
            return result;
        } catch (Exception e) {
            log.warn("json反序列化失败. error = {}, strVal = {}", e.getMessage(), strVal, e);
            return null;
        }
    }

    public Integer getExpectLimitSoc(String orderNo) {
        if (StringUtils.isBlank(orderNo)) {
            throw new DcArgumentException("参数错误, 订单号不能为空");
        }
        String s = redisTemplate.opsForValue().get(RedisKeyGenerator.genExpectLimitSocInfoKey(orderNo));
        if(StringUtils.isBlank(s)) {
            return null;
        }
        try {
            Integer result = Integer.valueOf(s);
            return result;
        } catch (Exception e) {
            log.warn("json反序列化失败. error = {}, strVal = {}", e.getMessage(), s, e);
            return null;
        }
    }

    public String getLimitSocChangeEvent(String orderNo) {
        if (StringUtils.isBlank(orderNo)) {
            throw new DcArgumentException("参数错误, 订单号不能为空");
        }
        String key = RedisKeyGenerator.genLimitSocEventKey(orderNo);
        log.debug("获取soc限制事件到redis. order = {}", orderNo);
        String s = redisTemplate.opsForValue().get(key);
        if(StringUtils.isBlank(s)) {
            return null;
        }
        return s;
    }


    /**
     * 查询订单的剩余缓存时间, 单位分钟
     *
     * @param orderNo 订单号
     * @return 返回剩余的时间, 小于 0 表示已经不缓存
     */
    public long getOrderTtl(String orderNo) {
        String key = RedisKeyGenerator.genChargerOrderInfoKey(orderNo);
        Optional<Long> ret = Optional.of(redisTemplate.getExpire(key, TimeUnit.MINUTES));
        return ret.map(Long::longValue).orElse(-2L);

    }


    public <T> List<T> getChargeOrderDetail(String orderNo, Class<T> clazz) {
        String key = RedisKeyGenerator.genChargerOrderDetailKey(orderNo);
        BoundListOperations ops = redisTemplate.boundListOps(key);
        Long size = ops.size();
        if (size == null) {
            return null;
        }
        List<String> strList = ops.range(0, size);
        List<T> ret = strList.stream().map(str -> str == null ? null : JsonUtils.fromJson(str, clazz))
                .collect(Collectors.toList());
        return ret;
    }
}
