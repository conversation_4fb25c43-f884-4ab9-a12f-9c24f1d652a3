package com.cdz360.data.cache.utils;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.SerializeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class RedisCacheUtils {


    public static <T> T getHashFromRedis(RedisTemplate redisTemplate, String key, Class<T> clazz) {
        Map<Object, Object> map = redisTemplate.opsForHash().entries(key);
        if (map == null || map.isEmpty()) {
            return null;
        }
        Map<String, String> map2 = new HashMap<>();
        map.forEach((k, v) -> map2.put((String) k, (String) v));
        try {
            T result = SerializeUtils.mapToObject(clazz, map2);
            return result;
        } catch (Exception e) {
            log.warn("json反序列化失败. error = {}, strVal = {}", e.getMessage(), JsonUtils.toJsonString(map), e);
            return null;
        }
    }


    public static boolean updateProperty(Object src, Object dest, Map<String, String> map, String name) {
        try {

            String nameX = name.substring(0, 1).toUpperCase() + name.substring(1);
            Method getMethod = src.getClass().getMethod("get" + nameX);
            Method setMethod = src.getClass().getMethod("set" + nameX, String.class);
            String newValue = (String) getMethod.invoke(src);
            String oldValue = (String) getMethod.invoke(dest);
            if (newValue != null && !newValue.equals(oldValue)) {
                setMethod.invoke(dest, newValue);
                map.put(name, newValue);
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }
}
