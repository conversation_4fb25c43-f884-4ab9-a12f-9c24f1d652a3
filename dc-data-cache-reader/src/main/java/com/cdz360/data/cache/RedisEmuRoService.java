package com.cdz360.data.cache;

import com.cdz360.base.model.bi.vo.SamplingMinuteDataVo;
import com.cdz360.base.model.es.vo.BmsRtData;
import com.cdz360.base.model.es.vo.BmsRtInfo;
import com.cdz360.base.model.es.vo.DehRtData;
import com.cdz360.base.model.es.vo.DehRtInfo;
import com.cdz360.base.model.es.vo.EmuRtData;
import com.cdz360.base.model.es.vo.EmuRtInfo;
import com.cdz360.base.model.es.vo.FfsRtData;
import com.cdz360.base.model.es.vo.FfsRtInfo;
import com.cdz360.base.model.es.vo.LiquidRtData;
import com.cdz360.base.model.es.vo.LiquidRtInfo;
import com.cdz360.base.model.es.vo.MeterRtData;
import com.cdz360.base.model.es.vo.MeterRtInfo;
import com.cdz360.base.model.es.vo.PcsRtData;
import com.cdz360.base.model.es.vo.PcsRtInfo;
import com.cdz360.base.model.es.vo.UpsRtData;
import com.cdz360.base.model.es.vo.UpsRtInfo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.data.cache.utils.RedisKeyGenerator;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class RedisEmuRoService {

    @Autowired
    protected StringRedisTemplate redisTemplate;

//    /**
//     * EMU发生DI数据信息
//     */
//    public Map<Integer, Object> getEmuRtDiData(String emuDno, List<Integer> idxList) {
//        String key = RedisKeyGenerator.genEmuRtDiDataKey(emuDno);
//
//        HashMap<Integer, Object> diDataMap = new HashMap<>();
//
//        HashOperations<String, Object, Object> op = redisTemplate.opsForHash();
//        idxList.forEach(idx -> {
//            Object o = op.get(key, idx.toString());
//            diDataMap.put(idx, o);
//        });
//
//        return diDataMap;
//    }

    /**
     * EMU实时设备信息 (不含 PCS、BMS)
     */
    public EmuRtInfo getEmuRtInfo(String emuDno) {
        String key = RedisKeyGenerator.genEmuRtInfoKey(emuDno);
        String json = redisTemplate.opsForValue().get(key);
        if (StringUtils.isBlank(json)) {
            return null;
        }
        return JsonUtils.fromJson(json, EmuRtInfo.class);
    }

    /**
     * EMU实时数据 (不含 PCS、BMS)
     */
    public EmuRtData getEmuRtData(String emuDno) {
        String key = RedisKeyGenerator.genEmuRtDataKey(emuDno);
        String json = redisTemplate.opsForValue().get(key);
        if (StringUtils.isBlank(json)) {
            return null;
        }
        return JsonUtils.fromJson(json, EmuRtData.class);
    }

    /**
     * PCS实时设备信息
     */
    public PcsRtInfo getPcsRtInfo(String pcsDno) {
        String key = RedisKeyGenerator.genPcsRtInfoKey(pcsDno);
        String json = redisTemplate.opsForValue().get(key);
        if (StringUtils.isBlank(json)) {
            return null;
        }
        return JsonUtils.fromJson(json, PcsRtInfo.class);
    }

    /**
     * PCS实时数据
     */
    public PcsRtData getPcsRtData(String pcsDno) {
        String key = RedisKeyGenerator.genPcsRtDataKey(pcsDno);
        String json = redisTemplate.opsForValue().get(key);
        if (StringUtils.isBlank(json)) {
            return null;
        }
        return JsonUtils.fromJson(json, PcsRtData.class);
    }


    /**
     * BMS实时设备信息
     */
    public BmsRtInfo getBmsRtInfo(String bmsDno) {
        String key = RedisKeyGenerator.genBmsRtInfoKey(bmsDno);
        String json = redisTemplate.opsForValue().get(key);
        if (StringUtils.isBlank(json)) {
            return null;
        }
        return JsonUtils.fromJson(json, BmsRtInfo.class);
    }

    /**
     * BMS实时数据
     */
    public BmsRtData getBmsRtData(String bmsDno) {
        String key = RedisKeyGenerator.genBmsRtDataKey(bmsDno);
        String json = redisTemplate.opsForValue().get(key);
        if (StringUtils.isBlank(json)) {
            return null;
        }
        return JsonUtils.fromJson(json, BmsRtData.class);
    }

    /**
     * 液冷 实时设备信息
     */
    public LiquidRtInfo getLiquidRtInfo(String liquidDno) {
        String key = RedisKeyGenerator.genLiquidRtInfoKey(liquidDno);
        String json = redisTemplate.opsForValue().get(key);
        if (StringUtils.isBlank(json)) {
            return null;
        }
        return JsonUtils.fromJson(json, LiquidRtInfo.class);
    }

    /**
     * 液冷 实时数据
     */
    public LiquidRtData getLiquidRtData(String liquidDno) {
        String key = RedisKeyGenerator.genLiquidRtDataKey(liquidDno);
        String json = redisTemplate.opsForValue().get(key);
        if (StringUtils.isBlank(json)) {
            return null;
        }
        return JsonUtils.fromJson(json, LiquidRtData.class);
    }

    /**
     * 除湿器实时设备信息
     */
    public DehRtInfo getDehRtInfo(String dehDno) {
        String key = RedisKeyGenerator.genDehRtInfoKey(dehDno);
        String json = redisTemplate.opsForValue().get(key);
        if (StringUtils.isBlank(json)) {
            return null;
        }
        return JsonUtils.fromJson(json, DehRtInfo.class);
    }

    /**
     * 除湿器实时数据
     */
    public DehRtData getDehRtData(String dehDno) {
        String key = RedisKeyGenerator.genDehRtDataKey(dehDno);
        String json = redisTemplate.opsForValue().get(key);
        if (StringUtils.isBlank(json)) {
            return null;
        }
        return JsonUtils.fromJson(json, DehRtData.class);
    }

    /**
     * 消防系统实时设备信息
     */
    public FfsRtInfo getFfsRtInfo(String ffsDno) {
        String key = RedisKeyGenerator.genFfsRtInfoKey(ffsDno);
        String json = redisTemplate.opsForValue().get(key);
        if (StringUtils.isBlank(json)) {
            return null;
        }
        return JsonUtils.fromJson(json, FfsRtInfo.class);
    }

    /**
     * 消防系统实时数据
     */
    public FfsRtData getFfsRtData(String ffsDno) {
        String key = RedisKeyGenerator.genFfsRtDataKey(ffsDno);
        String json = redisTemplate.opsForValue().get(key);
        if (StringUtils.isBlank(json)) {
            return null;
        }
        return JsonUtils.fromJson(json, FfsRtData.class);
    }

    /**
     * 电表实时设备信息
     */
    public MeterRtInfo getMeterRtInfo(String meterDno) {
        String key = RedisKeyGenerator.genMeterRtInfoKey(meterDno);
        String json = redisTemplate.opsForValue().get(key);
        if (StringUtils.isBlank(json)) {
            return null;
        }
        return JsonUtils.fromJson(json, MeterRtInfo.class);
    }

    /**
     * 电表实时数据
     */
    public MeterRtData getMeterRtData(String meterDno) {
        String key = RedisKeyGenerator.genMeterRtDataKey(meterDno);
        String json = redisTemplate.opsForValue().get(key);
        if (StringUtils.isBlank(json)) {
            return null;
        }
        return JsonUtils.fromJson(json, MeterRtData.class);
    }

    /**
     * UPS实时设备信息
     */
    public UpsRtInfo getUpsRtInfo(String upsDno) {
        String key = RedisKeyGenerator.genUpsRtInfoKey(upsDno);
        String json = redisTemplate.opsForValue().get(key);
        if (StringUtils.isBlank(json)) {
            return null;
        }
        return JsonUtils.fromJson(json, UpsRtInfo.class);
    }

    /**
     * UPS实时数据
     */
    public UpsRtData getUpsRtData(String upsDno) {
        String key = RedisKeyGenerator.genUpsRtDataKey(upsDno);
        String json = redisTemplate.opsForValue().get(key);
        if (StringUtils.isBlank(json)) {
            return null;
        }
        return JsonUtils.fromJson(json, UpsRtData.class);
    }

    /**
     * 获取BMS的 日内 数据曲线，v1=SOC
     */
    public List<SamplingMinuteDataVo> getBmsInDaySamplingData(String bmsDno, LocalDate date) {
        String key = RedisKeyGenerator.genBmsInDaySamplingDataKey(bmsDno, date);
        Long size = redisTemplate.opsForList().size(key);
        if (size == null || size < 1L) {
            return List.of();
        }
        List<String> strListValues = redisTemplate.opsForList().range(key, 0, size);
        if (CollectionUtils.isEmpty(strListValues)) {
            return List.of();
        } else {
            return strListValues.stream()
                .map(str -> JsonUtils.fromJson(str, SamplingMinuteDataVo.class))
                .collect(Collectors.toList());
        }
    }

    /**
     * 获取电表的 日内 数据曲线
     * <p>v1=有功功率</p>
     * <p>v2=无功功率</p>
     * <p>v2=功率因数</p>
     */
    public List<SamplingMinuteDataVo> getMeterInDaySamplingData(String meterDno, LocalDate date) {
        String key = RedisKeyGenerator.genMeterInDaySamplingDataKey(meterDno, date);
        Long size = redisTemplate.opsForList().size(key);
        if (size == null || size < 1L) {
            return List.of();
        }
        List<String> strListValues = redisTemplate.opsForList().range(key, 0, size);
        if (CollectionUtils.isEmpty(strListValues)) {
            return List.of();
        } else {
            return strListValues.stream()
                .map(str -> JsonUtils.fromJson(str, SamplingMinuteDataVo.class))
                .collect(Collectors.toList());
        }
    }

    /**
     * 获取PCS的日内数据曲线, v1=交流侧功率, v2=交流侧电压, v3=交流侧电流
     */
    public List<SamplingMinuteDataVo> getPcsInDaySamplingData(String pcsDno, LocalDate date) {
        String key = RedisKeyGenerator.genPcsInDaySamplingDataKey(pcsDno, date);
        Long size = redisTemplate.opsForList().size(key);
        if (size == null || size < 1L) {
            return List.of();
        }
        List<String> strListValues = redisTemplate.opsForList().range(key, 0, size);
        if (CollectionUtils.isEmpty(strListValues)) {
            return List.of();
        } else {
            return strListValues.stream()
                .map(str -> JsonUtils.fromJson(str, SamplingMinuteDataVo.class))
                .collect(Collectors.toList());
        }
    }
}
