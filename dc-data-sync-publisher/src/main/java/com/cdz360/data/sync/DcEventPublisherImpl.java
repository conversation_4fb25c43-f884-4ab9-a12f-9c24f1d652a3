package com.cdz360.data.sync;


import com.cdz360.base.model.base.type.IotEvent;
import com.cdz360.base.model.charge.vo.ChargeOrderVo;
import com.cdz360.base.model.corp.dto.CorpOrgSyncDto;
import com.cdz360.base.model.corp.dto.CorpSyncDto;
import com.cdz360.base.model.corp.dto.CorpUserSyncDto;
import com.cdz360.base.model.cus.dto.CusSyncDto;
import com.cdz360.base.model.es.dto.EssAlarmNotify;
import com.cdz360.base.model.es.dto.EssAlarms;
import com.cdz360.base.model.es.vo.EmuRtData;
import com.cdz360.base.model.iot.dto.PlugMqDto;
import com.cdz360.base.model.iot.vo.EssVo;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.model.iot.vo.PvGtiVo;
import com.cdz360.base.model.iot.vo.SiteCtrlVo;
import com.cdz360.data.sync.constant.DcMqConstants;
import com.cdz360.data.sync.event.ChargeOrderEvent;
import com.cdz360.data.sync.event.EssAlarmEvent;
import com.cdz360.data.sync.event.EssAlarmEvent2;
import com.cdz360.data.sync.event.EssInfoEvent;
import com.cdz360.data.sync.event.EssRtDataEvent;
import com.cdz360.data.sync.event.EvseInfoEvent;
import com.cdz360.data.sync.event.IotCtrlInfoEvent;
import com.cdz360.data.sync.event.IotGwCmdTimeoutEvent;
import com.cdz360.data.sync.event.IotGwDownCmdEvent;
import com.cdz360.data.sync.event.IotGwUpCmdEvent;
import com.cdz360.data.sync.event.PlugInfoEvent;
import com.cdz360.data.sync.event.PushAliLiteEvent;
import com.cdz360.data.sync.event.PushAndroidAppMsgEvent;
import com.cdz360.data.sync.event.PushCorpWxMsgEvent;
import com.cdz360.data.sync.event.PushIosAppMsgEvent;
import com.cdz360.data.sync.event.PushSmsEvent;
import com.cdz360.data.sync.event.PushWxLiteEvent;
import com.cdz360.data.sync.event.PvGtiInfoEvent;
import com.cdz360.data.sync.event.SimInfoEvent;
import com.cdz360.data.sync.event.SrsInfoEvent;
import com.cdz360.data.sync.event.SyncCommercialEvent;
import com.cdz360.data.sync.event.SyncCorpInfoEvent;
import com.cdz360.data.sync.event.SyncCorpOrgInfoEvent;
import com.cdz360.data.sync.event.SyncCorpUserInfoEvent;
import com.cdz360.data.sync.event.SyncCusInfoEvent;
import com.cdz360.data.sync.event.SyncSiteEvent;
import com.cdz360.data.sync.event.SyncSiteGroupEvent;
import com.cdz360.data.sync.model.DzCommercial;
import com.cdz360.data.sync.model.Site;
import com.cdz360.data.sync.model.SiteGroup;
import com.cdz360.data.sync.model.iot.IotGwCmdTimeout;
import com.cdz360.data.sync.model.iot.IotGwDownCmd;
import com.cdz360.data.sync.model.iot.IotGwUpCmd;
import com.cdz360.data.sync.service.DcEventPublisher;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class DcEventPublisherImpl implements DcEventPublisher {

    @Autowired
    private AmqpTemplate rabbitTemplate;


    /**
     * 发送同步场站信息的mq消息
     *
     * @param siteInfo 场站信息
     */
    @Override
    public void publishSiteInfo(Site siteInfo) {
        SyncSiteEvent event = new SyncSiteEvent(siteInfo);
        String msg = event.toString();
        log.info(" event = {}", msg);
        rabbitTemplate.convertAndSend(DcMqConstants.MQ_EXCHANGE_NAME_INFRASTRUCTURE,
            DcMqConstants.MQ_ROUTING_KEY_SITE, msg);
    }

    /**
     * 发送同步场站组信息的mq消息
     *
     * @param siteGroup 场站组信息
     */
    @Override
    public void publishSiteGroupInfo(SiteGroup siteGroup) {
        SyncSiteGroupEvent event = new SyncSiteGroupEvent(siteGroup);
        String msg = event.toString();
        log.info("sync site-group event = {}", msg);
        rabbitTemplate.convertAndSend(DcMqConstants.MQ_EXCHANGE_NAME_INFRASTRUCTURE,
            DcMqConstants.MQ_ROUTING_KEY_SITE_GROUP, msg);
    }

    /**
     * 发送同步商户信息的mq消息
     *
     * @param commercial 商户信息
     */
    @Override
    public void publishDzCommercialInfo(DzCommercial commercial) {
        SyncCommercialEvent event = new SyncCommercialEvent(commercial);
        String msg = event.toString();
        log.info(" event = {}", msg);
        rabbitTemplate.convertAndSend(DcMqConstants.MQ_EXCHANGE_NAME_INFRASTRUCTURE,
            DcMqConstants.MQ_ROUTING_KEY_COMMERCIAL, msg);
    }

    /**
     * 发送同步客户信息的mq消息
     *
     * @param cusInfo 客户信息
     */
    public void publishCusInfo(CusSyncDto cusInfo) {
        SyncCusInfoEvent event = new SyncCusInfoEvent(cusInfo);
        String msg = event.toString();
        log.info(" event = {}", msg);
        rabbitTemplate.convertAndSend(DcMqConstants.MQ_EXCHANGE_NAME_SYNC,
            DcMqConstants.MQ_ROUTING_KEY_SYNC, msg);
    }


    /**
     * 发送同步企业信息的mq消息
     *
     * @param corpInfo 企业信息
     */
    public void publishCorpInfo(CorpSyncDto corpInfo) {
        SyncCorpInfoEvent event = new SyncCorpInfoEvent(corpInfo);
        String msg = event.toString();
        log.info(" event = {}", msg);
        rabbitTemplate.convertAndSend(DcMqConstants.MQ_EXCHANGE_NAME_SYNC,
            DcMqConstants.MQ_ROUTING_KEY_SYNC, msg);
    }


    /**
     * 发送同步企业充电用户信息的mq消息
     *
     * @param corpUserInfo 企业充电用户信息
     */
    public void publishCorpUserInfo(CorpUserSyncDto corpUserInfo) {
        SyncCorpUserInfoEvent event = new SyncCorpUserInfoEvent(corpUserInfo);
        String msg = event.toString();
        log.info(" event = {}", msg);
        rabbitTemplate.convertAndSend(DcMqConstants.MQ_EXCHANGE_NAME_SYNC,
            DcMqConstants.MQ_ROUTING_KEY_SYNC, msg);
    }


    /**
     * 发送同步企业组织信息的mq消息
     *
     * @param corpOrgInfo 企业组织信息
     */
    public void publishCorpOrgInfo(CorpOrgSyncDto corpOrgInfo) {
        SyncCorpOrgInfoEvent event = new SyncCorpOrgInfoEvent(corpOrgInfo);
        String msg = event.toString();
        log.info(" event = {}", msg);
        rabbitTemplate.convertAndSend(DcMqConstants.MQ_EXCHANGE_NAME_SYNC,
            DcMqConstants.MQ_ROUTING_KEY_SYNC, msg);
    }

    /**
     * 发送同步枪头信息的mq消息
     *
     * @param plug 枪头信息
     */
    @Override
    public void publishPlugInfo(IotEvent eventType, PlugMqDto plug) {
        PlugInfoEvent event = new PlugInfoEvent(eventType, plug);
        String msg = event.toString();
        log.info(" event = {}", msg);
//        rabbitTemplate.convertAndSend(DcMqConstants.MQ_EXCHANGE_NAME_IOT,
//                DcMqConstants.MQ_ROUTING_KEY_PLUG, msg);
        rabbitTemplate.convertAndSend(DcMqConstants.MQ_EXCHANGE_NAME_IOT,
            DcMqConstants.MQ_ROUTING_KEY_IOT, msg);

    }

    /**
     * 发送同步枪头信息的mq消息
     *
     * @param plug 枪头信息
     */
    @Override
    public void publishPlugInfo(IotEvent eventType, PlugMqDto plug, String linkId, String ctrlNo) {
        PlugInfoEvent event = new PlugInfoEvent(eventType, plug);
        event.setLinkId(linkId);
        event.setCtrlNo(ctrlNo);
        String msg = event.toString();
        log.info(" event = {}", msg);
//        rabbitTemplate.convertAndSend(DcMqConstants.MQ_EXCHANGE_NAME_IOT,
//                DcMqConstants.MQ_ROUTING_KEY_PLUG, msg);
        rabbitTemplate.convertAndSend(DcMqConstants.MQ_EXCHANGE_NAME_IOT,
            DcMqConstants.MQ_ROUTING_KEY_IOT, msg);

    }

    /**
     * 发送同步桩信息的mq消息
     *
     * @param evse 桩信息
     */
    @Override
    public void publishEvseInfo(IotEvent eventType, EvseVo evse) {
        EvseInfoEvent event = new EvseInfoEvent(eventType, evse);
        String msg = event.toString();
        log.info(" event = {}", msg);
//        rabbitTemplate.convertAndSend(DcMqConstants.MQ_EXCHANGE_NAME_IOT,
//                DcMqConstants.MQ_ROUTING_KEY_EVSE, msg);
        rabbitTemplate.convertAndSend(DcMqConstants.MQ_EXCHANGE_NAME_IOT,
            DcMqConstants.MQ_ROUTING_KEY_IOT, msg);

    }

    /**
     * 发送同步桩信息的mq消息
     *
     * @param evse 桩信息
     */
    @Override
    public void publishEvseInfo(IotEvent eventType, EvseVo evse, String linkId, String ctrlNo) {
        EvseInfoEvent event = new EvseInfoEvent(eventType, evse);
        event.setLinkId(linkId);
        event.setCtrlNo(ctrlNo);
        String msg = event.toString();
        log.info(" event = {}", msg);
//        rabbitTemplate.convertAndSend(DcMqConstants.MQ_EXCHANGE_NAME_IOT,
//                DcMqConstants.MQ_ROUTING_KEY_EVSE, msg);
        rabbitTemplate.convertAndSend(DcMqConstants.MQ_EXCHANGE_NAME_IOT,
            DcMqConstants.MQ_ROUTING_KEY_IOT, msg);

    }

    /**
     * 发送同步场站控制器信息
     *
     * @param ctrl 控制器
     */
    @Override
    public void publishSiteCtrlInfo(IotEvent eventType, SiteCtrlVo ctrl) {
        IotCtrlInfoEvent event = new IotCtrlInfoEvent(eventType, ctrl);
        event.setLinkId(ctrl.getLinkId());
        String msg = event.toString();
        log.info(" event = {}", msg);
        rabbitTemplate.convertAndSend(DcMqConstants.MQ_EXCHANGE_NAME_IOT,
            DcMqConstants.MQ_ROUTING_KEY_IOT, msg);

    }

    @Override
    public void publishPvGtiInfo(IotEvent eventType, PvGtiVo gti) {
        PvGtiInfoEvent event = new PvGtiInfoEvent(eventType, gti);
        String msg = event.toString();
        log.info(" event = {}", msg);
        rabbitTemplate.convertAndSend(DcMqConstants.MQ_EXCHANGE_NAME_IOT,
            DcMqConstants.MQ_ROUTING_KEY_IOT, msg);
    }

    @Override
    public void publishSrsInfo(IotEvent eventType, PvGtiVo srs) {
        SrsInfoEvent event = new SrsInfoEvent(eventType, srs);
        String msg = event.toString();
        log.info(" event = {}", msg);
        rabbitTemplate.convertAndSend(DcMqConstants.MQ_EXCHANGE_NAME_IOT,
            DcMqConstants.MQ_ROUTING_KEY_IOT, msg);
    }

    @Override
    public void publishSimInfo(IotEvent eventType, PvGtiVo sim) {
        SimInfoEvent event = new SimInfoEvent(eventType, sim);
        String msg = event.toString();
        log.info(" event = {}", msg);
        rabbitTemplate.convertAndSend(DcMqConstants.MQ_EXCHANGE_NAME_IOT,
            DcMqConstants.MQ_ROUTING_KEY_IOT, msg);
    }

    /**
     * 户用储能ESS状态信息
     *
     * @param notify 告警信息
     * @deprecated 应使用 EssEventPublisher里的publishEssAlarm
     */
    @Deprecated
    @Override
    public void publishEssAlarm(EssAlarmNotify notify) {
        EssAlarmEvent event = new EssAlarmEvent(IotEvent.STATE_CHANGE, notify);
        String msg = event.toString();
        log.info(" event = {}", msg);
        rabbitTemplate.convertAndSend(DcMqConstants.MQ_EXCHANGE_NAME_IOT,
            DcMqConstants.MQ_ROUTING_KEY_IOT, msg);
    }

    @Override
    public void publishEssAlarm(EssAlarms alarms) {
        EssAlarmEvent2 event = new EssAlarmEvent2(IotEvent.STATE_CHANGE, alarms);
        String msg = event.toString();
        log.info(" event = {}", msg);
        rabbitTemplate.convertAndSend(DcMqConstants.MQ_EXCHANGE_NAME_IOT,
            DcMqConstants.MQ_ROUTING_KEY_IOT, msg);
    }

    @Override
    public void publishEssRtData(EmuRtData rtData) {
        EssRtDataEvent event = new EssRtDataEvent(IotEvent.RT_DATA_CHANGE, rtData);
        String msg = event.toString();
        log.info(" event = {}", msg);
        rabbitTemplate.convertAndSend(DcMqConstants.MQ_EXCHANGE_NAME_IOT,
            DcMqConstants.MQ_ROUTING_KEY_IOT, msg);
    }

    @Override
    public void publishEssInfo(IotEvent eventType, EssVo ess) {
        EssInfoEvent event = new EssInfoEvent(eventType, ess);
        String msg = event.toString();
        log.info(" event = {}", msg);
        rabbitTemplate.convertAndSend(DcMqConstants.MQ_EXCHANGE_NAME_IOT,
            DcMqConstants.MQ_ROUTING_KEY_IOT, msg);
    }

    /**
     * 网关上行指令
     *
     * @param cmd 指令
     */
    @Override
    public void publishIotGwUpCmd(IotGwUpCmd cmd) {
        IotGwUpCmdEvent event = new IotGwUpCmdEvent(cmd);
        String msg = event.toString();
        log.info(" event = {}", msg);
        rabbitTemplate.convertAndSend(DcMqConstants.MQ_EXCHANGE_NAME_IOT_CMD,
            DcMqConstants.MQ_ROUTING_KEY_GW_CMD_UP, msg);
    }

    /**
     * 网关下行指令
     *
     * @param cmd 指令
     */
    @Override
    public void publishIotGwDownCmd(IotGwDownCmd cmd) {
        IotGwDownCmdEvent event = new IotGwDownCmdEvent(cmd);
        String msg = event.toString();
        log.info(" event = {}", msg);
        rabbitTemplate.convertAndSend(DcMqConstants.MQ_EXCHANGE_NAME_IOT_CMD,
            DcMqConstants.MQ_ROUTING_KEY_GW_CMD_DOWN, msg);

    }

    @Override
    public void publishIotGwCmdTimeout(IotGwCmdTimeout cmd) {
        IotGwCmdTimeoutEvent event = new IotGwCmdTimeoutEvent(cmd);
        String msg = event.toString();
        log.info(" event = {}", msg);
        rabbitTemplate.convertAndSend(DcMqConstants.MQ_EXCHANGE_NAME_IOT_CMD,
            DcMqConstants.MQ_ROUTING_KEY_GW_CMD_TIMEOUT, msg);
    }

    /**
     * 发送充电订单的mq消息
     *
     * @param chargeOrder 充电订单
     */
    @Override
    public void publishChargeOrder(ChargeOrderVo chargeOrder) {
        ChargeOrderEvent event = new ChargeOrderEvent(chargeOrder);
        String msg = event.toString();
        log.info(" event = {}", msg);
        rabbitTemplate.convertAndSend(DcMqConstants.MQ_EXCHANGE_NAME_CHARGE_ORDER,
            DcMqConstants.MQ_ROUTING_KEY_CHARGE_ORDER, msg);

    }


    /**
     * 发送网关下行指令
     *
     * @param gwno 网关编号
     * @param msg  发送给网关的下行指令
     */
    @Override
    public void publishIotGwCmd(String gwno, String msg) {
        log.info("发送网关指令到 MQ. gwno: {}, msg: {}", gwno, msg);
        rabbitTemplate.convertAndSend(DcMqConstants.MQ_EXCHANGE_NAME_IOT_GW,
            gwno, msg);
    }

    /**
     * 发送短信的推送消息
     *
     * @param msg 短信消息
     */
    @Override
    public void publishSmsMsg(String msg) {
        PushSmsEvent event = new PushSmsEvent(msg);
        String mqMsg = event.toString();
        log.info("发送短信消息到 MQ. msg: {}", mqMsg);
        rabbitTemplate.convertAndSend(DcMqConstants.MQ_EXCHANGE_NAME_PUSH_MSG,
            DcMqConstants.MQ_ROUTING_KEY_PUSH_MSG, mqMsg);
    }

    /**
     * 发送微信小程序模板消息
     *
     * @param msg 小程序消息
     */
    @Override
    public void publishWxLiteMsg(String msg) {
        PushWxLiteEvent event = new PushWxLiteEvent(msg);
        String mqMsg = event.toString();
        log.info("发送微信小程序模板消息到 MQ. msg: {}", mqMsg);
        rabbitTemplate.convertAndSend(DcMqConstants.MQ_EXCHANGE_NAME_PUSH_MSG,
            DcMqConstants.MQ_ROUTING_KEY_PUSH_MSG, mqMsg);
    }

    /**
     * 发送支付宝小程序推送消息
     *
     * @param msg 支付宝小程序消息
     */
    @Override
    public void publishAliLiteMsg(String msg) {
        PushAliLiteEvent event = new PushAliLiteEvent(msg);
        String mqMsg = event.toString();
        log.info("发送支付宝小程序消息到 MQ. msg: {}", mqMsg);
        rabbitTemplate.convertAndSend(DcMqConstants.MQ_EXCHANGE_NAME_PUSH_MSG,
            DcMqConstants.MQ_ROUTING_KEY_PUSH_MSG, mqMsg);
    }


    @Override
    public void publishIosAppMsg(String msg) {
        PushIosAppMsgEvent event = new PushIosAppMsgEvent(msg);
        String mqMsg = event.toString();
        log.info("发送iOS APP推送消息到 MQ. msg: {}", mqMsg);
        rabbitTemplate.convertAndSend(DcMqConstants.MQ_EXCHANGE_NAME_PUSH_MSG,
            DcMqConstants.MQ_ROUTING_KEY_PUSH_MSG, mqMsg);
    }

    @Override
    public void publishAndroidAppMsg(String msg) {
        PushAndroidAppMsgEvent event = new PushAndroidAppMsgEvent(msg);
        String mqMsg = event.toString();
        log.info("发送安卓APP推送消息到 MQ. msg: {}", mqMsg);
        rabbitTemplate.convertAndSend(DcMqConstants.MQ_EXCHANGE_NAME_PUSH_MSG,
            DcMqConstants.MQ_ROUTING_KEY_PUSH_MSG, mqMsg);
    }

    @Override
    public void publishCorpWxMsg(String msg) {
        PushCorpWxMsgEvent event = new PushCorpWxMsgEvent(msg);
        String mqMsg = event.toString();
        log.info("发送企业微信推送消息到 MQ. msg: {}", mqMsg);
        rabbitTemplate.convertAndSend(DcMqConstants.MQ_EXCHANGE_NAME_PUSH_MSG,
            DcMqConstants.MQ_ROUTING_KEY_PUSH_MSG, mqMsg);
    }


    @Override
    public void publishSysUserLog(String msg) {
        rabbitTemplate.convertAndSend(DcMqConstants.MQ_EXCHANGE_NAME_SYS_USER_LOG,
            null, msg);
    }
}
