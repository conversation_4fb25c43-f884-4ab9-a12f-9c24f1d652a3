app:
  name: data-sync-test

server:
  address: 0.0.0.0
  port: 8080
  use-forward-headers: true
  compression.enabled: true

eureka:
  instance:
    hostname: localhost
  client:
    registerWithEureka: false
    fetchRegistry: true
    healthcheck:
      enabled: true
    serviceUrl:
      defaultZone: http://aaa:<EMAIL>/eureka/,http://aaa:<EMAIL>/eureka/


management:
  context-path: /admin
  security:
    enabled: false



logging:
  level:
    org.springframework: 'INFO'
    com.cdz360.data: 'DEBUG'
    org.springframework.amqp: 'DEBUG'
    
