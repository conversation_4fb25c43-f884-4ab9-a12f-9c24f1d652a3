package com.cdz360.data.sync.publisher;

import com.cdz360.base.model.base.type.ChargeOrderStatus;
import com.cdz360.base.model.base.type.IotEvent;
import com.cdz360.base.model.base.type.IotGwCmdType2;
import com.cdz360.base.model.charge.vo.ChargeOrderVo;
import com.cdz360.base.model.iot.dto.PlugMqDto;
import com.cdz360.base.model.iot.vo.*;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.data.sync.DataSyncTestBase;
import com.cdz360.data.sync.model.DzCommercial;
import com.cdz360.data.sync.model.Site;
import com.cdz360.data.sync.model.iot.IotGwCmdTimeout;
import com.cdz360.data.sync.model.iot.IotGwDownCmd;
import com.cdz360.data.sync.model.iot.IotGwUpCmd;
import com.cdz360.data.sync.service.DcEventPublisher;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class TestDcEventPublisher extends DataSyncTestBase {
    public final Logger log = LoggerFactory.getLogger(TestDcEventPublisher.class);

    @Autowired
    private DcEventPublisher dcEventPublisher;

    @Test
    public void test_publishSiteInfo() {
        log.info(">>");
        Site siteInfo = new Site();
        siteInfo.setName("UT-SITE-NAME")
                .setSiteId("UT-SITE-ID");

        this.dcEventPublisher.publishSiteInfo(siteInfo);
        log.info("<<");
    }

    @Test
    public void test_publishCommercialInfo() {
        log.info(">>");
        DzCommercial commercial = new DzCommercial();
        commercial.setId(1L).setCommName("UT-COMMERCIAL-NAME")
                .setMerchants("012345");

        this.dcEventPublisher.publishDzCommercialInfo(commercial);
        log.info("<<");
    }


    @Test
    public void test_publishPlugInfo() {
        log.info(">>");
        PlugMqDto plug = new PlugMqDto();
        plug.setSiteId("1910225011262762503").setName("ut-abcd1234");

        this.dcEventPublisher.publishPlugInfo(IotEvent.STATE_CHANGE, plug);
        this.dcEventPublisher.publishPlugInfo(IotEvent.STATE_CHANGE, plug, UUID.randomUUID().toString(), "12312");
        log.info("<<");
    }

    @Test
    public void test_publishEvseInfo() {
        log.info(">>");
        EvseVo evse = new EvseVo();
        evse.setName("ut-abcd1234");

        this.dcEventPublisher.publishEvseInfo(IotEvent.CREATE, evse);
        this.dcEventPublisher.publishEvseInfo(IotEvent.CREATE, evse, UUID.randomUUID().toString(), "12312");
        log.info("<<");
    }

    @Test
    public void test_publishCtrlInfo() {
        log.info(">>");
        SiteCtrlVo ctrl = new SiteCtrlVo();
        ctrl.setCtrlNo("ctrl-123213");

        this.dcEventPublisher.publishSiteCtrlInfo(IotEvent.STATE_CHANGE, ctrl);
        log.info("<<");
    }

    @Test
    public void test_publishIotGwUpCmd() {
        log.info(">>");
        IotGwUpCmd cmd = new IotGwUpCmd();
        cmd.setCmd(IotGwCmdType2.CE_CHARGE_START);

        this.dcEventPublisher.publishIotGwUpCmd(cmd);
        log.info("<<");

    }

    @Test
    public void test_publishIotGwDownCmd() {
        log.info(">>");
        IotGwDownCmd cmd = new IotGwDownCmd();
        cmd.setTtl(5).setMsg("ut-alge;wjlg;ejkwh;lehuoipwejhelwjhelwhj")
                .setSeq(UUID.randomUUID().toString())
                .setGwno("ut-abcd1234")
                .setVer(2)
                .setCmd(IotGwCmdType2.CE_CHARGE_START);

        this.dcEventPublisher.publishIotGwDownCmd(cmd);
        log.info("<<");

    }

    @Test
    public void test_publishChargeOrder() {
        log.info(">>");
        //for (int i = 0; i < 2; i++) {
        ChargeOrderVo chargeOrder = new ChargeOrderVo();
        chargeOrder.setSiteId("1910225011262762503")
                .setStatus(ChargeOrderStatus.START)
                .setOrderNo("aaabbbcccdddeeefff")
                .setSiteCommId(34474L)
                .setElecFee(BigDecimal.valueOf(12.34))
                .setServFee(BigDecimal.valueOf(34.56));
        this.dcEventPublisher.publishChargeOrder(chargeOrder);
        // }
        log.info("<<");
    }


    @Test
    public void test_publishIotGwCmdTimeout() {
        log.info(">>");
        IotGwCmdTimeout cmd = new IotGwCmdTimeout();
        cmd.setTtl(5).setSeq(UUID.randomUUID().toString()).setCmd(IotGwCmdType2.CE_CHARGE_START);

        this.dcEventPublisher.publishIotGwCmdTimeout(cmd);
        log.info("<<");

    }

    @Test
    public void test_publishIotGwCmd_startOrder() {
        log.info(">>");
        //String gwno = "alpha-dev";
        //String gwno = "GWNO1911050007CA";
        String gwno = "GWNO191217000005";
        Map<String, Object> msg = new HashMap<>();
        msg.put("n", gwno);
        msg.put("v", 2);
        msg.put("cmd", "CHARGE_START");
        msg.put("seq", UUID.randomUUID().toString());

        Map<String, Object> data = new HashMap<>();
        data.put("evseNo", "************");
        data.put("plugId", 1);
        data.put("orderNo", "************");
        data.put("startType", 0x32);
        data.put("accountNo", "123456");
        data.put("totalAmount", 12.34);
        data.put("frozenAmount", 3.45);
        Map<String, Object> stopMode = new HashMap<>();
        data.put("stopMode", stopMode);
        stopMode.put("type", "FULL");

        msg.put("data", data);
        //String msg = "aglekjwljgewlkgjelwkjgkelwjg;lewkjgew";
        this.dcEventPublisher.publishIotGwCmd(gwno, JsonUtils.toJsonString(msg));
    }


    @Test
    public void test_publishIotGwCmd_stopOrder() {
        log.info(">>");
        //String gwno = "alpha-dev";
        //String gwno = "GWNO1911050007CA";
        String gwno = "GWNO191217000005";
        Map<String, Object> msg = new HashMap<>();
        msg.put("n", gwno);
        msg.put("v", 2);
        msg.put("cmd", "CHARGE_STOP");
        msg.put("seq", UUID.randomUUID().toString());

        Map<String, Object> data = new HashMap<>();
        data.put("evseNo", "************");
        data.put("plugId", 1);
        data.put("orderNo", "************");
//        data.put("startType", 0x32);
//        data.put("accountNo", "123456");
//        data.put("totalAmount", 12.34);
//        data.put("frozenAmount", 3.45);
//        Map<String, Object> stopMode = new HashMap<>();
//        data.put("stopMode", stopMode);
//        stopMode.put("type", "FULL");
//
        msg.put("data", data);
        //String msg = "aglekjwljgewlkgjelwkjgkelwjg;lewkjgew";
        this.dcEventPublisher.publishIotGwCmd(gwno, JsonUtils.toJsonString(msg));
    }


    @Test
    public void test_publishSmsMsg() {
        String msg = "agewgewgew";
        this.dcEventPublisher.publishSmsMsg(msg);
    }


    @Test
    public void test_publishSysUserLog() {
        String msg = "gewgewgewgewegwwe";
        this.dcEventPublisher.publishSysUserLog(msg);
    }
}
