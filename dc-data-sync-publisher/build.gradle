
plugins {
    id 'java'
}

jar{
    enabled = true
}


dependencies {

    implementation project(':dc-base-model')
    implementation project(':dc-base-utils')
    implementation project(':dc-data-sync')


    implementation('org.springframework.boot:spring-boot-starter-amqp')


    testRuntimeOnly('org.springframework.cloud:spring-cloud-starter-config')


    implementation("com.fasterxml.jackson.core:jackson-annotations:${jacksonVersion}")

    testImplementation("com.alibaba.mq-amqp:mq-amqp-client:1.0.3")
    testImplementation("org.junit.jupiter:junit-jupiter:${junitVersion}")
}